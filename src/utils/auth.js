import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const UserRoleList = 'User-Role-List'
// const setUserId = 'user_id'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

// export function setBindUser(bind_user) {
//   return Cookies.set(setUserId, bind_user)
// }

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function setRole(roleList) {
  return Cookies.set(UserRoleList, roleList)
}

// export function setUser_id() {
//   return Cookies.set(USER_ID, USER_ID)
// }

export function getRole() {
  return Cookies.get(UserRoleList)
}

export function removeRole() {
  return Cookies.remove(UserRoleList)
}
