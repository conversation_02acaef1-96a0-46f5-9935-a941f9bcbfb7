let webSocket = null;
let socketOpen = false;

// 发送消息
export const doSend = (message) => {
    if (socketOpen) {
        webSocket.send(message)
    }
}
export function sendId() {
    const timeStamp = (new Date()).valueOf()
    const randId = randomNumber(100000, 999999)
    return timeStamp + '-' + randId
}
export function randomNumber(minNum, maxNum) {
    switch (arguments.length) {
        case 1:
            return parseInt(Math.random() * minNum + 1, 10)
        case 2:
            return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
        default:
            return 0
    }
}
// 初始化websocket
export const contactSocket = () => {
    if ("WebSocket" in window) {
        const openid = localStorage.getItem('openid')

        webSocket = new WebSocket("wss://socket.camkoon.com/im");
        webSocket.onopen = function () {
            webSocket.send('{"seq":"' + sendId() + '","cmd":"login","data":{"userId":"' + openid + '","appId":"web"}}')
            setInterval(() => {
                webSocket.send('{"seq":"' + sendId() + '","cmd":"heartbeat","data":{}}')
            }, 5000)
            socketOpen = true
        };
        webSocket.onmessage = function (evt) {
            var received_msg = evt.data;
        };
        webSocket.onclose = function () {
            webSocket.send('{"seq":"' + sendId() + '","cmd":"login","data":{"userId":"' + openid + '","appId":"web"}}')
            setInterval(() => {
                webSocket.send('{"seq":"' + sendId() + '","cmd":"heartbeat","data":{}}')
            }, 5000)
            socketOpen = true
        };
        webSocket.onerror = function () {
            webSocket.send('{"seq":"' + sendId() + '","cmd":"login","data":{"userId":"' + openid + '","appId":"web"}}')
            setInterval(() => {
                webSocket.send('{"seq":"' + sendId() + '","cmd":"heartbeat","data":{}}')
            }, 5000)
            socketOpen = true
        };
    }
}
