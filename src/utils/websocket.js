export function contactSocket() {

  const webSocket = new WebSocket('wss://socket.camkoon.com/im')
  const openid = localStorage.getItem('openid')
  webSocket.onopen = function() {
    // console.log(webSocket.readyState)
    if (webSocket.readyState === 1) {
      webSocket.send('{"seq":"' + sendId() + '","cmd":"login","data":{"userId":"' + openid + '","appId":"web"}}')
      setInterval(() => {
        webSocket.send('{"seq":"' + sendId() + '","cmd":"heartbeat","data":{}}')
      }, 1000000)
    }
  }
  return webSocket
}

export function sendId() {
  const timeStamp = (new Date()).valueOf()
  const randId = randomNumber(100000, 999999)
  return timeStamp + '-' + randId
}
export function randomNumber(minNum, maxNum) {
  switch (arguments.length) {
    case 1:
      return parseInt(Math.random() * minNum + 1, 10)
    case 2:
      return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
    default:
      return 0
  }
}

