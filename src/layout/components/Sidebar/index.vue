<template>
  <div :class="{'has-logo':showLogo}">
    <logo v-if="showLogo" :collapse="isCollapse"/>


    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :unique-opened="false"
          :active-text-color="variables.menuActiveText"
          :collapse-transition="false"
          mode="vertical"
      >
        <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path"/>
      </el-menu>
    </el-scrollbar>

<!--    <el-tabs :tab-position="tabPosition" style="height:100%">-->
<!--      <el-tab-pane label="菜单">-->
<!--        <el-scrollbar wrap-class="scrollbar-wrapper">-->
<!--          <el-menu-->
<!--            :default-active="activeMenu"-->
<!--            :collapse="isCollapse"-->
<!--            :background-color="variables.menuBg"-->
<!--            :text-color="variables.menuText"-->
<!--            :unique-opened="false"-->
<!--            :active-text-color="variables.menuActiveText"-->
<!--            :collapse-transition="false"-->
<!--            mode="vertical"-->
<!--          >-->
<!--            <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path"/>-->
<!--          </el-menu>-->
<!--        </el-scrollbar>-->
<!--      </el-tab-pane>-->
<!--      <el-tab-pane label="消息">-->
<!--        <el-scrollbar wrap-class="scrollbar-wrapper">-->
<!--          <el-menu-->
<!--            :default-active="activeMenu"-->
<!--            :collapse="isCollapse"-->
<!--            :background-color="variables.menuBg"-->
<!--            :text-color="variables.menuText"-->
<!--            :unique-opened="false"-->
<!--            :active-text-color="variables.menuActiveText"-->
<!--            :collapse-transition="false"-->
<!--            mode="vertical"-->
<!--          >-->
<!--            <sidebar-item v-for="route in im_Routes" :key="route.path" :item="route" :base-path="route.path"/>-->
<!--          </el-menu>-->
<!--        </el-scrollbar>-->
<!--      </el-tab-pane>-->

<!--      <el-tab-pane label="文件">-->
<!--        <el-scrollbar wrap-class="scrollbar-wrapper">-->
<!--          <el-menu-->
<!--            :default-active="activeMenu"-->
<!--             :collapse="isCollapse"-->
<!--             :background-color="variables.menuBg"-->
<!--             :text-color="variables.menuText"-->
<!--             :unique-opened="false"-->
<!--             :active-text-color="variables.menuActiveText"-->
<!--             :collapse-transition="false"-->
<!--             mode="vertical"-->
<!--          >-->
<!--            <sidebar-item v-for="route in file_routes" :key="route.path" :item="route" :base-path="route.path"/>-->
<!--          </el-menu>-->
<!--        </el-scrollbar>-->
<!--      </el-tab-pane>-->
    </el-tabs>


  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      tabPosition: 'left'
    }
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'file_routes',
      'im_Routes',
      'sidebar'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>




