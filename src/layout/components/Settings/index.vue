<template>
  <ul v-infinite-scroll="load" class="infinite-list" style="overflow:auto">
    <li v-for="(item, index) in listData.data" :key="index" class="infinite-list-item">
      <i class="el-icon-delete" style="color: red" @click="handleDele(item)" />
      <span class="time">{{ item.time }}</span>
      <div class="main">
        <div class="header">
          <img src="@/assets/images/logo.png" alt="">
          <span class="title-content">{{ item.name }}</span>
        </div>
        <div class="wrap">
          <div class="other">
            <label class="label">发送人:</label>
            <span class="title-content">{{ item.form.user_username }}</span>
          </div>
          <div class="other">
            <label class="label">接收人:</label>
            <span class="title-content">{{ item.receive.user_username }}</span>
          </div>
          <div class="other">
            <label class="label">内容:</label>
            <span class="title-content">{{ item.content }}</span>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>

<script>
import { getMsgRemindList, delMsgOfSelf } from '@/api/dashboard'
export default {
  data() {
    return {
      listQuery: {
        page: 1,
        page_size: 10
      },
      listData: {
        data: [],
        pages: 0
      },
      sendOut: '',
      objList: ''
    }
  },
  watch: {
  },
  mounted() {
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      await getMsgRemindList(this.listQuery).then((res) => {
        if (this.listQuery.page > 1) {
          this.listData.data = this.listData.data.concat(res.data.data)
          this.listData.pages = res.data.pages
        } else {
          this.listData.data = res.data.data
          this.listData.pages = res.data.pages
        }
      })
      // const that = this
      // this.$socketPublic.onmessage = function(evt) {
      //   const received_msg = evt.data
      //   if (JSON.parse(received_msg).cmd === 'msg') {
      //     const added = JSON.parse(received_msg).response.data
      //     const objList = JSON.parse(added.msg)
      //     that.listData.data.unshift(objList)
      //   }
      // }
    },
    load() {
      if (this.listQuery.page <= this.listData.pages) {
        this.listQuery.page++
        this.getList()
      } else {
      }
    },
    // 删除消息
    handleDele(item) {
      this.listQuery.page = 1
      delMsgOfSelf({ id: item.id }).then(() => {
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.time {
  position: absolute;
  top: -24px;
  left: calc(50% - 30px);
  font-size: 12px;
  color: #ccc;
  text-align: center;
}
.infinite-list-item {
  background: #fff;
  box-shadow: #dadcf3 0 0 10px;
  margin: 1vh auto;
  border-radius: 5px;
  font-size: 13px;
  color: black;
  list-style-type: none;
  position: relative;
  .el-icon-delete {
    position: absolute;
    top: 20px;
    right: 12px;
  }
  .main {
    width: 100%;
    padding: 10px;
    margin-bottom: 30px;
    .header {
      border-bottom: 1px solid #f1f1f1;
      padding: 10px 5px;
      display: flex;
      img {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: block;
      }
      .title {
        font-weight: bold;
      }
      .title-content {
        margin-left: 10px;
        font-weight: bold;
      }
    }
    .wrap {
      margin-top: 10px;
      .other {
        padding: 5px;
        .title-content {
          margin-left: 20px;
        }
      }
    }
  }
}
.infinite-list {
  width: 100%;
  height: 100vh;
  overflow: auto;
  padding: 15px;
  font-size: 14px;
  line-height: 1.5;
}
.drawer-container {
  width: 100%;
  height: 100vh;
  overflow: auto;
  padding: 15px;
  font-size: 14px;
  line-height: 1.5;
  background-color: rgb(244,246,251);
  border: 1px solid #f1f1f1;
  .drawer-title {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, .85);
    font-size: 14px;
    line-height: 22px;
  }

  .drawer-item {
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    padding: 12px 0;
  }

  .drawer-switch {
    float: right
  }

  .job-link{
    display: block;
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
  }
}
</style>
