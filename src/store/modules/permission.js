import { asyncRoutes, constantRoutes } from '@/router'
import { getRole } from '@/utils/auth'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [], addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, roles) {
    return new Promise(resolve => {
      let accessedRoutes
      if (roles.includes('admin')) {
        accessedRoutes = asyncRoutes || []
      } else {
        accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      }

      // 动态路由 移除不在权限范围之内的路由
      const roleList = getRole()
      const menu = []

      // 获取我有权限的菜单
      accessedRoutes.forEach(function(item, key) {
        const flag = roleList.search(item.name)
        // 处理一级菜单
        if (flag !== -1) {
          menu.push(item)
          if (item.children && item.children.length > 0) {
            const menuResult = getMenuChildren(item.children)
            let index = menu.length - 1
            if (index < 0) {
              index = 0
            }
            menu[index]['children'] = menuResult
          }
        }
      })
      commit('SET_ROUTES', menu)
      resolve(menu)
    })
  }
}

function getMenuChildren(data) {
  const roleList = getRole()
  const children = []
  data.forEach(function(value, key) {
    const flags = roleList.search(value.name)
    if (flags > -1) {
      children.push(value)
    }
  })
  return children
}

export default {
  namespaced: true, state, mutations, actions
}
