const state = {
  nodeMap: new Map(),
  isEdit: null,
  selectedNode: {},
  selectFormItem: null,
  design: {}
}

const mutations = {
  selectedNode: (state, val) => {
    state.selectedNode = val
  },
  loadForm: (state, val) => {
    state.design = val
  },
  setIsEdit: (state, val) => {
    state.isEdit = val
  }
}

const actions = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
