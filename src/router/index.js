import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import workAttendanceRouter from './modules/workAttendance'
import financial from './modules/financial'
import project from './modules/project'
import system from '@/router/modules/system'
import notice from '@/router/modules/notice'

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
  path: '/redirect', component: Layout, hidden: true, children: [{
    path: '/redirect/:path(.*)', component: () => import('@/views/permission/redirect/index')
  }]
}, {
  path: '/login', component: () => import('@/views/system/login/index'), hidden: true
}, {
  path: '/auth-redirect', component: () => import('@/views/system/login/auth-redirect'), hidden: true
}, {
  path: '/404', component: () => import('@/views/system/error-page/404'), hidden: true
}, {
  path: '/401', component: () => import('@/views/system/error-page/401'), hidden: true
}, {
  path: '/', component: Layout, redirect: '/dashboard', children: [{
    path: 'dashboard',
    component: () => import('@/views/dashboard/index'),
    name: 'Dashboard',
    meta: { title: 'dashboard', icon: 'dashboard', affix: true }
  }]
}]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [{
  path: '/permission', component: Layout, redirect: '/permission', alwaysShow: true, // will always show the root menu
  name: 'permission', meta: {
    title: '组织架构', icon: 'tree'
  }, children: [{
    path: 'administrators',
    name: 'administrators',
    component: () => import('@/views/permission/administrators/index'),
    meta: { title: '用户管理', icon: 'user' }
  }, {
    path: 'user-management',
    name: 'user-management',
    component: () => import('@/views/permission/userManagement/index'),
    meta: { title: '员工管理', icon: 'peoples' }
  }, {
    path: 'role', name: 'role', component: () => import('@/views/permission/role/index'), meta: {
      title: '角色管理', icon: 'role'
    }
  }, {
    path: 'dept', name: 'dept', component: () => import('@/views/permission/dept/index'), meta: {
      title: '部门管理', icon: 'tree-table'
    }
  }, {
    path: 'post', name: 'post', component: () => import('@/views/permission/post/index'), meta: {
      title: '岗位管理', icon: 'post'
    }
  }, {
    path: 'menu', name: 'menu', component: () => import('@/views/permission/menu/index'), meta: {
      title: '菜单管理', icon: 'tree'
    }
  }]
},

  /** when your routing map is too long, you can split it into small modules **/
project,  workAttendanceRouter,  financial, notice,system,  {
    path: '*', redirect: '/404', hidden: true
  }]

const createRouter = () => new Router({
  scrollBehavior: () => ({ y: 0 }), routes: constantRoutes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
