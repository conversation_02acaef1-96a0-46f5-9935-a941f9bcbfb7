/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const portrait = {
  path: '/portrait', component: Layout, redirect: '/portrait/basics', name: 'portrait', meta: {
    title: '员工画像', icon: 'el-icon-postcard'
  }, children: [{
    path: 'labelSet',
    name: 'labelSet',
    component: () => import('@/views/smartLabel/labelSet/index'),
    meta: { title: '标签管理' }
  }, {
    path: 'basics', name: 'basics', component: () => import('@/views/portrait/basics/index'), meta: { title: '基础分析' }
  }, {
    path: 'label', name: 'label', component: () => import('@/views/portrait/label/index'), meta: { title: '标签分析' }
  }, {
    path: 'trend', name: 'trend', component: () => import('@/views/portrait/trend/index'), meta: { title: '趋势分析' }
  }, {
    path: 'visit', name: 'visit', component: () => import('@/views/portrait/visit/index'), meta: { title: '访问分析' }
  }, {
    path: 'language', name: 'language', component: () => import('@/views/portrait/language/index'), meta: { title: '语言包' }
  }, {
    path: 'movement', name: 'movement', component: () => import('@/views/portrait/movement/index'), meta: { title: '动作' }
  }]
}

export default portrait
