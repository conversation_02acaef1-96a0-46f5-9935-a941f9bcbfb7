/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const workAttendanceRouter = {
  path: '/workAttendance', component: Layout, redirect: '/workAttendance/record', name: 'workAttendance', meta: {
    title: '考勤管理', icon: 'workAttendance'
  }, children: [{
    path: 'record',
    name: 'record',
    component: () => import('@/views/workAttendance/record/index'),
    meta: { title: '考勤记录' }
  }, {
    path: 'examine',
    name: 'examine',
    component: () => import('@/views/workAttendance/examine/index'),
    meta: { title: '请假审核' }
  }, {
    path: 'statistics',
    name: 'statistics',
    component: () => import('@/views/workAttendance/statistics/index'),
    meta: { title: '考勤统计' }
  }, {
    path: 'setting',
    name: 'setting',
    component: () => import('@/views/workAttendance/setting/index'),
    meta: { title: '考勤设置' }
  }, {
    path: 'report', name: 'report', component: () => import('@/views/report/index'), meta: { title: '工作汇报' }
  }]
}

export default workAttendanceRouter
