import Layout from '@/layout'

const workflow = {
  path: '/workflow', component: Layout, redirect: '/workflow/workflowDesign', name: 'system', meta: {
    title: '工作流程', icon: 'el-icon-share'
  }, children: [{
    path: '/formsPanel',
    name: 'formsPanel',
    component: () => import('@/views/workflow/workflowDesign/admin/FormsPanel.vue'),
    meta: { title: '流程设计' }
  }, {
    path: '/WorkSpace',
    name: 'WorkSpace',
    component: () => import('@/views/workflow/workflowDesign/workspace/WorkSpace.vue'),
    meta: { title: '流程发起' }
  }, {
    path: '/WorkManage',
    name: 'WorkManage',
    component: () => import('@/views/workflow/workflowDesign/workspace/WorkManage.vue'),
    meta: { title: '流程任务' }
  }, {
    path: '/admin/design',
    name: 'design',
    hidden: true,
    component: () => import('@/views/workflow/workflowDesign/admin/FormProcessDesign.vue'),
    meta: { title: '流程设计' }
  }, {
    path: '/admin/initProcess',
    name: 'initProcess',
    hidden: true,
    component: () => import('@/views/workflow/workflowDesign/workspace/InitiateProcess.vue'),
    meta: { title: '流程审批' }
  }]
}

export default workflow
