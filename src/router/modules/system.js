/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const system = {
  path: '/system', component: Layout, name: 'system', meta: {
    title: '系统设置', icon: 'el-icon-setting'
  }, children: [{
    path: 'system', name: 'system', component: () => import('@/views/permission/log/index'), meta: {
      title: '日志管理', icon: 'documentation'
    }
  }, {
    path: 'dictionary', name: 'dictionary', component: () => import('@/views/permission/dictionary/index'), meta: {
      title: '系统字典', icon: 'dict'
    }
  }, {
    path: '/profile', component: () => import('@/views/system/profile'), hidden: true, meta: {
      title: '个人信息'
    }
  }]
}

export default system

