/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const smartLabel = {
  path: '/smartLabel', component: Layout, redirect: '/smartLabel/labelSet', name: 'smartLabel', meta: {
    title: '智能标签库', icon: 'el-icon-postcard'
  }, children: [{
    path: 'labelSet',
    name: 'labelSet',
    component: () => import('@/views/smartLabel/labelSet/index'),
    meta: { title: '标签管理' }
  }, {
    path: 'mergeLabel',
    name: 'mergeLabel',
    component: () => import('@/views/smartLabel/mergeLabel/index'),
    meta: { title: '标签生成' }
  }]
}

export default smartLabel
