/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const financial = {
  path: '/financial', component: Layout, name: 'financial', meta: {
    title: '财务管理', icon: 'money'
  }, children: [{
    path: 'customer',
    name: 'customer',
    component: () => import('@/views/financial/customer/index'),
    meta: { title: '客户管理' }
  }, {
    path: 'informal',
    name: 'informal',
    component: () => import('@/views/financial/informal/index'),
    meta: { title: '财务审核' }
  }, {
    path: 'contract',
    name: 'contract',
    component: () => import('@/views/financial/contract/index'),
    meta: { title: '合同管理' }
  }, {
    path: 'invoice',
    name: 'invoice',
    component: () => import('@/views/financial/invoice/index'),
    meta: { title: '发票管理' }
  }, {
    path: 'informal-category',
    name: 'informal-category',
    component: () => import('@/views/financial/informal-category/index'),
    meta: { title: '财务设置' }
  }]
}

export default financial

