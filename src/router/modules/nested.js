/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const nestedRouter = {
  path: '/project',
  component: Layout,
  redirect: '/project/project',
  name: 'project',
  meta: {
    title: '项目管理',
    icon: 'component'
  },
  children: [
    {
      path: 'project',
      name: 'project',
      component: () => import('@/views/project/project/index'),
      meta: { title: '项目管理' }
    },
    {
      path: 'projectType',
      name: 'projectType',
      component: () => import('@/views/project/projectType/index'),
      meta: { title: '项目类型' }
    }, {
      path: 'projectreview',
      name: 'projectreview',
      component: () => import('@/views/project/projectreview/index'),
      meta: { title: '项目归档' }
    }, {
      path: 'issue',
      name: 'issue',
      component: () => import('@/views/project/issue/index'),
      meta: { title: '复盘问题' }
    },
    {
      path: 'task',
      name: 'task',
      component: () => import('@/views/project/task/index'),
      meta: { title: '任务管理' }
    },{
      path: 'oncetask',
      name: 'oncetask',
      component: () => import('@/views/project/oncetask/index'),
      meta: { title: '任务视图' }
    },
    {
      path: 'taskType',
      name: 'taskType',
      component: () => import('@/views/project/taskType/index'),
      meta: { title: '任务类型' }
    }
  ]
}

export default nestedRouter
