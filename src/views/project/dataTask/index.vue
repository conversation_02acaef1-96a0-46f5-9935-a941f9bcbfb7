<template>
  <div class="app-container">
    <el-row>
      <el-menu default-active="0" class="el-menu-demo" mode="horizontal">
        <el-menu-item v-for="(item ,index) in  deptOptions" :index="index.toString()" @click="changeDept(item.id)">
          {{ item.department_name }}
        </el-menu-item>
      </el-menu>
      <div class="line"></div>
    </el-row>

    <el-row class="search_box" :gutter="24">
      <el-col :span="3">
        <el-select v-model="searchDate.pro_managers" placeholder="用户名称" clearable class="filter-item"
                   @visible-change="getCompanyUsers" @change="handleFilter"
        >
          <el-option v-for="(item, i) in projectManager" :key="i" :label="item.user_username" :value="item.id"/>
        </el-select>
      </el-col>

      <el-col :span="3">
        <el-input v-model="searchDate.pro_name" placeholder="项目名称" clearable class="filter-item"
                  @keyup.enter.native="handleFilter"
        />
      </el-col>

      <!-- 添加间距 -->
      <el-col :span="5">
        <el-date-picker v-model="searchDate.search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                        type="daterange" clearable range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="handleFilter"
        />
      </el-col>

      <!-- 添加间距 -->
      <el-col :span="5">
        <el-button class="filter-item" type="primary" icon="el-icon-download" @click="handleDownload">
          导出
        </el-button>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-table :data="list.data" height="850"
                  ref="myTable"
                  border
                  :header-cell-style="{'text-align':'center'}"
                  :cell-style="{'text-align':'center','vertical-align':'top'}"
                  style="width: 100%"
        >
          <el-table-column prop="name" fixed label="姓名" width="90">
            <template slot-scope="{row}">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="year" align="center">
            <el-table-column v-for="(item, index) in list.header" :key="index" :label="item.date" align="center">
              <el-table-column :label="item.week" align="center">
                <template slot-scope="{row}">
                  <div v-for="(value) in row.list">
                    <div v-if="item.date == it.date" v-for="(it,key) in value">
                      <div v-if="it.is_leave != false">
                        <el-tag type="info">{{ it.is_leave }}</el-tag>
                      </div>
                      <div v-else-if="it.task == ''"></div>
                      <div v-else>
                        <div style="cursor: pointer;"
                             @click="taskAudit(it)"
                        >
                          <div class="point"><b>【{{ it.task.project_name }}】</b></div>
                          <div class="retract">{{ it.task.task_name }}</div>
                          <div style="display: flex;color: red;">
                            <span v-if="it.task.is_end == 1"><el-tag type="warning">待完成</el-tag></span>
                            <div v-else>
                              <span v-if="it.task.task_score == 0"><el-tag type="danger">待审核</el-tag> </span>
                              <div v-else>
                                <el-tag>评分:{{ it.task.task_score }}</el-tag>
                              </div>
                            </div>
                            <span style="margin-left: 5px;">
                            <el-tag>审核人:{{ it.task.check_user_name }}</el-tag>
                            </span>
                          </div>
                        </div>
                        <el-divider v-if="key+1 < value.length "></el-divider>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-drawer title="任务详情" :visible.sync="drawer" size="50%">
      <div class="audit">
        <template>
          <el-descriptions :column="2" size="medium" border>
            <el-descriptions-item>
              <template slot="label">
                项目名称
              </template>
              {{ task_data.project_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                任务类型
              </template>
              {{ task_data.task_category }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                任务级别
              </template>
              {{ task_data.task_level_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                执行人
              </template>
              {{ task_data.receive_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                审核人
              </template>
              {{ task_data.check_user_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                开始时间
              </template>
              {{ task_data.start_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                结束时间
              </template>
              {{ task_data.end_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                任务内容
              </template>
              {{ task_data.task_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                完成状态
              </template>
              <div v-if="task_data.is_end == 2">
                完成
              </div>
              <div v-if="task_data.is_end !== 2">
                未完成
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                审核状态
              </template>
              {{ task_data.status }}
            </el-descriptions-item>
          </el-descriptions>
          <el-divider></el-divider>
        </template>


        <div style="margin: 20px;"></div>
        <el-form label-position="left" label-width="60px">
          <el-form-item label="评分">
            <el-rate style="line-height: 50px;" allow-half v-model="task_data.task_score"/>
          </el-form-item>

          <el-form-item label="评语">
            <el-input v-model="task_data.opinion" type="textarea" :rows="3" placeholder="请填写你的意见"/>
          </el-form-item>

        </el-form>
        <el-row style="text-align: right" v-if="task_score == 0 && is_end == 2">
          <el-button type="primary" @click="handleSubmitReview" plain>提交评分</el-button>
          <el-button type="success" @click="reset()" plain>重置评分</el-button>
        </el-row>


      </div>
    </el-drawer>
  </div>

</template>

<script>
import {getDeptList} from '@/api/system/dept'
import {dateTask, evaluation, getCompanyUsers} from '@/api/porject/project'

export default {
  data() {
    return {
      projectManager: [],
      deptOptions: {},
      task_score: 0,
      is_end: 0,
      opinion: '',
      searchDate: {
        search_time: null
      },
      list: [],
      task_data: {},
      drawer: false,
      year: ''
    }
  },
  created() {
    // 获取部门
    this.getDeptList()
    // 初始化数据
    this.initData()
    // 获取任务数据
    this.getDataList()
  },
  methods: {
    // 初始化数据
    initData() {
      const startTime = this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 4 * 24 * 3600 * 1000)
      const endTime = this.formatDate(+new Date())
      this.searchDate.search_time = [startTime, endTime]
      this.searchDate.dept_id = 2
    },
    // 获取部门
    getDeptList() {
      getDeptList().then(response => {
        this.deptOptions = response.data[0].child
      })
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options = response.data
        this.userListData = response.data
        this.projectManager = response.data
      })
    },
    changeDept(id) {
      this.searchDate.dept_id = id
      this.getDataList()
    },
    // 获取任务数据
    getDataList() {
      const searchDate = {search_time: this.searchDate.search_time[0] + '——' + this.searchDate.search_time[1]}
      searchDate.dept_id = this.searchDate.dept_id
      searchDate.pro_managers = this.searchDate.pro_managers
      searchDate.pro_name = this.searchDate.pro_name

      dateTask(searchDate).then(response => {
        this.list = response.data
        this.list.header.forEach((iem) => {
          this.year = iem.date.slice(0, 7)
          this.year = this.year.replace(/-/g, '年')
          this.year = this.year + '月'
        })
      })
    },
    //最近一周
    handleFilter(e) {
      const searchDate = {search_time: this.searchDate.search_time[0] + '——' + this.searchDate.search_time[1]}
      searchDate.dept_id = this.searchDate.dept_id
      searchDate.pro_managers = this.searchDate.pro_managers
      searchDate.pro_name = this.searchDate.pro_name

      dateTask(searchDate).then(response => {
        this.list = response.data
        this.$nextTick(() => {
          if (this.$refs.myTable && this.$refs.myTable.doLayout) {
            this.$refs.myTable.doLayout();
          }
        })
        this.list.header.forEach((item) => {
          this.year = item.date.slice(0, 7)
          this.year = this.year.replace(/-/g, '年')
          this.year = this.year + '月'
        })
      })
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    taskAudit(e) {
      this.task_data = e.task
      this.task_score = e.task.task_score
      this.is_end = e.task.is_end
      this.drawer = true
    },
    handleSubmitReview() {
      evaluation({
        task_id: this.task_data.id,
        task_score: this.task_data.task_score,
        opinion: this.task_data.opinion,
        status: 1
      }).then(() => {
        this.getDataList()
        this.opinion = ''
        this.task_score = 0
      })
    },
    reset() {
      this.task_data.opinion = ''
      this.task_data.task_score = 0
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async excel => {
        let header_time = []
        let explode_data = []

        const searchDate = {search_time: this.searchDate.search_time[0] + '——' + this.searchDate.search_time[1]}
        searchDate.dept_id = this.searchDate.dept_id
        searchDate.pro_managers = this.searchDate.pro_managers
        searchDate.pro_name = this.searchDate.pro_name

        await dateTask(searchDate).then((response) => {
          header_time = response.data.header
          explode_data = response.data.data
        })

        // 处理表头
        const tHeader = ['姓名']
        header_time.forEach((item) => {
          tHeader.push(item.date + '|' + item.week)
        })

        // 处理数据
        let exportData = {}
        explode_data.forEach((v, k) => {
          exportData[k] = [{}]
          exportData[k][0] = v.name

          v.list.forEach((item) => {
            let task = ''
            if (item[0].is_leave) {
              task = '请假'
            } else {
              item.forEach((taskValue) => {
                let content = '';
                if (taskValue.task != '') {
                  let isEnd = '未完成'
                  if (taskValue.task.is_end === 2) {
                    isEnd = '完成'
                  }
                  content = '【' + taskValue.task.project_name + '】' + '\n'
                    + taskValue.task.task_name
                    + '\n\r完成状态:'
                    + isEnd

                  if (taskValue.task.status === "已审核") {
                    content += '\n\r审核评分:'
                      + taskValue.task.task_score
                  } else {
                    content += '\n\r审核状态:待审核'
                  }

                  if (taskValue.task.opinion !== "") {
                    content += '\n\r评语:' + taskValue.task.opinion
                  }

                  content += '\n\r审核人:' + taskValue.task.check_user_name

                  content += "\n\r ---------------------------------\n\r"

                } else {
                  content = ''
                }
                task += content;
              })
            }
            exportData[k].push(task)
          })
        })

        const data = Object.values(exportData)
        // 处理数据
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '任务看板',
          autoWidth: 200
        })
        this.downloadLoading = false
      })
    }
  }
}
</script>

<style>

.point {
  white-space: nowrap;
  text-align: left;
}

.search_box {
  margin: 20px 0;
}

.audit {
  padding: 20px;
}

.retract {
  padding: 5px 15px;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.retract:hover {
  white-space: normal;
}

.el-divider {
  margin: 10px 0 !important;
}

.el-drawer__header {
  margin-bottom: 0;
}

</style>
