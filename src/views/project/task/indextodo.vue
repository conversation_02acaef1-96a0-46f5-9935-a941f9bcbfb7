<template>
  <div class="app-container">
    <el-row>
      <el-menu default-active="0" class="el-menu-demo" mode="horizontal">
        <el-menu-item v-for="(item ,index) in  deptOptions"
                      :index="index.toString()"
                      @click="changeDept(item.id)"
        >
          {{ item.department_name }}
        </el-menu-item>
      </el-menu>
      <div class="line"></div>
    </el-row>
    <el-container class="search_box">
      <!--左侧项目列表-->
<!--      <el-aside class="pro_aside">-->
<!--        <el-header class="pro_header">-->

<!--          <el-input v-model="proListQuery.pro_name" placeholder="项目名称搜索" clearable class="inputPro"-->
<!--                    @input="getProject"-->
<!--                    style="width: 170px;"-->
<!--          />-->

<!--        </el-header>-->


<!--        <el-menu :default-active="selectPro" class="el-menu-vertical-demo" active-text-color="#46a6ff">-->
<!--          <div v-for="(item,index) in proDate">-->
<!--            <el-menu-item :index="index.toString()">-->
<!--              <div-->
<!--                class="overflow-text"-->
<!--                @click="getProTasks(item)"-->
<!--                :style="{ color: item.status === '进行中' ? 'inherit' : 'gray' }"-->
<!--              >-->
<!--                <span>{{ item.pro_name }}   </span>-->

<!--              </div>-->
<!--            </el-menu-item>-->
<!--          </div>-->
<!--        </el-menu>-->
<!--      </el-aside>-->
      <!--搜索列表-->
      <el-main>
        <el-row :gutter="24">
          <el-tabs @tab-click="changeNode">
            <el-tab-pane :key="ix" v-for="(item, ix) in takList" :label=item.name :name="item.index">
              <el-row :gutter="17">
                <el-col :span="4">
                  <el-select v-model="listQuery.receive_id" placeholder="执行人" clearable class="filter-item"
                             filterable
                             @visible-change="getCompanyUsers" @change="handleFilter"
                  >
                    <el-option v-for="(item, i) in companyUsers" :key="i" :label="item.user_username" :value="item.id"/>
                  </el-select>
                </el-col>

                <el-col :span="4">
                  <el-input placeholder="任务名称" v-model="listQuery.task_name"
                            clearable
                            class="filter-item"
                  />
                </el-col>

                <el-col :span="6">
                  <el-date-picker v-model="listQuery.search_time" unlink-panels value-format="yyyy-MM-dd"
                                  format="yyyy-MM-dd"
                                  type="daterange" clearable range-separator="至" start-placeholder="开始日期"
                                  end-placeholder="结束日期"
                                  @change="handleFilter"
                  />
                </el-col>


                <el-col :span="2">
                  <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
                    搜索
                  </el-button>
                </el-col>

                <el-col :span="2">
                  <el-button type="primary" class="filter-item" icon="el-icon-download"
                             @click="centerDialogVisible = true"
                  >导出
                  </el-button>
                </el-col>

                <el-col :span="2">
                  <el-button type="primary" @click="addPushTask(item)" v-if="item.status === '进行中'">
                    <i class="el-icon-edit"></i>
                    新增
                  </el-button>
                </el-col>

              </el-row>
              <!--empty-->
              <el-empty style="margin-top: 15%;" v-if=" item.data.length === 0"
                        description="该节点暂无任务，快来创建吧!"></el-empty>
              <!--table-->
              <div style="margin-top: 10px;overflow: auto">
                <el-table :rules="rules"
                          v-if="item.data && item.data.length > 0"
                          :data="item.data"
                          row-key="id"
                          height="600"
                          border
                          fit highlight-current-row default-expand-all
                          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                >
                  <el-table-column label="发布人" width="85px" align="center" fixed="left">
                    <template slot-scope="{row}">
                      <div>
                        <span v-if="!row.id">{{ userName }}</span>
                        <span v-else>{{ row.user_name }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="执行人" width="120px" align="center" fixed="left">
                    <template slot-scope="{row}">
                      <div v-if="item.status =='进行中'">
                        <el-select v-model="row.receive_id" :multiple="multiple" filterable placeholder="请选择"
                                   @change="addRowTaskFun(row, 'receive_id')" @focus="selectRowEdit(row)"
                        >
                          <el-option v-for="item in proDateUsers" :key="item.id" :label="item.user_username"
                                     :value="item.id"
                          />
                        </el-select>
                      </div>
                      <div v-else>
                        {{ row.receive_name }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="审核人" width="120px" align="center" fixed="left">
                    <template slot-scope="{row}">
                      <div v-if="item.status =='进行中'">
                        <el-select v-model="row.check_user_id"  filterable placeholder="请选择审核人"
                                   @change="addRowTaskFun(row, 'check_user_id')" @focus="selectRowEdit(row)"
                        >
                          <el-option v-for="item in companyUsers" :key="item.id" :label="item.user_username"
                                     :value="item.id"
                          />
                        </el-select>
                      </div>
                      <div v-else>
                        {{ row.check_user_name }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="任务名称"  width="400px" align="left" fixed="left">
                    <template slot-scope="{row}">
                      <div v-if="item.status == '进行中'">
                        <el-input v-model="row.task_name" type="textarea"
                                  :autosize="{minRows:1,maxRows:4}"
                                  placeholder="任务名称"
                                  @input="addRowTaskFun(row, 'task_name')" @focus="selectRowEdit(row)"
                        />
                      </div>
                      <div v-else>
                        {{ row.task_name }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="任务类型" width="100px" align="center">
                    <template slot-scope="{row}">
                      <div v-if="item.status == '进行中'">
                        <el-select v-model="row.task_category_id" placeholder="类型" clearable
                                   @change="addRowTaskFun(row,'task_category')"
                                   @focus="selectRowEdit(row)"
                                   class="filter-item"
                        >
                          <el-option v-for="(item) in taskCategory" :key="item.id" :label="item.task_name"
                                     :value="item.id"
                          />
                        </el-select>
                      </div>
                      <div v-else>
                        {{ row.task_category }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="任务级别" width="80px" align="center">
                    <template slot-scope="{row}">
                      <div v-if="item.status == '进行中'">
                        <el-select v-model="row.task_level" placeholder="选择任务等级" clearable
                                   style="width:60px"
                                   class="filter-item" @change="addRowTaskFun(row,'task_level')"
                                   @focus="selectRowEdit(row)"
                        >
                          <el-option v-for="item in taskLevel" :key="item.id" :label="item.label" :value="item.id"/>
                        </el-select>
                      </div>
                      <div v-else>
                        {{ row.task_level_name }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="开始时间" width="160px" align="center">
                    <template slot-scope="{row}">
                      <div v-if="item.status == '进行中'">
                        <el-date-picker v-model="row.start_time" style="width:140px" type="date" placeholder="选择日期"
                                        @focus="selectRowEdit(row)"
                                        @change="addRowTaskFun(row,'start_time')"
                        >
                        </el-date-picker>
                      </div>
                      <div v-else>
                        {{ row.start_time }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="结束时间" width="160px" align="center">
                    <template slot-scope="{row}">
                      <div v-if="item.status == '进行中'">
                        <el-date-picker v-model="row.end_time" style="width:140px" type="date" placeholder="选择日期"
                                        @focus="selectRowEdit(row)"
                                        @change="addRowTaskFun(row,'end_time')"
                        >
                        </el-date-picker>
                      </div>
                      <div v-else>
                        {{ row.end_time }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="完成时间" width="140px" align="center">
                    <template slot-scope="{row}">
                      <span>{{ row.complete_time }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="评分" width="60px" align="center">
                    <template slot-scope="{row}">
                      <span>{{ row.task_score }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="任务状态" align="left" width="220px" class-name="status-col">
                    <template slot-scope="{row}">

                      <!--  完成状态-->
                      <el-tag v-if="row.is_end == '1'"  size="small"
                              type="danger"
                      >
                        待完成
                      </el-tag>
                      <el-tag v-if="row.is_end == '2'"  size="small"
                              type="success"
                      >
                        已完成
                      </el-tag>

                      <!--  审核状态-->
                      <el-tag v-if="row.status == '待审核'"  size="small"
                              type="danger"
                      >
                        {{ row.status }}
                      </el-tag>
                      <el-tag v-if="row.status == '已审核'"  size="small"
                              type="success"
                      >
                        {{ row.status }}
                      </el-tag>
                      <!--  延期状态-->
                      <el-tag v-if="row.is_beyond" style="margin: 4px;display: inline-block" size="small"
                              type="danger"
                      >
                        {{ row.is_beyond }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column width="140" :label="$t('table.actions')" align="left" fixed="right">
                    <template slot-scope="{row,$index}">
                      <el-button type="warning" size="mini" v-if="row.level != '2'" @click="splitTask(row,$index)">
                        拆分
                      </el-button>
                      <el-button size="mini" type="danger" @click="handleDelete(row,$index)">
                        删除
                      </el-button>

                      <el-button type="success" class="check"
                                 v-if="row.user_id == userId  && row.task_score == 0 && row.is_end == 2"
                                 size="mini" @click="taskAudit(row)"
                      >
                        审核
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-row>
      </el-main>
    </el-container>
    <el-drawer title="任务详情" :visible.sync="drawer" size="50%">
      <div class="audit">
        <template>
          <el-descriptions :column="2" size="medium" border>
            <el-descriptions-item>
              <template slot="label">
                项目名称
              </template>
              {{ task_data.project_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                任务类型
              </template>
              {{ task_data.task_category }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                任务级别
              </template>
              {{ task_data.task_level_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                执行人
              </template>
              {{ task_data.receive_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                审核人
              </template>
              {{ task_data.user_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                开始时间
              </template>
              {{ task_data.start_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                结束时间
              </template>
              {{ task_data.end_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                任务内容
              </template>
              {{ task_data.task_name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                完成状态
              </template>
              <div v-if="task_data.is_end == 2">
                完成
              </div>
              <div v-if="task_data.is_end !== 2">
                未完成
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                审核状态
              </template>
              {{ task_data.status }}
            </el-descriptions-item>
          </el-descriptions>
          <el-divider></el-divider>
        </template>

        <div style="margin: 20px;"></div>


        <el-form label-position="left" label-width="60px">
          <el-form-item label="评分">
            <el-rate style="line-height: 50px;" allow-half v-model="check_data.task_score"/>
          </el-form-item>

          <el-form-item label="评语">
            <el-input v-model="check_data.opinion" type="textarea" :rows="3" placeholder="请填写你的意见"/>
          </el-form-item>

        </el-form>
        <el-row style="text-align: right" v-if="task_data.task_score == 0 && task_data.is_end == 2">
          <el-button type="primary" @click="handleSubmitReview" plain>提交评分</el-button>
          <el-button type="success" @click="reset()" plain>重置评分</el-button>
        </el-row>

      </div>

    </el-drawer>
    <el-dialog
      title="项目导出"
      :visible.sync="centerDialogVisible"
      width="40%"
      center
    >
      <el-radio-group v-model="derive">
        <el-radio :label="1">全部任务(该项目)</el-radio>
        <el-radio :label="2">节点任务(该节点)</el-radio>
        <el-radio :label="3">搜索任务(该节点)</el-radio>
      </el-radio-group>


      <span slot="footer" style="text-align: right" class="dialog-footer">
          <el-button type="primary" @click="deriveData">导出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {getDeptList} from '@/api/system/dept'
import {
  addTask, dateTask,
  delTask, dismantleTask, editTask,
  evaluation,
  fetchList,
  getCompanyUsers,
  getproList,
  getProUser,
  getTaskCategory,
  getTaskLevel, projectEdit
} from '@/api/porject/project'
import Pagination from '@/components/Pagination'
import {parseTime} from '@/utils'

export default {
  components: {Pagination},
  data() {
    return {
      deptOptions: {},
      proListQuery: {
        'dept_id': 2,
        'page': 1,
        'page_size': 50,
        'pid': 0,
        'search_status': 1,
        'pro_name': '',
        'sort': 'status'
      },
      listQuery: {
        task_name: undefined,
        search_time: null,
        receive_id: undefined
      },
      proDate: {},
      selectPro: '0',
      selectNode: null,
      selectIndex: 0,
      derive: 1,
      nodeList: [],
      pageNum: null ?? 7,
      pagination_value: false,
      pro_pagination_total: 0,
      takList: [],
      drawer: false,
      task_data: {},
      check_data: {
        'task_id': undefined,
        'task_score': undefined,
        'opinion': undefined
      }, // 审核数据
      companyUsers: [],  // 公司成员
      proDateUsers: [], // 项目参与人员
      taskCategory: [], // 任务类别
      taskLevel: [], // 任务等级
      addRowTask: {
        'pid': 0,
        'receive_id': undefined,
        'check_user_id': undefined,
        'task_level': undefined,
        'task_name': undefined,
        'task_category': undefined,
        'start_time': undefined,
        'end_time': undefined
      },
      centerDialogVisible: false,
      // 任务等级
      multiple: false, //是否开启多选
      rules: {
        company_id: [{required: true, message: '请选择公司名称', trigger: 'change'}],
        pro_name: [{required: true, message: '请输入项目名称', trigger: 'blur'}],
        pro_notify: [{required: true, message: '请选择项目人员', trigger: 'change'}],
        pro_managers: [{required: true, message: '请选择项目经理', trigger: 'change'}],
        pro_type: [{required: true, message: '请选择项目类型', trigger: 'change'}],
        search_time: [{required: true, message: '请选择日期', trigger: 'change'}],
        customer_id: [{required: true, message: '请选择客户', trigger: 'change'}],
        pro_level: [{required: true, message: '请选择项目等级', trigger: 'change'}]
      },
      userName: localStorage.getItem('userName'),
      roleId: localStorage.getItem('role'),
      userId: localStorage.getItem('userID')
    }
  },
  created() {
    // 获取部门
    this.getDeptList()
    // 初始化
    this.init()
  },
  methods: {
    // 初始化任务
    init() {
      // 初始化 人员列表 用于搜索
      this.getCompanyUsers()
      // 任务类型
      this.getTaskCategory()
      // 任务等级
      this.getTaskLevel()
    },
    // 获取公司员工
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.companyUsers = response.data
      })
    },
    // 获取部门
    getDeptList() {
      getDeptList().then(response => {
        this.deptOptions = response.data[0].child
        if (this.proListQuery.dept_id) {
          this.changeDept(this.deptOptions[0].id)
        }
      })
    },
    // 修改部门
    async changeDept(id) {
      this.proListQuery.dept_id = id
      await this.getProject()
      const proId = this.proDate[0] ?? null
      if (proId) {
        this.getProTasks(proId)
      }
      this.selectPro = '0'
    },
    // 获取项目列表
    async getProject() {
      await new Promise((resolve, reject) => {
        getproList(this.proListQuery)
          .then(response => {
            this.proDate = response.data.data
            this.pro_pagination_total = response.data.total
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 获取项目节点任务
    getProTasks(e) {
      this.selectPro = e.id.toString()
      // 项目参与人员
      this.getProUserByPro(e.id)
      // 获取项目下的任务信息
      fetchList({
        pro_id: e.id
      }).then(response => {
        this.takList = response.data
      })
    },
    // 根据项目ID 获取项目成员
    getProUserByPro(id) {
      getProUser({pro_id: id}).then(response => {
        this.proDateUsers = response.data
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pro_id = this.selectPro !== 0 ? this.selectPro : this.proDate[0].id;
      this.listQuery.end_time = this.listQuery.search_time[0] + '——' + this.listQuery.search_time[1]

      fetchList(this.listQuery).then(response => {
        this.takList = response.data
      })
    },
    // 任务类型
    getTaskCategory() {
      getTaskCategory().then(response => {
        this.taskCategory = response.data
      })
    },
    // 获取任务等级
    getTaskLevel() {
      getTaskLevel().then(response => {
        for (const i in response.data) {
          this.taskLevel.push({
            'id': i,
            'label': response.data[i]
          })
        }
      })
    },
    // 任务审核
    taskAudit(e) {
      this.task_data = e
      this.check_data.task_id = e.id
      this.check_data.task_score = e.task_score
      this.check_data.opinion = e.opinion
      this.drawer = true
    },
    // 切换项目节点
    changeNode(tab) {
      const key = tab.index
      this.selectIndex = tab.index
      this.selectNode = this.takList[key].id
    },
    // 新增任务
    addPushTask() {
      const row = {
        pid: 0,
        project_name: undefined,
        project_id: undefined,
        project_type: undefined,
        task_level: 3,
        is_end: 1,
        project_typeId: undefined
      }
      // 表格头部插入
      this.takList[this.selectIndex].data.unshift(row)
    },
    // 拆分任务
    splitTask(e, index) {
      if (!this.takList) {
        return
      }

      const row = {
        pid: e.id,
        project_name: undefined,
        project_id: undefined,
        project_type: undefined,
        task_level: 3,
        is_end: 1,
        project_typeId: undefined
      }

      // 在拆分条目下新增一行
      this.takList[this.selectIndex].data.splice(index + 1, 0, row)
    },
    // 删除任务
    handleDelete(row, index) {

      if (!row.id) {
        this.takList[this.selectIndex].data.splice(index, 1)
        return
      }

      if (row.id) {
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delTask({task_id: row.id}).then(response => {
            if (response.meta.status === 200) {
              this.$notify({
                title: 'Success',
                message: '删除成功',
                type: 'success',
                duration: 2000
              })
            }
          })
          this.tableFormat()
          // 回调表单
        }).catch(() => {
          this.$notify({
            title: 'info',
            message: '已取消删除',
            type: 'info',
            duration: 2000
          })
        })
      }
    },
    // 审核任务
    handleSubmitReview() {
      evaluation(this.check_data).then(response => {
        if (response.meta.status === 200) {
          this.$notify({
            title: 'Success',
            message: '评价成功',
            type: 'success',
            duration: 2000
          })
        }
        // 刷新表单
      })
    },
    // 重置评分
    reset() {
      this.check_data.opinion = ''
      this.check_data.task_score = 0
    },
    // 获取选中行
    selectRowEdit(e) {
      this.addRowTask.pid = e.pid ?? undefined
      this.addRowTask.check_user_id = e.check_user_id ?? undefined
      this.addRowTask.receive_id = e.receive_id ?? undefined
      this.addRowTask.task_level = e.task_level ?? undefined
      this.addRowTask.task_name = e.task_name ?? undefined
      this.addRowTask.task_category = e.task_category_id ?? undefined

      if (this.formattingDate(e.start_time) !== undefined) {
        this.addRowTask.start_time = this.formattingDate(e.start_time) + '09:00'
      } else {
        this.addRowTask.start_time = undefined
      }

      if (this.formattingDate(e.end_time) !== undefined) {
        this.addRowTask.end_time = this.formattingDate(e.end_time) + '18:30'
      } else {
        this.addRowTask.end_time = undefined
      }
    },
    // 格式化时间
    formattingDate(e) {
      if (e === undefined) {
        return undefined
      }
      const date = new Date(e)
      const y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      return y + '-' + m + '-' + d + ' '
    },
    // 将数据填充到选中的行
    // index  添加索引 防止同时修改多行时候数据直接的相互影响
    addRowTaskFun(row, category) {
      // 获取表单数据
      switch (category) {
        // 接收人
        case 'receive_id':
          this.addRowTask.receive_id = row.receive_id
          break
        // 接收人
        case 'check_user_id':
          this.addRowTask.check_user_id = row.check_user_id
          break
        // 任务等级
        case 'task_level':
          this.addRowTask.task_level = row.task_level
          break
        // 任务名称
        case 'task_name':
          this.addRowTask.task_name = row.task_name
          break
        // 任务分类
        case 'task_category':
          this.addRowTask.task_category = row.task_category_id
          break
        // 开始时间
        case 'start_time':
          this.addRowTask.start_time = this.formattingDate(row.start_time) + '09:00'
          break
        // 结束时间
        case 'end_time':
          this.addRowTask.end_time = this.formattingDate(row.end_time) + '18:30'
          break
      }

      let flag = true
      Object.keys(this.addRowTask).forEach(key => {
        const value = this.addRowTask[key]
        if (value === '' || value === undefined) {
          flag = false
        }
      })

      if (!flag) {
        return flag
      }

      if (this.selectNode == null) {
        this.addRowTask.project_id = this.takList[0].id
      } else {
        this.addRowTask.project_id = this.selectNode
      }

      if (row.pid !== 0) {
        this.addRowTask.pid = row.pid
        dismantleTask(this.addRowTask).then(() => {
          this.$message({
            message: '拆分成功',
            type: 'success'
          })
        })
        this.tableFormat()
        return
      }

      if (row.id) {
        this.addRowTask.task_id = row.id
        editTask(this.addRowTask).then(() => {
          this.tableFormat()
          this.$message({
            message: '编辑成功',
            type: 'success'
          })
        })
      } else {
        // 新增任务
        addTask(this.addRowTask).then(() => {
          this.tableFormat()
          this.$message({
            message: '添加成功',
            type: 'success'
          })
        })
      }
    },
    // 更新表格数据
    tableFormat() {
      let pro_id = this.proDate[0].id
      if (this.selectPro != 0) {
        pro_id = this.selectPro
      }
      // 项目参与人员
      this.getProUserByPro(pro_id)
      // 获取项目下的任务信息
      fetchList({
        pro_id: pro_id
      }).then(response => {
        this.takList = response.data
      })
    },
    // 导出任务
    async deriveData() {
      let query = {}
      // 导出节点下所有任务
      if (this.derive === 1) {
        let pro_id = this.proDate[0].id
        if (this.selectPro != 0) {
          pro_id = this.selectPro
        }
        query.pro_id = pro_id
      }

      // 导出节点任务
      if (this.derive === 2) {
        query.pro_id = this.selectNode
      }

      // 导出搜索区域任务
      if (this.derive === 3) {
        query.pro_id = this.selectNode
        query.task_name = this.listQuery.task_name
        query.receive_id = this.listQuery.receive_id
      }

      //  获取数据
      await fetchList(query).then((response) => {
        this.allTaskList = response.data
      })

      this.downloadLoading = true
      let _this = this

      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['所属项目', '发布人', '发布人部门', '执行人', '执行人部门', '任务内容', '任务类型', '任务级别', '任务开始时间', '任务结束时间', '发布时间', '完成时间', '审核时间', '评价', '评分', '任务状态']
        const filterVal = ['project_name', 'user_name', 'user_dept', 'receive_name', 'receive_dept', 'task_name', 'task_category', 'task_level_name', 'start_time', 'end_time', 'created_at', 'complete_time', 'check_time', 'opinion', 'task_score', 'status']

        let exportData = []
        let sheetTitle = []
        this.allTaskList.forEach((item) => {
          const data = _this.formatJson(filterVal, item.data)
          if (this.derive > 1) {
            if (item.id === this.selectNode) {
              exportData.push(data)
              sheetTitle.push(item.name)
            }
          } else {
            exportData.push(data)
            sheetTitle.push(item.name)
          }
        })

        let result = []
        exportData.forEach((item, key) => {
          result.push(
            {
              th: tHeader,
              data: item,
              sheetTitle: sheetTitle[key]
            })
        })

        console.log(result)

        excel.export2ExcelMultiSheet(result, '任务管理台账')
        _this.downloadLoading = false
      })
    },
    // 格式化数据
    formatJson(filterVal, n) {
      const export_data = []
      n.map(v => {
        if (v.children === '' || v.children === undefined || v.children.length === 0) {
          export_data.push(this.objToArray(v))
        } else {
          const res = this.getChildren(v)
          if (res.length > 1) {
            res.map(kv => {
              export_data.push(kv)
            })
          } else {
            export_data.push(res)
          }
        }
      })

      return export_data.map((v, k) => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    },
    // 获取子集任务
    getChildren(data, res = []) {
      res.push(this.objToArray(data))

      if (data.children !== undefined) {
        if (data.children.length > 0) {
          for (let i = 0; i < data.children.length; i++) {
            this.getChildren(data.children[i], res)
          }
        }
      }
      return res
    },
    // 对象转数组
    objToArray(obj) {
      const arr = []
      for (const i in obj) {
        arr[i] = obj[i]
      }
      return arr
    },
    // 切换项目状态
    changeStatus(status) {
      this.proListQuery.search_status = status;
      this.getProject();
    }
  }
}
</script>


<style>
.search_box {
  margin: 20px 0;
}

.el-aside {
  padding: 0 0 !important;
  background: #ffffff !important;
}

.el-main {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.pro_aside {
  overflow: hidden !important;
  width: 230px !important;
  border: 1px solid #e6e6e6;
}

.pro_header {
  text-align: center;
  margin-top: 20px;
  height: 40px !important;
  padding-left: 5px;
}


.status-tags .el-tag {
  transition: all 0.3s;
}

.status-tags .el-tag:hover {
  transform: scale(1.05);
}

.pro_aside .el-menu {
  border: none !important;
}

.pro_aside .el-menu-item {
  height: 45px !important;
  line-height: 45px;
}
.overflow-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audit {
  padding: 20px !important;
}

.check {
  margin-top: 10px;
  margin-left: 0;
}
</style>




