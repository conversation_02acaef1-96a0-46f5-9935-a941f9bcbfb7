<template>
    <div class="app-container">

        <div style="display: inline-block;">
            <el-date-picker v-model="search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                type="daterange" clearable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                @change="handleFilter" />

            <el-button style="margin-bottom: 15px;margin-left: 10px;" v-waves class="filter-item" type="primary" icon="el-icon-search" @click="">
                搜索
            </el-button>
            
            <el-row>
                <el-col :span="23">
                    <el-table  v-loading="listLoading"    :data="list.data"   >
                        <el-table-column prop="name" fixed label="姓名" width="150" align="center">
                            <template slot-scope="{row}">
                                <span>{{ row.name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="year" align="center">
                            <el-table-column v-for="(item, index) in list.header" :key="index" :label="item.date"
                                align="center">
                                <el-table-column width="200" :label="item.week"  align="center">
                                    <template slot-scope="{row}">
                                        <span v-if="item.date == itm.date" v-for="(itm, indx) in row.list" :key="indx">
                                            {{ itm.task.task_name }} </span>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script>
import { dateTask } from '@/api/project'

export default {
    data() {
        return {
            list: [],
            year: '',
            search_time: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],

        }
    },
    created() {
        this.getDataList()
    },
    methods: {
        formatDate(value) {
            if (value == null) {
                return ''
            } else {
                const date = new Date(value)
                const y = date.getFullYear()// 年
                let MM = date.getMonth() + 1 // 月
                MM = MM < 10 ? ('0' + MM) : MM
                let d = date.getDate() // 日
                d = d < 10 ? ('0' + d) : d
                return y + '-' + MM + '-' + d
            }
        },
        //最近一周
        handleFilter() {
            if (this.search_time) {
                this.search_time = this.search_time[0] + '——' + this.search_time[1]
                var obj = { search_time: this.search_time }
                dateTask(obj).then(response => {
                this.list = response.data
                this.list.header.forEach((item) => {
                    this.year = item.date.slice(0, 7)
                    this.year = this.year.replace(/-/g, "年")
                    this.year = this.year + '月'
                })
            })
            }
        },
        getDataList() {
            const obj = {
                search_time: this.search_time[0] + '——' + this.search_time[1]
            }
            dateTask(obj).then(response => {
                this.list = response.data
                this.list.header.forEach((item) => {
                    this.year = item.date.slice(0, 7)
                    this.year = this.year.replace(/-/g, "年")
                    this.year = this.year + '月'
                })
            })
        }
    }
}
</script>
<style lang="scss" scoped></style>
