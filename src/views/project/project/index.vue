<template>
  <div class="app-container">
    <div>
      <div class="filter-container" style="position: relative">
        <el-row :gutter="24">
          <el-col :span="3">
            <el-input v-model="listQuery.pro_name" placeholder="项目名称" clearable class="filter-item"
                      @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :span="2">
            <el-select v-model="listQuery.pro_managers" placeholder="项目经理" clearable class="filter-item"
                       @visible-change="getCompanyUsers" @change="handleFilter"
            >
              <el-option v-for="(item, i) in projectManager" :key="i" :label="item.user_username" :value="item.id"/>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-select v-model="listQuery.pro_type" placeholder="项目类型" clearable class="filter-item"
                       @change="handleFilter"
            >
              <el-option v-for="(item, idx) in proTypeList" :key="idx" :label="item.name" :value="item.id"/>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-select v-model="listQuery.expired" placeholder="是否超期" clearable class="filter-item"
                       @change="handleFilter"
            >
              <el-option v-for="(item, ind) in proExpiredList" :key="ind" :label="item.expired" :value="item.id"/>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-select v-model="listQuery.status" placeholder="完成状态" clearable class="filter-item"
                       @change="handleFilter"
            >
              <el-option v-for="(item, ind) in proStatusList" :key="ind" :label="item.status" :value="item.id"/>
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker v-model="listQuery.search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                            type="daterange" clearable range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="handleFilter"
            />
          </el-col>
          <el-col :span="2">
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
              搜索
            </el-button>
          </el-col>
        </el-row>
        <div>
          <div style="display:block;">
            <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
                       @click="handleDownload"
            >
              导出
            </el-button>
            <el-button @click="upload" v-waves class="filter-item" type="primary" icon="el-icon-download">
              下载模板
            </el-button>
            <el-button v-waves class="filter-item" type="primary" @click="openInto">
              <svg-icon icon-class="guide"/>
              导入
            </el-button>
            <el-button class="filter-item" style="" type="primary" icon="el-icon-edit" @click="handleCreate">
              新增
            </el-button>
          </div>
        </div>
        <div v-if="popshow" style="  position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 99;">
          <div class="pop-bg"
               style="position: absolute;top: 0;right: 0;bottom: 0;left: 0;background: rgba(0, 0, 0, 0.7);"
          />
          <div class="pop-text"
               style="position: absolute;width: 600px;height: 500px;top: 50%;left: 50%;padding: 20px;transform: translate(-50%, -50%);background: #fff;z-index: 9;box-sizing: border-box;"
          >
            <h2 class="user-permission"
                style="margin-top:1px;color:#333;margin-bottom: 30px;color: #666;font-size: 16px;font-weight: bold;text-align:center;"
            >
              项目导入</h2>
            <p class="close"
               style="position: absolute;top: 0;right: 25px;color: #a6a6a6;font-size: 25px;cursor: pointer;margin-top: 13px;"
               @click="closePop"
            >×</p>

            <el-upload style="text-align:center;" class="upload-demo" drag action="fakeaction"
                       :http-request="uploadSectionFile" multiple
            >
              <i class="el-icon-upload"/>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传excel表格，且不超过1000kb</div>
            </el-upload>
          </div>
        </div>
      </div>
      <div v-if="flog === 1">
        <el-table v-loading="listLoading" :data="list" style="width: 100%; margin-bottom: 20px; " row-key="id" border
                  fit
                  highlight-current-row default-expand-all lazy
        >
          <el-table-column label="项目编号" align="center" width="130">
            <template slot-scope="{row}">
              <span>{{ row.pro_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" align="left" width="160">
            <template slot-scope="{row}">
              <span>{{ row.pro_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目等级" align="center" width="80">
            <template slot-scope="{row}">
              <span>{{ row.pro_level }}</span>
            </template>
          </el-table-column>
          <el-table-column label="管理者" align="center" width="100">
            <template slot-scope="{row}">
              <span>{{ row.pro_managers }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目类型" align="center" width="100">
            <template slot-scope="{row}">
              <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">
                {{ row.pro_type }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目标签" align="center" width="100">
            <template slot-scope="{row}">
              <span>{{ row.label }}</span>
            </template>
          </el-table-column>
          <el-table-column label="成员数量" align="center">
            <template slot-scope="{row}">
              <span>{{ row.member_num }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目周期" align="center">
            <template slot-scope="{row}">
              <span>{{ row.project_cycle }}</span>
            </template>
          </el-table-column>
          <el-table-column label="客户名称" align="center">
            <template slot-scope="{row}">
              <span>{{ row.customer_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目开始时间" align="center">
            <template slot-scope="{row}">
              <span>{{ row.pro_start_time }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目结束时间" align="center">
            <template slot-scope="{row}">
              <span>{{ row.pro_end_time }}</span>
            </template>
          </el-table-column>
          <el-table-column label="超期状态" class-name="status-col">
            <template slot-scope="{row}">
              <span v-if="row.expired == '正常'">
                <el-tag>
                  {{ row.expired }}
                </el-tag>
              </span>
              <span v-else>
                <el-tag type="danger">
                  {{ row.expired }}
                </el-tag>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="完成状态" class-name="status-col">
            <template slot-scope="{row}">
              <el-tag :type="row.status | statusFilter">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column :label="$t('table.actions')" align="left" width="220px"
                           style="display:flex;flex-wrap:wrap;" class-name="small-padding fixed-width"
          >
            <template slot-scope="{row,$index}">
              <el-button type="primary" size="mini" @click="handleView(row)">
                管理
              </el-button>

              <el-button v-if="row.status === '进行中'" size="mini" type="info" @click="handleEnd(row)">
                结束
              </el-button>

              <el-button v-if="row.status === '已完成'" size="mini" type="success" @click="handleEnd(row)">
                激活
              </el-button>

              <el-button size="mini" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 新增项目弹窗 -->
      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="temp"
          :inline="true"
          label-width="90px"
          label-position="left"
        >
          <el-form-item label="公司名称" prop="company_id">
            <el-select v-model="temp.company_id" placeholder="公司名称" clearable class="filter-item">
              <el-option v-for="(item, count) in proCompanyList" :key="count" :label="item.company_name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="项目名称" prop="pro_name">
            <el-input v-model="temp.pro_name" placeholder="项目名称" />
          </el-form-item>
          <el-form-item label="项目经理" prop="pro_managers">
            <el-select v-model="temp.pro_managers" filterable placeholder="请选择" @visible-change="getCompanyUsers" @change="changeIndex">
              <el-option v-for="item in options" :key="item.user_username" :label="item.user_username" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="项目人员" prop="pro_notify">
            <el-select v-model="temp.pro_notify" multiple filterable placeholder="请选择" @visible-change="getCompanyUsers" @change="changes">
              <el-option v-for="item in userListData" :key="item.user_username" :label="item.user_username" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="项目类型" prop="pro_type_id">
            <el-select v-model="temp.pro_type_id" placeholder="项目类型" clearable class="filter-item">
              <el-option v-for="(item, cou) in proTypeList" :key="cou" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="客户名称" prop="customer_id">
            <el-select v-model="temp.customer_id" placeholder="客户名称" clearable class="filter-item">
              <el-option v-for="(item, cou2) in proCustomerList" :key="cou2" :label="item.customer_name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="项目等级" prop="pro_level">
            <el-select v-model="temp.pro_level" placeholder="项目等级" clearable class="filter-item">
              <el-option v-for="(item, cou) in proLevelList" :key="cou" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="选择日期" prop="search_time">
            <el-date-picker v-model="temp.search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="daterange" clearable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">提交</el-button>
        </div>
      </el-dialog>
      <!-- 新建项目节点 -->
      <el-dialog :title="splitPro[splitStatus]" :visible.sync="showSplitProject">
        <el-form ref="dataForm"
                 :rules="rules"
                 :model="splitProject"
                 label-position="left"
                 :inline="true"
                 label-width="80px"
        >
          <!-- 弹框 -->
          <el-form-item label="节点名称" prop="pro_name">
            <el-input v-model="splitProject.pro_name" placeholder="节点名称" style="width: 250px"/>
          </el-form-item>

          <el-form-item label="项目经理" prop="pro_managers">
            <el-select v-model="splitProject.pro_managers" filterable placeholder="请选择" style="width: 350px"
                       @visible-change="getCompanyUsers"
            >
              <el-option v-for="item in options" :key="item.id" :label="item.user_username"
                         :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="项目类型" prop="node_type_id">
            <el-select v-model="splitProject.node_type_id" placeholder="项目类型" clearable style="width: 250px"
                       class="filter-item" @visible-change="getCompanyUsers"
            >
              <el-option v-for="(item, index) in proTypeList" :key="index" :label="item.name" :value="item.id"/>
            </el-select>
          </el-form-item>

          <el-form-item label="选择日期" prop="search_time">
            <el-date-picker v-model="splitProject.search_time" unlink-panels value-format="yyyy-MM-dd"
                            format="yyyy-MM-dd"
                            type="daterange" clearable range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期"
            />
          </el-form-item>

          <el-form-item label="项目人员" prop="pro_notify">
            <el-select v-model="splitProject.pro_notify" multiple filterable placeholder="请选择"
                       style="width: 600px" @visible-change="getCompanyUsers"
            >
              <el-option v-for="item in userListData" :key="item.user_username" :label="item.user_username"
                         :value="item.id"
              />
            </el-select>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showSplitProject = false">
            取消
          </el-button>
          <el-button type="primary" @click="splitStatus === 'create' ?addSplitData() : updateSplitData()">
            提交
          </el-button>
        </div>
      </el-dialog>
      <!-- 操作弹框 -->
      <el-drawer :title="drawerData.pro_name" :visible.sync="drawer" size="70%">
        <el-row>
          <el-col :span="23" :offset="1">
            <template>
              <el-tabs v-model="activeName">
                <el-tab-pane label="项目概览" name="overview">
                  <el-row>
                    <el-col :span="24">
                      <el-form ref="dataForm" :inline="true" :rules="rules" :model="drawerData" label-position="left">
                        <el-form-item label="项目名称" prop="pro_name">
                          <el-input v-model="drawerData.pro_name" placeholder="项目名称" @change="updateData"
                                    style="width: 300px"
                          />
                        </el-form-item>

                        <el-form-item label="项目经理" prop="pro_managers">
                          <el-select v-model="drawerData.pro_managers" filterable placeholder="请选择"
                                     style="width: 350px"
                                     @visible-change="getCompanyUsers"
                                     @change="changeIndex"
                          >
                            <el-option v-for="item in options" :key="item.user_username" :label="item.user_username"
                                       :value="item.id"
                            />
                          </el-select>
                        </el-form-item>

                        <el-form-item label="项目类型">
                          <el-select v-model="drawerData.pro_type_id" placeholder="项目类型" clearable
                                     @change="updateData"
                                     style="width: 300px"
                                     class="filter-item"
                          >
                            <el-option v-for="(item, cou) in proTypeList" :key="cou" :label="item.name"
                                       :value="item.id"
                            />
                          </el-select>
                        </el-form-item>

                        <el-form-item label="项目等级" prop="pro_level">
                          <el-select v-model="drawerData.pro_level" placeholder="项目等级" @change="updateData"
                                     clearable
                                     style="width: 350px"
                                     class="filter-item"
                          >
                            <el-option v-for="(item, cou) in proLevelList" :key="cou" :label="item"
                                       :value="item"
                            />
                          </el-select>
                        </el-form-item>

                        <el-form-item label="关联客户" prop="customer_id">
                          <el-select v-model="drawerData.customer_id" placeholder="客户" @change="updateData" clearable
                                     style="width: 300px"
                                     class="filter-item"
                          >
                            <el-option v-for="(item, cou2) in proCustomerList" :key="cou2"
                                       :label="item.customer_name"
                                       :value="item.id"
                            />
                          </el-select>
                        </el-form-item>

                        <el-form-item label="项目周期" prop="search_time">
                          <el-date-picker v-model="drawerData.search_time" unlink-panels value-format="yyyy-MM-dd"
                                          format="yyyy-MM-dd" @change="updateData"
                                          type="daterange" clearable range-separator="至" start-placeholder="开始日期"
                                          end-placeholder="结束日期"
                          />
                        </el-form-item>

                        <el-form-item label="项目人员" prop="pro_notify">
                          <el-select v-model="drawerData.pro_notify" multiple filterable placeholder="请选择"
                                     style="width: 750px" @visible-change="getCompanyUsers"
                                     @change="changes"
                          >
                            <el-option v-for="item in userListData" :key="item.user_username"
                                       :label="item.user_username"
                                       :value="item.id"
                            />
                          </el-select>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                  <el-divider></el-divider>
                  <el-container>
                    <el-header>
                      <el-row class="mb-4">
                        <el-button type="primary" @click="splitView">新增节点</el-button>
                      </el-row>
                    </el-header>

                    <el-main>
                      <template>
                        <el-table
                          :data="nodeData"
                        >
                          <el-table-column
                            prop="date"
                            label="节点名称"
                            width="180"
                          >
                            <template slot-scope="{row}">
                              {{ row.pro_name }}
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="name"
                            label="节点负责人"
                            width="180"
                          >
                            <template slot-scope="{row}">
                              {{ row.pro_managers }}
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="address"
                            label="项目类型"
                          >
                            <template slot-scope="{row}">
                              {{ row.pro_type }}
                            </template>

                          </el-table-column>

                          <el-table-column
                            prop="address"
                            label="开始日期"
                          >
                            <template slot-scope="{row}">
                              {{ row.pro_start_time }}
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="address"
                            label="结束日期"
                          >
                            <template slot-scope="{row}">
                              {{ row.pro_end_time }}
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="address"
                            label="节点状态"
                          >

                            <template slot-scope="{row}">
                              {{ row.status }}
                            </template>

                          </el-table-column>

                          <el-table-column
                            prop="address"
                            label="操作"
                            width="200px"
                          >
                            <template slot-scope="{row}">
                              <el-button type="primary" size="mini" @click="updateView(row)">编辑</el-button>
                              <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
                            </template>

                          </el-table-column>
                        </el-table>
                      </template>
                    </el-main>
                  </el-container>
                </el-tab-pane>
                <el-tab-pane label="成本核算" name="accounting">
                  <el-descriptions class="margin-top" :column="3" size="medium " border>
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-s-order"></i>
                        关联合同：
                      </template>
                      <el-select v-model="costAccounting.contract_id" multiple filterable
                                 style="width: 200px; margin-left: 10px; padding: 10px" placeholder="关联合同"
                                 @visible-change="getContract(costAccounting.id)"
                      >
                        <el-option v-for="item in options_task" :key="item.id" :label="item.contract_num"
                                   :value="item.id"
                        />
                      </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-connection "></i>
                        项目金额：
                      </template>
                      <el-input class="pro_name" placeholder="请输入金额" v-model="costAccounting.money" clearable>
                      </el-input>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-connection "></i>
                        税额：
                      </template>
                      <el-input class="pro_name" placeholder="请输入税额" v-model="costAccounting.tax" clearable>
                      </el-input>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-connection "></i>
                        外包支出：
                      </template>
                      <el-input class="pro_name" placeholder="请输入外包支出" v-model="costAccounting.outsourcing"
                                clearable
                      >
                      </el-input>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-connection "></i>
                        杂项支出：
                      </template>
                      <el-input class="pro_name" placeholder="请输入杂项支出" v-model="costAccounting.other" clearable>
                      </el-input>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-connection "></i>
                        项目提成支出：
                      </template>
                      <el-input class="pro_name" placeholder="请输入项目提成支出" v-model="costAccounting.commission"
                                clearable
                      >
                      </el-input>
                    </el-descriptions-item>
                  </el-descriptions>

                  <el-divider></el-divider>


                  <el-table :data="skuListInfo" style="width: 100%"
                            max-height="450"
                  >
                    <el-table-column prop="create_by" label="报销人">
                    </el-table-column>
                    <el-table-column :show-overflow-tooltip="true" height="100" prop="expense_detail" label="报销事由">
                    </el-table-column>
                    <el-table-column prop="expense_money" label="报销金额">
                    </el-table-column>
                    <el-table-column prop="created_at" show-overflow-tooltip label="报销时间">
                    </el-table-column>
                  </el-table>

                  <div class="pro_reject">
                    <span> 合计：{{ allMoney }}</span>
                  </div>
                  <el-divider></el-divider>

                  <el-table v-loading="listLoading" :data="projectWorkDay"
                            highlight-current-row
                            style="width: 100%"
                            max-height="450"
                  >
                    <el-table-column label="姓名" align="center" width="120">
                      <template slot-scope="{row}">
                        <span>{{ row.user_username }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="工时天数" align="center">
                      <template slot-scope="{row}">
                        <span>{{ row.days }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="人力成本/天" align="center">
                      <template slot-scope="{row}">
                        <el-input disabled v-model="row.cost"
                                  class="pro_name" placeholder="请输入项目提成支出"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label="合计" align="center">
                      <template slot-scope="{row}">
                        <el-input disabled v-model="row.all_cost" class="pro_name" placeholder="请输入项目提成支出"/>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="  pro_reject">
                    合计：{{ costAccounting.labor_cost }}
                  </div>
                </el-tab-pane>

                <el-tab-pane label="项目归档">
                  <template>
                    <el-tabs tab-position="left">
                      <el-tab-pane :label="item.ratee.user_username" v-for="item in markInfo">
                        <el-descriptions :column="2" border>
                          <el-descriptions-item label="人员"> {{ item.ratee.user_username }}</el-descriptions-item>
                          <el-descriptions-item label="角色"></el-descriptions-item>
                          <el-descriptions-item v-for="items in item.questions" :span="items.type == 2 ? 2 : 1"
                                                :label="items.question"
                          >{{ items.answer }}
                          </el-descriptions-item>
                        </el-descriptions>
                      </el-tab-pane>
                    </el-tabs>
                  </template>
                </el-tab-pane>

              </el-tabs>
            </template>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
    <div v-if="flog === 0">
      <!-- wl-gantt -->
      <wlgantt :data="list"></wlgantt>
    </div>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
                @pagination="getList"
    />
  </div>
</template>
<script>
import {
  getproList,
  ProCategory,
  searchCompany,
  imageUpload,
  projectAdd,
  getCompanyUsers,
  projectEdit,
  projectDel,
  getProLevel,
  CustomerAllList,
  claimexpenseList,
  projectWorkDay,
  proMarkInfo,
  projectContract,
  mergeInfo,
  questionMark, projectEnd
} from '@/api/porject/project'
import waves from '@/directive/waves'
import { parseTime } from '@/utils'
import progressChart from '../progressChart'
import Pagination from '@/components/Pagination'
import wlgantt from '../wl-gantt'

export default {
  name: 'ComplexTable',
  components: { Pagination, progressChart, wlgantt },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      data: [],
      drawer: false,
      activeName: 'overview',
      drawerData: [],
      nodeData: [],
      listQuery: {
        page: 1,
        page_size: 10,
        pid: 0,
        pro_name: undefined,
        pro_managers: undefined,
        pro_end_time: undefined,
        pro_type_id: undefined,
        status: undefined,
        expired: undefined,
        sort: undefined,
        search_time: null,
        search_id: null
      },
      showSplitProject: false,
      flog: 1,
      splitStatus: '',
      splitProject: {},
      proTypeList: [],
      proLevelList: [],
      proCustomerList: [],
      proCompanyList: [],
      proStatusList: [
        {
          status: '待完成',
          id: '1'
        },
        {
          status: '已完成',
          id: '2'
        }
      ],
      proExpiredList: [
        {
          expired: '正常',
          id: '1'
        },
        {
          expired: '超期',
          id: '2'
        }
      ],
      listData: {
        page: 1,
        page_size: 20
      },
      costAccounting: {
        contract_id: []
      },
      markInfo: {},
      projectScore: [],
      comment: [],
      comment1: '',
      skuListInfo: [],
      projectWorkDay: [],
      options_task: [],
      project: {},
      popshow: false,
      list: [],
      total: 0,
      projectManager: [],
      userListData: [],
      listLoading: true,
      allMoney: '',
      options: [],
      temp: {
        company_id: '',
        pro_name: '',
        pro_managers: '',
        pro_type_id: '',
        pro_start_time: '',
        pro_end_time: '',
        pro_notify: [],
        pro_level: '',
        customer_id: '',
        search_time: ''
      },
      textMap: {
        update: '编辑项目',
        create: '新增项目'
      },
      splitPro: {
        update: '编辑节点',
        create: '新增节点'
      },
      rules: {
        company_id: [{ required: true, message: '请选择公司名称', trigger: 'change' }],
        pro_name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        pro_notify: [{ required: true, message: '请选择项目人员', trigger: 'change' }],
        pro_managers: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
        node_type_id: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
        search_time: [{ required: true, message: '请选择日期', trigger: 'change' }],
        customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
        pro_type_id: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
        pro_level: [{ required: true, message: '请选择项目等级', trigger: 'change' }]
      },
      downloadLoading: false,
      dialogFormVisible: false,
      dialogStatus: ''
    }
  },
  created() {
    this.getList()
    this.searchList()
    this.getProLevels()
    this.getProCustomer()
    this.getCompanyUsers()
  },
  methods: {
    // 项目管理
    handleView(row) {
      this.drawer = true
      this.getCompanyUsers()
      this.drawerData = row
      this.nodeData = row.pro_nodes
      this.drawerData.timestamp = new Date(this.temp.timestamp)
      this.drawerData.pro_notify = this.drawerData.pro_notify.map((str) => parseInt(str))

      //  成本核算
      this.getMergeInfo(row.id)
      // 项目复盘
      this.getProMarkInfo(row.id)
    },
    //  新增项目
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 新增项目
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const [start, end] = this.formatSearchTime(this.temp.search_time)
          this.temp.pro_start_time = start
          this.temp.pro_end_time = end
          this.temp.pro_notify = this.formatNotifyArray(this.temp.pro_notify).toString()
          this.temp.pro_managers = this.formatManagerId(this.temp.pro_managers)
          projectAdd(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    // 新增节点视图
    splitView() {
      this.splitProject = {}
      this.splitProject.pro_level = this.drawerData.pro_level
      this.splitProject.customer_id = this.drawerData.customer_id
      this.splitProject.pid = this.drawerData.id
      this.splitStatus = 'create'
      this.showSplitProject = true
    },
    // 新增项目节点提交
    addSplitData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const [start, end] = this.formatSearchTime(this.splitProject.search_time)
          this.splitProject.pro_start_time = start
          this.splitProject.pro_end_time = end
          this.splitProject.pro_notify = this.formatNotifyArray(this.splitProject.pro_notify).toString()
          this.splitProject.pro_type_id = this.splitProject.node_type_id
          this.splitProject.pro_managers = this.formatManagerId(this.splitProject.pro_managers)
          projectAdd(this.splitProject).then(() => {
            this.showSplitProject = false
            this.getProInfo(this.drawerData.id)
            this.splitProject = {}
            this.$notify({
              title: 'Success',
              message: '新增节点成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    // 修改项目节点视图
    updateView(row) {
      this.splitProject = {}
      this.splitProject.id = row.id
      this.splitProject.pro_name = row.pro_name
      this.splitProject.node_type_id = parseInt(row.pro_type_id)
      this.splitProject.pro_managers = parseInt(row.pro_managers_id)
      this.splitProject.pro_notify = row.pro_notify.map((str) => parseInt(str))
      this.splitProject.pro_level = row.pro_level
      this.splitProject.customer_id = row.customer_id
      this.splitProject.search_time = row.search_time
      this.splitStatus = 'update'
      this.showSplitProject = true
    },
    // 修改项目节点提交
    updateSplitData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const [start, end] = this.formatSearchTime(this.splitProject.search_time)
          this.splitProject.pro_start_time = start
          this.splitProject.pro_end_time = end
          this.splitProject.pro_notify = this.formatNotifyArray(this.splitProject.pro_notify).toString()
          this.splitProject.pro_type_id = this.splitProject.node_type_id
          this.splitProject.pro_managers = this.formatManagerId(this.splitProject.pro_managers)
          projectEdit(this.splitProject).then(() => {
            this.showSplitProject = false
            this.getProInfo(this.drawerData.id)
            this.splitProject = {}
            this.$notify({
              title: 'Success',
              message: '编辑成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    // 根据ID 获取项目数据
    getProInfo(id) {
      this.listQuery.search_id = id
      getproList(this.listQuery).then(response => {
        response.data.data.forEach((item) => {
          item.name = item.pro_name
          item.startDate = item.search_time[0]
          item.endDate = item.search_time[1]
        })
        this.drawerData = response.data.data[0]
        this.nodeData = response.data.data[0].pro_nodes
        this.drawerData.timestamp = new Date(this.temp.timestamp)
        this.drawerData.pro_notify = this.drawerData.pro_notify.map((str) => parseInt(str))
        this.listQuery.search_id = null
      })
    },
    // 删除项目和节点
    handleDelete(row) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectDel({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getList()
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      })
    },
    handleEnd(row) {
      let message = ''
      if (row.status === '进行中') {
        message = '要结束该项目吗,项目结束后则不可对该项目在进行报销和发布任务，请知晓'
      } else if (row.status === '已完成') {
        message = '要激活该项目吗，激活后可继续进行报销和发布任务，请知晓'
      } else {
        message = '确定要进行此操作吗？'
      }
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectEnd({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getList()
            this.$notify({
              title: 'Success',
              message: '操作成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      })
    },
    // 编辑项目项目信息
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const [start, end] = this.formatSearchTime(this.drawerData.search_time)
          this.drawerData.pro_start_time = start
          this.drawerData.pro_end_time = end
          const tempData = Object.assign({}, this.drawerData)
          tempData.pro_managers = this.formatManagerId(this.drawerData.pro_managers_id)
          tempData.pro_notify = this.formatNotifyArray(this.drawerData.pro_notify).toString()
          projectEdit(tempData).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
            this.getProInfo(this.drawerData.id)
          })
        }
      })
    },
    // 成本核算 -------------------------------------------------------
    getMergeInfo(e) {
      mergeInfo({ pro_id: e }).then(response => {

        // 获取成本相关信息
        let contractId = response.data.contract_id

        if (contractId !== '') {
          response.data.contract_id = contractId.split(',')
          contractId = this.costAccounting.contract_id.map(Number)
        }

        this.costAccounting = response.data
        this.costAccounting.contract_id = contractId
        this.costAccounting.id = e

        //  获取合同信息
        this.getContract(e)
        //  获取报销信息
        this.reimbursementList(e)
        // 获取参与人员天数
        this.getProjectWorkDay(e)
      })
    },
    // 获取合同信息
    getContract(e) {
      projectContract({ pro_id: e }).then(response => {
        this.options_task = response.data
      })
    },
    // 获取项目中每个人的报销信息
    reimbursementList(e) {
      claimexpenseList({ pro_id: e }).then(response => {
        this.allMoney = response.data.total
        const arr = []
        response.data.data.forEach((item) => {
          item.list.forEach((itm) => {
            itm.create_by = item.create_by
            itm.created_at = item.created_at
            arr.push(itm)
          })
        })
        this.skuListInfo = arr
      })
    },
    // 获取项目中每个人工作多少天
    getProjectWorkDay(e) {
      projectWorkDay({ pro_id: e }).then(response => {
        this.projectWorkDay = response.data
      })
    },
    // 获取所有的项目等级
    getProLevels() {
      getProLevel().then(response => {
        this.proLevelList = response.data.pro_level
      })
    },
    // 获取所有客户
    getProCustomer() {
      CustomerAllList().then(response => {
        this.proCustomerList = response.data
      })
    },
    // 导入弹框
    openInto() {
      this.popshow = true
    },
    // 上传导入的exile
    upload() {
      this.flag('project')
    },
    //  获取复盘信息
    getProMarkInfo(e) {
      proMarkInfo({ pro_id: e }).then(response => {
        this.markInfo = response.data
      })
    },
    // 导出项目评价分
    score() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['项目问题', '平均分数']
        const filterVal = ['title', 'score']
        let explode_data = []
        await questionMark({ pro_id: this.project_id }).then(response => {
          explode_data = response.data
        })
        const data = this.formatJson(explode_data, filterVal)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '项目评分'
        })
        this.downloadLoading = false
      })
    },
    // 导出项评价
    issue(e) {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        let tHeader = ['姓名']
        const filterVal = ['user_username', 'mark_msg']
        let explode_data = []
        tHeader.push(e.question)
        e.answer.forEach((iem, inex) => {
          let obj = {}
          obj.mark_msg = iem.mark_msg
          obj.user_username = iem.user.user_username
          explode_data.push(obj)
        })

        const data = this.formatJson(explode_data, filterVal)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '项目评价'
        })
        this.downloadLoading = false
      })
    },
    changeIndex(user_id) {
      this.drawerData.pro_managers_id = user_id
      this.updateData()
    },
    changes(user_id) {
      this.drawerData.receive_id = user_id
      this.updateData()
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options = response.data
        this.userListData = response.data
        this.projectManager = response.data
      })
    },
    indexAdd(index) {
      const page = this.listQuery.page
      const resize = this.listQuery.page_size
      return index + 1 + (page - 1) * resize
    },
    closePop() {
      this.popshow = false
    },
    getList() {
      if (this.listQuery.search_time) {
        this.listQuery.pro_end_time = this.listQuery.search_time[0] + '——' + this.listQuery.search_time[1]
      }
      this.listLoading = true
      getproList(this.listQuery).then(response => {
        response.data.data.forEach((item) => {
          item.name = item.pro_name
          item.startDate = item.search_time[0]
          item.endDate = item.search_time[1]
        })
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    searchList() {
      searchCompany(this.listData).then(response => {
        this.proCompanyList = response.data.data
      })
      ProCategory().then(response => {
        this.proTypeList = response.data
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      if (this.listQuery.search_time == null) {
        this.listQuery.pro_end_time = ''
      }
    },
    // 工具方法：格式化 pro_notify
    formatNotifyArray(arr) {
      if (Array.isArray(arr)) {
        return arr.map(Number).filter(v => !isNaN(v))
      }
      return []
    },
    // 工具方法：格式化 manager id
    formatManagerId(val) {
      if (typeof val === 'string' || typeof val === 'number') {
        return Number(val)
      }
      return ''
    },
    // 工具方法：格式化时间区间
    formatSearchTime(arr) {
      if (Array.isArray(arr) && arr.length === 2) {
        return arr
      }
      return ['', '']
    },
    resetTemp() {
      this.temp = {
        company_id: '',
        pro_name: '',
        pro_managers: '',
        pro_type_id: '',
        pro_start_time: '',
        pro_end_time: '',
        pro_notify: [],
        pro_level: '',
        customer_id: '',
        search_time: ''
      }
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['序号', '公司名称', '项目编号', '项目名称', '项目等级', '项目管理者', '项目类型', '客户', '项目开始时间', '项目结束时间', '超期状态', '完成状态']
        const filterVal = ['company_name', 'pro_number', 'pro_name', 'pro_level', 'pro_managers', 'pro_type', 'customer_name', 'pro_start_time', 'pro_end_time', 'expired', 'status']
        let explode_data = []
        this.listQuery.page_size = 100
        await getproList(this.listQuery).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data.data
            this.listQuery.page_size = 10
          }
        })
        const data = this.formatJson(explode_data, filterVal)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '项目管理台账'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      form.append('excel_file', file)
      imageUpload(form)
        .then(res => {
          if (res.meta.status === 200) {
            this.popshow = false
            this.getList()
          }
        })
    }
  }
}
</script>

<style>
.nodeAdd {
  position: relative;
  left: 49px;
}

h5 {
  margin-bottom: 0 !important;
}

.node_item {
  padding-left: 10px !important;
  padding-right: 20px !important;
  font-size: 12px !important;
}

.pro_reject {
  text-align: right;
  margin: 10px;
  font-weight: bold;
  font-size: 15px;
}

.node_task_header {
  text-align: left;
  font-size: 14px;
  height: auto !important;
  border-bottom: 1px solid #dcdfe6 !important;;
  padding: 0 20px 10px 0 !important;;
}

.node_box {
  border: none !important;
}

.node_container {
  width: 190px !important;
  margin-right: 30px;
  color: #000000;
  background-color: #fff;
  border: 1px solid #dcdfe6;
}

.no-wrap-header {
  white-space: nowrap;
}
.el-form--inline .el-form-item {
  margin-right: 32px;
  margin-bottom: 18px;
}
</style>

