<template>
    <div>
        <div id="app">
            <!-- Echarts 图表 -->
            <div ref="progressChart" class="progressChart"></div>
        </div>
        <!-- <pagination :total="item.total" :page.sync="item.page" :limit.sync="item.page_size" @pagination="initChart" /> -->
    </div>

</template>

<script>
import echarts from 'echarts'
import { getproList } from '@/api/porject/project'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
    components: { Pagination },
    props: {
        ganTet: {
            type: Object,
            default: () => {
            }
        },
    },
    data() {
        return {
            proName: [],
            start_time: [],
            end_time: [],
            item: {}
        }
    },
    watch: {
        "ganTet": {
            deep: true,
            immediate: true,
            handler(e) {
                this.initChart(e)
            }
        }
    },
    updated() {
        this.myChart.resize();
    },
    mounted() {
    },
    created() {
        this.item = this.ganTet
        // this.$nextTick(() => {
        // this.initChart(this.ganTet)
        // })
    },
    methods: {
        /* 初始化图表 */
        async initChart(e) {
                this.proName = [],
                this.start_time = [],
                this.end_time = [],
                await getproList(e).then((res) => {
                    this.item.total = res.data.total
                    res.data.data.forEach((item) => {
                        this.proName.push(item.pro_name)
                        this.start_time.push(item.pro_start_time)
                        this.end_time.push(item.pro_end_time)
                    })
                })
            const progressChart = echarts.init(this.$refs.progressChart)
            const option = {
                // 鼠标移入提示工具
                tooltip: {
                    trigger: 'axis',
                    formatter(params) {
                        if (params[1].data && params[0].data) {
                            return `<div>开始时间：${params[1].data}</div>` + `<div>结束时间：${params[0].data}</div>`
                        } else {
                            return ''
                        }
                    },
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    containLabel: true,
                    show: false,
                    right: 80,
                    left: 40,
                    bottom: 40,
                    top: 20,
                    backgroundColor: '#fff'
                },
                legend: {
                    // 图例组件
                    data: ['持续时间'],
                    align: 'auto',
                    top: 'bottom'
                },
                xAxis: {
                    type: 'time',
                    position: 'top', // x 轴位置
                    axisTick: {
                        // 隐藏刻度
                        show: false
                    },
                    axisLine: {
                        // 隐藏轴线
                        show: false
                    },
                    splitLine: {
                        // 显示网格线
                        show: true
                    }
                },
                yAxis: {
                    inverse: true, // y 轴数据翻转，该操作是为了保证项目一放在最上面，项目七在最下面
                    axisTick: {
                        // 隐藏刻度
                        show: false
                    },
                    axisLine: {
                        // 隐藏轴线
                        show: false
                    },
                    data: this.proName

                },
                series: [
                    {
                        name: '持续时间',
                        type: 'bar',
                        stack: 'duration',
                        itemStyle: {
                            color: '#007acc',
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        zlevel: -1,
                        data: this.end_time // 结束时间
                    },
                    {
                        name: '持续时间',
                        type: 'bar',
                        stack: 'duration', // 堆叠标识符，同个类目轴上系列配置相同的 stack 值可以堆叠放置
                        itemStyle: {
                            color: '#fff'
                        },
                        zlevel: -1, // zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面
                        z: 9, // z值小的图形会被z值大的图形覆盖，z相比zlevel优先级更低，而且不会创建新的 Canvas
                        data: this.start_time // 开始时间
                    }
                ]
            }
            progressChart.setOption(option)
            // 浏览器窗口大小变化，图表大小自适应
            window.addEventListener('resize', () => {
                progressChart.resize()
            })
        }
    }
}
</script>

<style scoped>
#app {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.progressChart {
    width: 100%;
    height: 100vh;
    overflow: scroll;
    border: 1px solid #aaa;
}
</style>
