<template>
    <div class="app-container">
        <div class="filter-container" style="position: relative">
            <el-row :gutter="20">


                <el-col :span="2">
                    <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleFilter">
                        新增
                    </el-button>
                </el-col>

            </el-row>
            <div>
            </div>
        </div>
        <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row
            style="width: 100%;">
            <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="50" />
            <el-table-column label="问题" align="center">
                <template slot-scope="{row}">
                    <span>{{ row.title }}</span>
                </template>
            </el-table-column>
            <el-table-column label="类型" align="center">
                <template slot-scope="{row}">
                    <span v-if='row.type == 1'>评分</span>
                    <span v-if='row.type == 2'>问答</span>
                </template>
            </el-table-column>

            <el-table-column label="项目归档" align="center">
                <template slot-scope="{row}">
                    <el-button type="success" size="mini" @click="edit(row)">
                        编辑
                    </el-button><el-button type="danger" size="mini" @click="del(row)">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
            @pagination="getList" />


        <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
            <el-input class="text_mar" type="textarea" :rows="2" placeholder="请输入问题" v-model="textarea">
            </el-input>
            <div class="radio_box">
                <el-radio v-model="radio1" class="radio_btn" label="1" border>评分</el-radio>
                <el-radio v-model="radio1" class="radio_btn" label="2" border>开放问答</el-radio>
            </div>
            <div slot="footer" class="dialog-footer">

                <el-button @click="dialogFormVisible = false">
                    取消
                </el-button>
                <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
                    提交
                </el-button>
            </div>
        </el-dialog>


    </div>
</template>
<script>
import { questionAdd, questionEdit, questionDel, questionList } from '@/api/project'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
    components: { Pagination },
    filters: {
        statusFilter(status) {
            const statusMap = {
                published: 'success',
                draft: 'info',
                deleted: 'danger'
            }
            return statusMap[status]
        }
    },
    data() {
        return {
            popshow: false,
            listQuery: {
                page: 1,
                page_size: 10,
                name: undefined,
                phone: undefined,
            },
            textMap: {
                update: '编辑问题',
                create: '新增问题'
            },
            dialogStatus: '',
            radio1: '',
            textarea: '',
            list: [],
            total: 0,
            tableKey: 0,
            listLoading: true,
            downloadLoading: false,
            numId: 0,
            dialogFormVisible: false
        }
    },
    created() {
        this.getList()
    },
    methods: {
        // 编辑提交
        updateData() {
            var obj = {
                id: this.edit_id,
                title: this.textarea,
                type: this.radio1
            }
            if (obj.title == '' || obj.type == '') {
                this.$notify({
                    title: 'error',
                    message: '请完善信息',
                    type: 'error',
                    duration: 2000
                })
            } else {
                questionEdit(obj).then(response => {
                    this.$notify({
                        title: 'success',
                        message: '添加成功',
                        type: 'success',
                        duration: 2000
                    })
                })
                this.dialogFormVisible = false
                this.getList()
            }
        },
        // 新增提交
        createData() {
            var obj = {
                title: this.textarea,
                type: this.radio1
            }
            if (obj.title == '' || obj.type == '') {
                this.$notify({
                    title: 'error',
                    message: '请完善信息',
                    type: 'error',
                    duration: 2000
                })
            } else {
                questionAdd(obj).then(response => {
                    this.$notify({
                        title: 'success',
                        message: '添加成功',
                        type: 'success',
                        duration: 2000
                    })
                })
                this.dialogFormVisible = false
                this.getList()
            }
        },
        // 删除问题
        del(e) {
            this.$confirm('此操作将永久删除该问题, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                questionDel({ id: e.id }).then(response => {
                    if (response.meta.status === 200) {
                        this.getList()
                        this.$notify({
                            title: 'Success',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                    }
                })
            }).catch(() => {
                this.$notify({
                    title: 'info',
                    message: '已取消删除',
                    type: 'info',
                    duration: 2000
                })
            })
        },
        // 编辑问题
        edit(e) {
            this.dialogFormVisible = true
            this.dialogStatus = 'update'
            this.textarea = e.title
            this.radio1 = e.type
            this.edit_id = e.id
        },
        // 新增问题
        handleFilter() {
            this.radio1 = ''
            this.textarea = ''
            this.dialogFormVisible = true
            this.dialogStatus = 'create'
        },
        indexAdd(index) {
            const page = this.listQuery.page // 当前页码
            const resize = this.listQuery.page_size // 每页条数
            return index + 1 + (page - 1) * resize
        },
        // 获取偶问题列表
        getList() {
            questionList(this.listQuery).then(response => {
                this.list = response.data.data
                this.total = response.data.total
                setTimeout(() => {
                    this.listLoading = false
                }, 1 * 100)
            })
        },

    }
}
</script>

<style>
.radio_box {
    display: flex;
    justify-content: center;
}

.radio_btn {
    width: 100px;
}

.text_mar {
    margin-bottom: 20px;
}
</style>
