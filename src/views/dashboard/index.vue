<template>
  <div class="dashboard-editor-container">
    <panel-group @handleSetLineChartData="handleSetLineChartData" />
    <user-label/>
    <!-- <div class="summary">
      <Summarys />
    </div> -->
  </div>
</template>

<script>
import PanelGroup from './components/PanelGroup'
import UserLabel from './components/UserLabel'
// import Summarys from './modular/summarys.vue'
import { getCompanyUsers } from '@/api/porject/project'
export default {
  name: 'DashboardAdmin',
  components: {
    PanelGroup,
    UserLabel
  },
  data() {
    return {
      projectManager: [],
      checkWork: [],
      searchTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      searchTimeTwo: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      proTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      user_name: '',
      username: [],
      userName: [],
      year: '',
      years: '2022',
      takeWork: [],
      taskScoring: {},
      proNum: {},
      parameter: {},
      proId: '',
      activeName: 'second'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleSetLineChartData(type) {
      let lineChartData
      this.lineChartData = lineChartData[type]
    },
    getList() {
      this.user_id = localStorage.getItem('userID')
      const time = this.searchTime[0] + '——' + this.searchTime[1]
      const time2 = this.searchTimeTwo[0] + ' 00:00:00' + '——' + this.searchTimeTwo[1] + ' 23:59:59'
      const time3 = this.proTime[0] + ' 00:00:00' + '——' + this.proTime[1] + ' 23:59:59'
      // 考勤
      this.parameter = {
        search_time: time,
        id: this.user_id
      }
      // 任务评分
      this.taskScoring = {
        search_time: time2,
        id: this.user_id
      }
      // 项目完成率
      this.proNum = {
        search_time: time3,
        id: this.user_id
      }
      this.proId = parseInt(localStorage.getItem('userID'))
      this.taskId = parseInt(localStorage.getItem('userID'))
      this.user_name = localStorage.getItem('userName')
      getCompanyUsers().then(response => {
        this.projectManager = response.data
        this.checkWork = response.data
        this.takeWork = response.data
        this.projectManager.forEach(item => {
          if (item.id === this.proId) {
            this.username.push(item.id)
          }
        })
        this.takeWork.forEach(item => {
          if (item.id === this.taskId) {
            this.userName.push(item.id)
          }
        })
      })
    },
    // 任务完成率的change事件
    handleProbability(id) {
      this.proNum.id = id.toString()
      if (this.proNum.id === '') {
        this.proNum.id = this.proId
      }
    },
    // 任务评分change事件
    handleScore(id) {
      this.taskScoring.id = id.toString()
      if (this.taskScoring.id === '') {
        this.taskScoring.id = this.proId
      }
    },
    // 任务时间事件
    handleScoreTime(val) {
      const time = []
      val.forEach(item => {
        time.push(this.formatDate(item))
      })
      this.taskScoring.search_time = time[0] + ' 00:00:00' + '——' + time[1] + ' 23:59:59'
    },
    // 完成率的时间搜索
    handleTime(val) {
      const time = []
      val.forEach(item => {
        time.push(this.formatDate(item))
      })
      this.proNum.search_time = time[0] + ' 00:00:00' + '——' + time[1] + ' 23:59:59'
    },
    // 考勤统计事件
    handleCheck(id) {
      this.parameter.id = id
      if (this.parameter.id === '') {
        this.parameter.id = this.proId
      }
    },
    // 考勤时间搜索
    handleCheckTime(val) {
      const time = []
      val.forEach(item => {
        time.push(this.formatDate(item))
      })
      this.parameter.search_time = time[0] + '——' + time[1]
    },
    // 项目数量事件
    handleFilter(id) {
      this.years = this.formatDate(id).substring(0, 4)
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 10px;
  background-color: rgb(240, 242, 245);
  box-sizing: border-box;
  height: 100vh ;
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
::v-deep #tab-second{
  padding-left:20px ;
}
 
.summary {
  background: #fff;
  margin-top: 10px;
  padding: 10px 0;
  border-top: 1px solid #f1f1f1;
  box-sizing: border-box;
  position: relative;
  height: 15vh;
}
.assembly{
  position: fixed;
  bottom: 0;
}

</style>
