<template>
  <!-- 任务评分 -->
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { getUserTaskGrade } from '@/api/dashboard'

export default {
  mixins: [resize],
  props: {
    task: {
      type: Object,
      default: () => { }
    },
    fourObj: {
      type: Object,
      default: () => { }
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      day: [],
      count: [],
      names: []
    }
  },
  watch: {
    'task': {
      deep: true,
      handler() {
        this.initChart()
      }
    },
    'fourObj': {
      deep: true,
      handler() {
        this.chart.resize()
      }
    },
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      await getUserTaskGrade(this.task).then((res) => {
        this.day = []
        this.count = []
        const tasks = []
        for (const i in res.data) {
          tasks.push(res.data[i])
        }
        tasks[1].forEach(item => {
          this.day.push(item.substring(5, 10))
        })
        for (const i in tasks[0]) {
          this.count.push(tasks[0][i])
        }
        this.count.forEach(item => {
          this.names.push(item.name)
        })
      })
      this.chart.setOption({
        title: {
          text: '任务评分'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: this.names
        },
        toolbox: {
          show: true,
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.day
        },
        yAxis: {
          type: 'value'
        },
        series: this.count
      }, true)
    }
  }
}
</script>
