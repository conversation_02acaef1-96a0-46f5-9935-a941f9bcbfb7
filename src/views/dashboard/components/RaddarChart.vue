<template>
  <!-- 柱形图 -->
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { getCompanyProNum } from '@/api/dashboard'

export default {
  mixins: [resize],
  props: {
    year: {
      type: String,
      default: 'year'
    },
    'fourObj': {
      type: Object,
      default: () => { }
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      count: [],
      acc: []
    }
  },
  watch: {
    'year': {
      deep: true,
      handler() {
        this.initChart()
      }
    },
    'fourObj': {
      deep: true,
      handler() {
        this.chart.resize()
      }
    },
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async initChart() {
      var obj = {
        year: this.year
      }
      if (obj.year === '') {
        obj.year = '2022'
      }
      await getCompanyProNum(obj).then((res) => {
        this.count = []
        this.acc = []
        const arr = []
        for (const i in res.data) {
          arr.push(res.data[i])
        }
        arr.forEach(item => {
          this.acc.push(item.acc)
          this.count.push(item.count)
        })
      })
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        title: {
          text: '项目状况分析',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['每月总数量', '已完成数量']
        },
        toolbox: {
          show: true,
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '每月总数量',
            type: 'bar',
            data: this.count,
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            },
            markLine: {
              data: [{ type: 'average', name: 'Avg' }]
            }
          },
          {
            name: '已完成数量',
            type: 'bar',
            data: this.acc,
            markPoint: {
              data: [
                { name: 'Max', value: 182.2, xAxis: 7, yAxis: 183 },
                { name: 'Min', value: 2.3, xAxis: 11, yAxis: 3 }
              ]
            },
            markLine: {
              data: [{ type: 'average', name: 'Avg' }]
            }
          }
        ]
      })
    }
  }
}
</script>
