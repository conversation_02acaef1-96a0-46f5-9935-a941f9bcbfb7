<template>
  <div class="wrap">
    <!-- 标签弹出框 -->
    <el-dialog v-if="labelRole !== ''" :title="labelRole" :visible.sync="dialogVisible" width="65%" height="1200px">
      <el-table :align="center" :data="labelList" border>
        <el-table-column v-for="(item, index) in labelTitle" :key="index" :align="center" :prop="index" :label="item"/>
      </el-table>
    </el-dialog>
    <el-dialog v-if="labelRole === ''" :visible.sync="dialogVisible" width="65%">
      <el-table :align="center" :data="labelList" border>
        <el-table-column v-for="(item, index) in labelTitle" :key="index" :align="center" :prop="index" :label="item"/>
      </el-table>
    </el-dialog>
    <!-- 下部分数据 -->
    <el-tabs style="background: #FFFFFF; padding: 10px 20px; box-sizing: border-box;" @tab-click="handleClick">
      <el-tab-pane label="ADA助手" style="">
        <div>
          <div class="main">
            <el-row :gutter="20">
              <el-col :span="4">
                <div class="tree">
                  <div
                    style="width: 13vw; height: 72vh; padding: 10px; border: 1px solid #f1f1f1; background: white;overflow: auto;"
                  >
                    <el-input v-model="usertree" placeholder="请选择人员"/>
                    <el-tree
                      ref="tree"
                      style="margin-top: 30px"
                      class="filter-tree"
                      :default-expand-all="true"
                      :data="user_tree"
                      :props="defaultProps"
                      :accordion="true"
                      :filter-node-method="filterNode"
                    >
                      <span slot-scope="{ data }" class="custom-tree-node">
                        <div class="tree-name">{{ data.department_name }}</div>
                        <div
                          v-for="(item, index) in data.user_list"
                          :key="index"
                          class="tree-title"
                          @click="handleOnClick(item)"
                        >{{ item.label }}</div>
                      </span>
                    </el-tree>
                  </div>
                </div>
              </el-col>
              <el-col :span="14" class="left">
                <el-row :gutter="24" style="width: 48vw;">
                  <el-col :span="8">
                    <el-card class="box-card" shadow="hover">
                      <div slot="header" class="clearfix">
                        <span>褒义词</span>
                      </div>
                      <div>
                        <span v-for="(item, index) in userLabel" :key="index">
                          <el-tag
                          v-if="item.status === 1 && item.word_meaning === 3 && item.category <= 4"

                            class="tagItem"
                            type="success"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}1
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 3 && item.category == 5"
                            class="tagItem"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 3 && item.category == 6"
                            class="tagItem"
                            type="danger"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 3 && item.category == 7"
                            class="tagItem"
                            type="info"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                        </span>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card class="box-card" shadow="hover">
                      <div slot="header" class="clearfix">
                        <span>中性词</span>
                      </div>
                      <div>
                        <span v-for="(item, index) in userLabel" :key="index">
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 2 && item.category <= 4"
                            class="tagItem"
                            type="success"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 2 && item.category === 5"
                            class="tagItem"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 2 && item.category === 6"
                            class="tagItem"
                            type="danger"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 2 && item.category === 7"
                            class="tagItem"
                            type="info"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                        </span>
                      </div>
                    </el-card>

                  </el-col>
                  <el-col :span="8">
                    <el-card class="box-card" shadow="hover">
                      <div slot="header" class="clearfix">
                        <span>贬义词</span>
                      </div>
                      <div>
                        <span v-for="(item, index) in userLabel" :key="index">
                          <el-tag
                          v-if="item.status === 1 && item.word_meaning === 1 && item.category <= 4"
                            class="tagItem"
                            type="success"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 1 && item.category === 5"
                            class="tagItem"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 1 && item.category === 6"
                            class="tagItem"
                            type="danger"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                          <el-tag
                            v-if="item.status === 1 && item.word_meaning === 1 && item.category === 7"
                            class="tagItem"
                            type="info"
                            @click.stop="handleLabel(item)"
                          >
                            {{ item.label_name }}
                          </el-tag>
                        </span>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="4">
                <div class="title-all" style="overflow: auto; width: 16vw; height: 72vh">
                  <div class="information">
                    <li>
                      <label class="label">员工姓名:</label>
                      <span class="list">{{ userData.user_username }}</span>
                    </li>
                    <li>
                      <label class="label pro_active">今日任务:</label>
                      <span v-for="(item, index) in userData.today_task" :key="index" class="list active">
                        {{item }}</span>
                    </li>
                    <li>
                      <label class="label pro_active">参与项目:</label>
                      <span v-for="(item, index) in userData.pro_join" :key="index" class="list active">{{
                          item
                        }}</span>
                    </li>
                    <li>
                      <label class="label">联系电话:</label>
                      <span class="list">{{ userData.user_tel }}</span>
                    </li>
                    <li>
                      <label class="label">上班时间:</label>
                      <span class="list">{{ userData.work_time_go }}</span>
                    </li>
                    <li>
                      <label class="label">上班地点:</label>
                      <span class="list">{{ userData.work_location_go }}</span>
                    </li>
                    <li>
                      <label class="label">下班时间:</label>
                      <span class="list">{{ userData.work_time_leave }}</span>
                    </li>
                    <li>
                      <label class="label">下班地点:</label>
                      <span class="list">{{ userData.work_location_leave }}</span>
                    </li>
                  </div>

                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="日常分析" name="second">
        <chart :three-obj="threeObj" :list-query="listQuery" class="chart"/>
      </el-tab-pane>
<!--      <el-tab-pane label="代办事项" style="height: 62vh; overFlow: auto">-->
<!--        <el-tabs v-model="activeName" :tab-position="tabPosition">-->
<!--          <el-tab-pane label="补卡审核" name="second">-->
<!--            <card/>-->
<!--          </el-tab-pane>-->
<!--          <el-tab-pane label="请假审核" name="first">-->
<!--            <leave/>-->
<!--          </el-tab-pane>-->
<!--          <el-tab-pane label="报销审核" name="third">-->
<!--            <expenses/>-->
<!--          </el-tab-pane>-->
<!--          <el-tab-pane label="费用申请" name="fourd">-->
<!--            <applice/>-->
<!--          </el-tab-pane>-->
<!--        </el-tabs>-->
<!--      </el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
import { resultingtree } from '@/api/workAttendance'
import { getUserLabel } from '@/api/dashboard'
import { mapGetters } from 'vuex'
import { getTaskLabel } from '@/api/smartLabel'
import card from './card'
import leave from './leave'
import expenses from './expenses'
import chart from './chart'
import applice from './applice'
export default {
  name: 'Dashboard',
  components: {
    card,
    leave,
    expenses,
    chart,
    applice
  },
  data() {
    return {
      img: '',
      params: {
        // 用于计算图片的缩放，位置等
        zoomVal: 1,
        isDown: false,
        disX: 0,
        disY: 0
      },
      threeObj: {},
      tabPosition: 'left',
      activeName: 'second',
      search_data: [],
      usertree: '',
      user_tree: [],
      center: '',
      listQuery: {
        page: 1,
        page_size: 50,
        end_time: undefined,
        user_id: undefined
      },
      defaultProps: {
        children: 'child',
        label: 'department_name'
      },
      dialogVisible: false,
      userData: [],
      data: [],
      userLabel: [],
      user_id: '',
      labelRole: '',
      labelTitle: [],
      labelList: [],
      proTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      taskScoring: {
        id: localStorage.getItem('userID'),
        search_time: ''
      },
      parameter: {
        id: localStorage.getItem('userID'),
        search_time: ''
      },
      proNum: {
        id: localStorage.getItem('userID'),
        search_time: ''
      },
      tag: {
        1: 'success',
        2: 'danger'
      }
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  watch: {
    usertree(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.parameter.search_time = this.proTime[0] + '00:00:00' + '——' + this.proTime[1] + '23:59:59'
    this.proNum.search_time = this.proTime[0] + '00:00:00' + '——' + this.proTime[1] + '23:59:59'
    this.taskScoring.search_time = this.proTime[0] + '00:00:00' + '——' + this.proTime[1] + '23:59:59'
    this.getList()
    this.getTree()
    this.deFault()
  },
  methods: {
    // 拖动图片
    move(e) {
      e.preventDefault()
      // 获取元素
      var left = document.querySelector('.left')
      var img = document.querySelector('.img')
      var x = e.pageX - img.offsetLeft
      var y = e.pageY - img.offsetTop
      // 添加鼠标移动事件
      left.addEventListener('mousemove', move)

      function move(e) {
        img.style.left = e.pageX - x + 'px'
        img.style.top = e.pageY - y + 'px'
      }

      // 添加鼠标抬起事件，鼠标抬起，将事件移除
      img.addEventListener('mouseup', function() {
        left.removeEventListener('mousemove', move)
      })
      // 鼠标离开父级元素，把事件移除
      left.addEventListener('mouseout', function() {
        left.removeEventListener('mousemove', move)
      })
    },
    handleOnClick(item) {
      this.usertree = item.label
      this.userLabel = []
      getUserLabel({ user_id: item.user_id }).then(response => {
        this.userData = response.data.userData
        for (const list in response.data.label) {
          response.data.label[list].forEach(item => {
            this.userLabel.push(item)
          })
        }
      })
      console.log(this.userLabel, '这是所有标签')
    },
    handleClick(tab) {
      if (tab.index == 1) {
        this.threeObj = {
          name: 1
        }
      }
    },
    filterNode(value, data) {
      console.log(value, 'valuie')
      console.log(data.label.indexOf(value), 'data')
      if (!value) return true
      return data.user_list.label.indexOf(value) !== -1
    },
    getList() {
      if (this.search_data <= 0) {
        this.search_data = [this.formatDate(+new Date()), this.formatDate(+new Date())]
      }
      const userName = localStorage.getItem('userName')
      this.usertree = userName
      this.listLoading = true
    },
    deFault() {
      this.listLoading = true
      this.userLabel = []
      this.user_id = localStorage.getItem('userID')
      getUserLabel({ user_id: this.user_id }).then(response => {
        this.userData = response.data.userData
        for (const list in response.data.label) {
          response.data.label[list].forEach(item => {
            this.userLabel.push(item)
          })
        }
      })

      this.listLoading = false
    },
    handleNodeClick(data) {
      this.listLoading = true
      this.listQuery.user_id = data.user_id
      this.taskScoring.id = data.user_id
      this.parameter.id = data.user_id
      this.proNum.id = data.user_id
      getUserLabel({ user_id: data.user_id }).then(response => {
        this.userLabel = []
        this.userData = response.data.userData
        for (const list in response.data.label) {
          response.data.label[list].forEach(item => {
            this.userLabel.push(item)
          })
        }
        setTimeout(() => {
          this.listLoading = false
        }, 500)
      })
    },
    handleFilter() {
      if (this.usertree === '') {
        this.listQuery.user_id = undefined
      }
      this.listQuery.page = 1
      if (this.listQuery.end_time != null) {
        this.listQuery.end_time = ''
        this.listQuery.search_time = ''
      }
      this.getList()
    },
    getTree() {
      this.listLoading = true
      resultingtree(this.listQuery).then(response => {
        this.user_tree = response.data
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleChild(list) {
      list.forEach((item, index) => {
        if (item.child) {
          item.children = JSON.parse(JSON.stringify(item.child))
          this.handleChild(item.children)
        }
      })
    },
    handleData(list) {
      if (list.length) {
        list.map(item => {
          if (item.user_list) {
            item.user_list.map(itm => {
              itm.child = item.child
              this.handleData(itm.child)
            })
          }
        })
      }
    },
    handleDatas(list) {
      list.map(it => {
        const a = []
        it.child.map(i => {
          if (i.user_list) {
            a.push(...i.user_list)
          }
        })
        it.child = a
        this.handleDatas(it.child)
      })
    },
    // 标签事件
    handleLabel(e) {
      this.labelList = []
      this.labelRole = ''
      this.labelTitle = []
      getTaskLabel({ label_id: e.id }).then(response => {
        if (response.meta.status === 404 || response.data.length === 0) {
          this.labelList = []
          this.labelRole = ''
          this.labelTitle = []
          return
        } else {
          this.labelList = response.data.data
          this.labelRole = '标签规则：' + response.data.role
          this.labelTitle = response.data.title
        }
      })
      this.dialogVisible = true
    },
    // 时间戳转换日期格式方法
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.left {
  position: relative;
  top: 0;
  width: 50vw;
  height: 77vh;
  background-color: transparent;
  padding-top: 20px;
  margin: 0 10px;
}

.img {
  position: absolute;
  top: 25vh;
  right: 0;
  width: 25vw;
  cursor: move;
}

::v-deep .el-table th > .cell {
  text-align: center;
}

::v-deep .el-table td > .cell {
  text-align: center;

}

.wrap {
  background: rgb(238, 240, 243);

  .header {
    width: 100%;
    padding-top: 20px;
    padding-left: 20px;
  }

  .main {
    // justify-content: space-between;
    flex-wrap: wrap;
    background: white;

    .el-row {
      height: 570px;
    }

    .tree {
      background: white;
      padding: 10px;
      border: 0;
    }

    .images {
      margin-top: -60px;
      text-align: center;

      :hover {
        cursor: pointer
      }

      img {
        width: 600px;
        margin-top: -10vh;

      }
    }

    .title-all {
      width: 300px;
      height: 600px;
      padding: 10px;
      border: 1px solid #f1f1f1;
      background: white;
      margin-right: 10px;

      .tag {
        margin: 10px;
      }
    }
  }
}

.chart {
  height: 62vh;
}

li {
  list-style-type: none;
  padding: 10px 0;
  border-bottom: 1px solid #f1f1f1;

  .label {
    font-size: 12px;
    padding-left: 10px;
    color: #555;
  }

  .list {
    font-size: 12px;
    padding: 0 10px;
  }

  .active {
    padding-top: 10px;
    width: 164px;
    padding-left: 20px;
    text-align: left;
    display: inline-block;
  }
}

.pro_active {
  display: block;
  padding-bottom: 10px;
}

.tagList {
  width: 450px;
  margin-top: 40px;

  .tagItem {
    margin: 5px 8px;
    padding: 0 10px;
    font-size: 12px;
    z-index: 999;
  }
}

.proCharts {
  width: 600px;
  height: 400px;
  background: #ccc;
}

.tree {
  background: white;
  padding: 10px;
  border: 0;
}

::v-deep .el-tree-node__content {
  height: auto !important;
  position: relative;
  margin-left: 30px;
}

.tree-name {
  font-size: 13px;
  font-weight: bold;
}

.tree-title {
  padding: 5px 10px;
  font-size: 12px;
  text-align: center;
}

.custom-tree-node {
  padding: 5px 0;
}

::v-deep .el-tree-node__expand-icon {
  position: absolute;
  top: 0;
  left: 0;
}

.tabs {
  border: 1px solid #ccc;
}

.el-tag {
  margin: 5px;
}

.words {
  display: block;
  font-size: 12px;
  margin: 10px 0;
  text-align: center;
  font-weight: bold;
}
</style>
