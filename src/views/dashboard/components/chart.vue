<template>
  <div class="dashboard-editor-container">

    <el-row style="background:#fff;margin-bottom:2px; ">
      <el-date-picker
        v-model="proTime"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="margin-bottom: 30px"
        @change="handleTime"
      />
      <el-select
        v-model="username"
        multiple
        style="width: 300px; padding-left:10px"
        filterable
        class="filter-item"
        placeholder="请选择"
        @change="handleProbability"
      >
        <el-option v-for="item in projectManager" :key="item.value" :label="item.user_username" :value="item.id" />
      </el-select>
      <line-chart :four-obj="fourObj" :pro-num="proNum" />
    </el-row>
    <el-row :gutter="32" style="background:#fff; padding: 10px 0">
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="block">
          <el-date-picker
            v-model="years"
            style="width: 140px;"
            type="year"
            :editable="false"
            :clearable="false"
            placeholder="请选择年份"
            @change="handleFilter"
          />
        </div>
        <div class="chart-wrapper">
          <raddar-chart :four-obj="fourObj" :year="years" />
        </div>
      </el-col>
<!--      <el-col :xs="24" :sm="24" :lg="8">-->
<!--        <div class="block">-->
<!--          <el-date-picker-->
<!--            v-model="searchTime"-->
<!--            type="daterange"-->
<!--            range-separator="至"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--            @change="handleCheckTime"-->
<!--          />-->
<!--          <el-select-->
<!--            v-model="user_name"-->
<!--            placeholder="选择人员"-->
<!--            clearable-->
<!--            filterable-->
<!--            style="width: 120px; padding-left:10px;padding-right: 5px"-->
<!--            class="filter-item"-->
<!--            @change="handleCheck"-->
<!--          >-->
<!--            <el-option v-for="(item, i) in projectManager" :key="i" :label="item.user_username" :value="item.id" />-->
<!--          </el-select>-->
<!--        </div>-->
<!--        <div class="chart-wrapper">-->
<!--          <pie-chart :four-obj="fourObj" :parameter="parameter" />-->
<!--        </div>-->
      <!-- </el-col> -->
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="block">
          <el-date-picker
            v-model="searchTimeTwo"
            collapse-tags
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleScoreTime"
          />
          <el-select
            v-model="userName"
            multiple
            collapse-tags
            style="width: 165px; padding-left:10px"
            class="filter-item"
            placeholder="请选择"
            @change="handleScore"
          >
            <el-option v-for="(item, i) in takeWork" :key="i" :label="item.user_username" :value="item.id" />
          </el-select>
        </div>
        <div class="chart-wrapper">
          <bar-chart :four-obj="fourObj" :task="taskScoring" />
        </div>
      </el-col>
    </el-row>

  </div>

</template>

<script>
import LineChart from './LineChart.vue'
import PanelGroup from './PanelGroup'
import RaddarChart from './RaddarChart'
import PieChart from './PieChart'
import BarChart from './BarChart'
import { getCompanyUsers } from '@/api/porject/project'

export default {
  name: 'DashboardAdmin',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart
  },
  props: {
    listQuery: {
      type: Object,
      default: () => {
      }
    },
    threeObj: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      fourObj: {},
      projectManager: [],
      checkWork: [],
      searchTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      searchTimeTwo: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      proTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      user_name: '',
      username: [localStorage.getItem('userID')],
      userName: [localStorage.getItem('userID')],
      year: '',
      years: '2022',
      takeWork: [],
      taskScoring: {},
      proNum: {},
      parameter: {},
      proId: '',
      activeName: 'second'
    }
  },
  watch: {
    'listQuery': {
      deep: true,
      handler() {
        this.username.push(this.listQuery.user_id)
        this.userName.push(this.listQuery.user_id)
        this.getList(this.listQuery.user_id)
      }
    },
    'threeObj': {
      deep: true,
      handler() {
        this.fourObj = this.threeObj
      }
    }
  },
  created() {
    this.getList(localStorage.getItem('userID'))
  },
  methods: {
    getList(e) {
      this.user_id = e
      const time = this.searchTime[0] + '——' + this.searchTime[1]
      const time2 = this.searchTimeTwo[0] + ' 00:00:00' + '——' + this.searchTimeTwo[1] + ' 23:59:59'
      const time3 = this.proTime[0] + ' 00:00:00' + '——' + this.proTime[1] + ' 23:59:59'
      // 考勤
      this.parameter = {
        search_time: time,
        id: this.user_id
      }
      // 任务评分
      this.taskScoring = {
        search_time: time2,
        id: this.user_id
      }
      // 项目完成率
      this.proNum = {
        search_time: time3,
        id: this.user_id
      }
      this.proId = e
      this.taskId = e
      this.userName = [localStorage.getItem('userName')]
      this.username = [localStorage.getItem('userName')]
      this.user_name = localStorage.getItem('userName')
      getCompanyUsers().then(response => {
        this.projectManager = response.data
        this.checkWork = response.data
        this.takeWork = response.data
        this.projectManager.forEach(item => {
          if (item.id === this.proId) {
            this.username = []
            this.username.push(item.id)
            this.user_name = item.user_username
          }
        })
        this.takeWork.forEach(item => {
          if (item.id === this.taskId) {
            this.userName = []
            this.userName.push(item.id)
          }
        })
      })
    },
    // 任务完成率的change事件
    handleProbability(id) {
      this.proNum.id = id.toString()
      if (this.proNum.id === '') {
        this.proNum.id = this.proId
      }
    },
    // 任务评分change事件
    handleScore(id) {
      this.taskScoring.id = id.toString()
      if (this.taskScoring.id === '') {
        this.taskScoring.id = this.proId
      }
    },
    // 任务时间事件
    handleScoreTime(val) {
      const time = []
      val.forEach(item => {
        time.push(this.formatDate(item))
      })
      this.taskScoring.search_time = time[0] + ' 00:00:00' + '——' + time[1] + ' 23:59:59'
    },
    // 完成率的时间搜索
    handleTime(val) {
      const time = []
      val.forEach(item => {
        time.push(this.formatDate(item))
      })
      this.proNum.search_time = time[0] + ' 00:00:00' + '——' + time[1] + ' 23:59:59'
    },
    // 考勤统计事件
    handleCheck(id) {
      this.parameter.id = id
      if (this.parameter.id === '') {
        this.parameter.id = this.proId
      }
    },
    // 考勤时间搜索
    handleCheckTime(val) {
      const time = []
      val.forEach(item => {
        time.push(this.formatDate(item))
      })
      this.parameter.search_time = time[0] + '——' + time[1]
    },
    // 项目数量事件
    handleFilter(id) {
      this.years = this.formatDate(id).substring(0, 4)
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  background-color: rgb(240, 242, 245);
  position: relative;
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

::v-deep #tab-second {
  padding-left: 20px;
}

.block {
  padding-bottom: 10px;
}
</style>
