<template>
  <!-- 考勤统计 -->
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { getAttendanceList } from '@/api/dashboard'
// import SidebarItemVue from '@/layout/components/Sidebar/SidebarItem.vue'
export default {
  mixins: [resize],
  props: {
    parameter: {
      type: Object,
      default: () => {}
    },
    fourObj: {
      type: Object,
      default: () => { }
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      obje: {},
      searchTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      day: [],
      morning: [],
      after: []
    }
  },
  watch: {
    parameter: {
      deep: true,
      immediate: true,
      handler() {
        this.initChart()
      }
    },
    'fourObj': {
      deep: true,
      handler() {
        this.chart.resize()
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
  },
  methods: {
    async initChart() {
      await getAttendanceList(this.parameter).then((res) => {
        this.day = []
        this.morning = []
        this.after = []
        res.data.date.forEach((item) => {
          this.day.push(item.substring(5, 10))
        })
        for (const i in res.data.morning) {
          res.data.morning[i].forEach(item => {
            this.morning.push(item.substring(0, 5))
          })
        }
        for (const i in res.data.after) {
          res.data.after[i].forEach(item => {
            this.after.push(item.substring(0, 5))
          })
        }
      })
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        title: {
          text: '本周考勤统计'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {},
        toolbox: {
          show: true,
          feature: {
            dataView: { readOnly: false }, // 数据视图
            magicType: { type: ['line', 'bar'] }, // 柱形图折线图转换
            restore: {}, // 重置
            saveAsImage: {}// 下载图片
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.day
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}:00'
          }
        },
        series: [
          {
            name: '上班打卡',
            type: 'line',
            data: this.morning,
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            },
            markLine: {
              data: [{ type: 'average', name: 'Avg' }]
            }
          },
          {
            name: '下班打卡',
            type: 'line',
            data: this.after,
            markPoint: {
              data: [{ name: '周最低', value: -2, xAxis: 1, yAxis: -1.5 }]
            },
            markLine: {
              data: [
                { type: 'average', name: 'Avg' },
                [
                  {
                    symbol: 'none',
                    x: '90%',
                    yAxis: 'max'
                  },
                  {
                    symbol: 'circle',
                    label: {
                      position: 'start',
                      formatter: 'Max'
                    },
                    type: 'max',
                    name: '最高点'
                  }
                ]
              ]
            }
          }
        ]
      })
    },
    getList() {
    },
    formatDate(value) { // 时间戳转换日期格式方法
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>
