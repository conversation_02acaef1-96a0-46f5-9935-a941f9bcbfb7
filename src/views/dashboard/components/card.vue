<template>
  <div class="app-container">
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column label="序号" prop="index" type="index" sortable="custom" align="center" width="80" :class-name="getSortClass('id')">
        <!-- <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template> -->
      </el-table-column>
      <el-table-column label="申请人" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_id }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="类型" align="center">
        <template slot-scope="{row}">
          <span>{{ row.time }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="摘要" align="center">
        <template slot-scope="{row}">
          <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;">{{ row.reason }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="驳回理由" align="center">
        <template slot-scope="{row}">
          <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;">{{ row.refuse_reason }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="提交时间" align="center">
        <template slot-scope="{row}">
          <span style="">{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="补签时间" align="center">
        <template slot-scope="{row}">
          <span style="">{{ row.retroactive_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" width="100px" class-name="status-col">
        <template slot-scope="{row}">
          <el-tag v-if="row.status == '待审核'" type="danger">
            {{ row.status }}
          </el-tag>
          <el-tag v-if="row.status == '审核通过'" type="success">
            {{ row.status }}
          </el-tag>
          <el-tag v-if="row.status == '审核驳回'" type="warning">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="$t('table.actions')" align="center" width="200px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button v-if="row.status == '待审核'" type="text" size="mini" @click="handleAdopt(row)">
            通过
          </el-button>
          <el-button v-if="row.status =='待审核'" size="mini" type="text" @click="handleReject(row)">
            驳回
          </el-button>
          <el-button v-if="row.status !== '待审核'" type="text" size="mini" disabled @click="handleAdopt(row)">
            通过
          </el-button>
          <el-button v-if="row.status !=='待审核'" size="mini" disabled type="text" @click="handleReject(row)">
            驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size" @pagination="getList" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="80px" style="width: 430px; margin-left:50px;">
        <el-form-item label="驳回理由" prop="refuse_reason" class="labelName">
          <el-input
            v-model="temp.refuse_reason"
            style="width: 300px"
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="rejectData">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanyUsers } from '@/api/porject/project'
import { reissueCardList, reissueCardActive } from '@/api/workAttendance'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

const calendarTypeOptions = [
  { key: 'CN', display_name: 'China' },
  { key: 'US', display_name: 'USA' },
  { key: 'JP', display_name: 'Japan' },
  { key: 'EU', display_name: 'Eurozone' }
]

// arr to obj, such as { CN : "China", US : "USA" }
const calendarTypeKeyValue = calendarTypeOptions.reduce((acc, cur) => {
  acc[cur.key] = cur.display_name
  return acc
}, {})

export default {
  name: 'Setcard',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    },
    typeFilter(type) {
      return calendarTypeKeyValue[type]
    }
  },
  data() {
    return {
      popshow: false,
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        end_time: undefined,
        user_id: undefined,
        status: 1,
        search_time: null
      },
      listQueryAdd: {
        id: '',
        status: ''
      },

      // searchTime1: '',
      listData: {
        page: 1,
        page_size: 10
      },
      options: [],
      importanceOptions2: [
        {
          status: '待审核',
          id: '1'
        },
        {
          status: '已审核',
          id: '2'
        },
        {
          status: '已驳回',
          id: '3'
        }
      ],
      calendarTypeOptions,
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      statusOptions: ['published', 'draft', 'deleted'],
      showReviewer: false,
      temp: {
        id: '',
        status: 3,
        refuse_reason: ''
      },
      textMap: {
        reject: '驳回请假'

      },
      rules: {
        refuse_reason: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' }
        ]
      },
      dialogStatus: '',
      dialogFormVisible: false,
      dialogPvVisible: false,
      pvData: [],
      downloadLoading: false,
      numId: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },

    getList() {
      if (this.listQuery.search_time) {
        this.listQuery.end_time = this.listQuery.search_time[0] + '——' + this.listQuery.search_time[1]
      }
      this.listLoading = true
      reissueCardList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      if (this.listQuery.search_time == null) {
        this.listQuery.end_time = ''
      }
    },
    handleModifyStatus(row, status) {
      this.$message({
        message: '操作Success',
        type: 'success'
      })
      row.status = status
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: '',
        status: 3,
        refuse_reason: ''
      }
    },
    handleReject(row) {
      this.resetTemp()
      this.temp.id = row.id
      this.dialogStatus = 'reject'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    rejectData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          reissueCardActive({ id: this.temp.id, status: 3, refuse_reason: this.temp.refuse_reason }).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: '驳回成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleAdopt(row) {
      this.$confirm('是否确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reissueCardActive({ id: row.id, status: 2 }).then(response => {})
        this.$message({
          type: 'success',
          message: '审核通过!'
        })
        this.getList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消审核'
        })
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['序号', '申请人', '类型', '摘要', '驳回理由', '提交时间', '补签时间', '审核状态']
        const filterVal = ['user_id', 'time', 'reason', 'refuse_reason', 'created_at', 'retroactive_time', 'status']
        const data = this.formatJson(filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '补卡列表'
        })
        this.downloadLoading = false
      })
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    getSortClass: function(key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item{
  margin-bottom: 0px;
  margin-left: 10px;
}
.container {
  padding: 0;
  margin: 0;
}

</style>
