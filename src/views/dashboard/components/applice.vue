<template>
  <div class="app-container">
    <el-table
      :key="tableKey"
      ref="multipleTable"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="序号"
        align="center"
        type="index"
        :index="indexAdd"
        width="50"
      />
      <el-table-column label="项目名称" width="150px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.pro_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" width="70px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.user_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销明细" align="center">
        <template slot-scope="{ row }">
          <span
            style="
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            "
          >{{ row.reason }}</span>
        </template>
      </el-table-column>
      <el-table-column label="金额" width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.money }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销类型" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.category_name  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报销凭证" width="270px" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.cost_photo.length" class="prove">
            <div v-for="(v, k) in row.cost_photo" :key="k" class="voucher">
              <el-image
                style="width: 50px; height: 50px"
                :src="v"
                :scroll-container="scrollContainer"
                :preview-src-list="srcList"
                @click="big_image(v)"
              />
            </div>
          </div>
          <div v-if="row.cost_proof.length > 0" style="display: flex;justify-content: space-around;">
            <div
              v-for="(file_item, filedex) in row.cost_proof"
              :key="filedex"
            >
              <i class="el-icon-document-remove" /> <a :href="file_item" title="点击预览打印" target="_blank">报销凭证</a>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="部门审核人" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.expense_examiner }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门审核" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.finance_check }}</span>
        </template>
      </el-table-column>
      <el-table-column label="财务审核" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.status }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="驳回原因" width="200px" align="center">
        <template slot-scope="{ row }">
          <span
            style="
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            "
          >{{ row.remarks }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" class-name="status-col" width="150px">
        <template slot-scope="{ row }">
          <div v-if="row.finance_check == '已通过' && row.status== '待审核'">
            <el-button type="text" size="mini" @click="costApplyAudit(row)">
              通过
            </el-button>
            <el-button size="mini" type="text" @click="handleUpdate(row)">
              驳回
            </el-button>
          </div>
          <div v-else>
            <el-button type="text" disabled size="mini" @click="costApplyAudit(row)">
              通过
            </el-button>
            <el-button size="mini" disabled type="text" @click="handleUpdate(row,$index)">
              驳回
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="70px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="驳回原因">
          <el-input
            v-model="temp.reason"
            props="reason"
            style="margin-top: 15px"
            type="textarea"
            placeholder="请输入驳回意见"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消</el-button>
        <el-button
          type="primary"
          @click="updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {  getReimbursementType, costApplyAudit, costApplyList } from '@/api/finance'
import { getproList, searchCompany } from '@/api/porject/project'
import { getAllUserInfo } from '@/api/user'
import Pagination from '@/components/Pagination'

export default {
  name: 'applice',
  components: { Pagination },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      receiveId: [],
      scrollContainer: HTMLCollection,
      srcList: [' '],
      tableKey: 0,
      list: [],
      multipleSelection: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        expense_type: undefined,
        company_id: undefined,
        project_id: undefined,
        create_by: undefined,
        status: 1,
        pro_end_time: undefined,
        sort: undefined,
        finance_check: 2
      },
      searchTime: null,
      listData: {
        page: 1,
        page_size: 20
      },
      importanceOptions: [],
      importanceOptions1: [],
      importanceOptions2: [
        {
          status: '待审核',
          id: '1'
        },
        {
          status: '已通过',
          id: '2'
        },
        {
          status: '拒绝',
          id: '3'
        }
      ],
      importanceOptions3: [],
      importanceOptions4: [],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        type: '',
        reason: '',
        status: 'published'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        examine: '审核驳回'
      },
      dialogPvVisible: false,
      pvData: [],
      rules: {
        type: [
          { required: true, message: 'type is required', trigger: 'change' }
        ],
        timestamp: [
          {
            type: 'date',
            required: true,
            message: 'timestamp is required',
            trigger: 'change'
          }
        ],
        reason: [
          { required: true, message: '请添加驳回原因', trigger: 'blur' }
        ]
      },
      downloadLoading: false,
      numId: 0
    }
  },
  created() {
    this.getList()
    this.searchList()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = []
      for (var i = 0; i < val.length; i++) {
        this.multipleSelection.push(val[i].id)
      }
      return this.multipleSelection
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
    },

    // 批量通过
    handleAllUpdate() {
      this.multipleSelection = this.multipleSelection.join(',')
      costApplyAudit({ id: this.multipleSelection, finance_check: 2 }).then(response => {
        if (response.meta.status === 200) {
          this.$notify({
            title: 'Success',
            message: '审核通过',
            type: 'success',
            duration: 2000
          })
        }
        this.getList()
      })
    },
    getSortClass: function(key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    big_image(item) {
      this.srcList[0] = item
    },
    getList() {
      if (this.searchTime) {
        this.listQuery.pro_end_time =
          this.searchTime[0] + '——' + this.searchTime[1]
      }
      if (this.receiveId) {
        this.listQuery.create_by = this.receiveId.join(',')
      }
      this.listLoading = true
      costApplyList(this.listQuery).then((response) => {

        response.data.data.forEach((item) => {
          if (item.cost_photo === '') {
            item.cost_photo = []
          } else {
            item.cost_photo = item.cost_photo.split(',')
          }
          if (item.cost_proof === '') {
            item.cost_proof = []
          } else {
            item.cost_proof = item.cost_proof.split(',')
          }
        })
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    searchList() {
      searchCompany(this.listData).then((response) => {
        this.importanceOptions = response.data.data
      })
      getproList(this.listData).then((response) => {
        this.importanceOptions3 = response.data.data
      })
      getAllUserInfo().then((response) => {
        this.importanceOptions4 = response.data
      })
      getReimbursementType({type: 1}).then((response) => {
        this.importanceOptions1 = response.data
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      if (this.searchTime == null) {
        this.listQuery.pro_end_time = ''
      }
    },
    costApplyAudit(row) {
      this.$confirm('是否确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        costApplyAudit({ id: row.id, status: 2 }).then(response => {})
        this.$message({
          type: 'success',
          message: '审核通过!'
        })
        this.getList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消审核'
        })
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        status: 'published',
        type: ''
      }
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.timestamp = new Date(this.temp.timestamp)
      this.dialogStatus = 'examine'
      this.dialogFormVisible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const obj = { id: this.temp.id, status: 3, pass_reason: this.temp.reason }
          if (this.temp.reason !== '') {
            costApplyAudit(obj).then((response) => {
              if (response.meta.status === 200) {
                this.$notify({
                  title: 'Success',
                  message: '驳回成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.getList()
              }
            })
          }
        }
      })
    },
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 10px;
}
.prove {
  display: flex;
  flex-wrap: wrap;
  margin: 10px;
}
.voucher {
  margin-right: 5px;
}
</style>
