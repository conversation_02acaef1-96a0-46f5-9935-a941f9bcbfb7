<template>
  <div>
    <el-row :gutter="40" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="peoples" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              在职人员
            </div>
            <count-to :start-val="0" :end-val="svgData.userCount" :duration="2600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('messages')">
          <div class="card-panel-icon-wrapper icon-message">
            <svg t="1657592364179" icon-class="message" class-name="card-panel-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2389" width="50" height="50"><path d="M0 0m0 0l1024 0q0 0 0 0l0 1024q0 0 0 0l-1024 0q0 0 0 0l0-1024q0 0 0 0Z" fill="#E8EFF8" p-id="2390" /><path d="M139.636364 651.636364a23.272727 23.272727 0 0 1 23.272727-23.272728h698.181818a23.272727 23.272727 0 0 1 23.272727 23.272728v116.363636a23.272727 23.272727 0 0 1-23.272727 23.272727H162.909091a23.272727 23.272727 0 0 1-23.272727-23.272727v-116.363636z" fill="#247ADE" p-id="2391" /><path d="M139.636364 442.181818a23.272727 23.272727 0 0 1 23.272727-23.272727h395.636364a23.272727 23.272727 0 0 1 23.272727 23.272727v116.363637a23.272727 23.272727 0 0 1-23.272727 23.272727H162.909091a23.272727 23.272727 0 0 1-23.272727-23.272727v-116.363637zM581.818182 232.727273a23.272727 23.272727 0 0 0-23.272727-23.272728H162.909091a23.272727 23.272727 0 0 0-23.272727 23.272728v116.363636a23.272727 23.272727 0 0 0 23.272727 23.272727h395.636364a23.272727 23.272727 0 0 0 23.272727-23.272727v-116.363636z" fill="#A0BFF7" p-id="2392" /><path d="M768 488.727273a116.363636 116.363636 0 1 0 0-232.727273 116.363636 116.363636 0 0 0 0 232.727273z" fill="#69CB91" p-id="2393" /></svg>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              进行中项目
            </div>
            <count-to :start-val="0" :end-val="svgData.proCount" :duration="3000" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('purchases')">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="money" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              本月报销总金额
            </div>
            <count-to :start-val="0" :end-val="svgData.moneyCount" :duration="3200" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('purchases')">
          <div class="card-panel-icon-wrapper icon-shopping">
            <!-- <svg-icon icon-class="shopping" class-name="card-panel-icon" /> -->
            <svg t="1657592413912" icon-class="shopping" class-name="card-panel-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4201" width="50" height="50"><path d="M273.408 207.36h492.032c38.912 0 70.144 31.232 70.144 70.144v492.032c0 38.912-31.232 70.144-70.144 70.144H273.408c-38.912 0-70.144-31.232-70.144-70.144V277.504c0-38.912 31.232-70.144 70.144-70.144z" fill="#CAE4FF" p-id="4202" /><path d="M160.768 127.488c-16.896-1.024-31.744 11.776-32.768 29.184v725.504c-1.024 16.896 11.776 31.744 29.184 32.768h725.504c16.896 1.024 31.744-11.776 32.768-29.184V160.256c1.024-16.896-11.776-31.744-29.184-32.768h-3.584-721.92z m0-65.536h721.92c53.76 1.536 97.28 44.544 98.304 98.304v721.92c-1.536 53.76-44.544 97.28-98.304 98.304h-721.92c-53.76-1.536-97.28-44.544-98.304-98.304v-721.92c1.024-53.76 44.544-96.768 98.304-98.304z m393.728 262.656h196.608c16.896-1.024 31.744 11.776 32.768 29.184v3.584c1.024 16.896-11.776 31.744-29.184 32.768h-200.192c-16.896 1.024-31.744-11.776-32.768-29.184v-3.584c-1.024-16.896 11.776-31.744 29.184-32.768h3.584z m-197.12 393.728c17.92 0 32.768-14.848 32.768-32.768 0-17.92-14.848-32.768-32.768-32.768-17.92 0-32.768 14.848-32.768 32.768 0 17.92 14.848 32.768 32.768 32.768z m0 65.536c-54.272 0-98.304-44.032-98.304-98.304s44.032-98.304 98.304-98.304S455.68 631.296 455.68 685.568s-43.52 98.304-98.304 98.304z m-6.144-400.384L279.04 311.296c-13.312-12.288-33.792-11.776-46.08 1.536-11.776 12.288-11.776 31.744 0 44.544l91.648 91.648c7.168 6.656 16.384 11.264 26.112 13.312 9.216 1.024 18.432-1.536 26.112-6.656l137.728-137.728c12.8-12.8 12.8-33.28 0-46.08s-33.28-12.8-46.08 0L351.232 383.488z m203.264 268.8h196.608c17.92 0 32.768 14.848 32.768 32.768 0 17.92-14.848 32.768-32.768 32.768h-196.608c-17.92 0-32.768-14.848-32.768-32.768 0-17.92 14.848-32.768 32.768-32.768z" fill="#0972E7" p-id="4203" /></svg>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              本日任务完成状况
            </div>
            <count-to :start-val="0" :end-val="svgData.taskNum" :duration="3600" class="card-panel-num" /><span style="font-size: 20px;">/</span>
            <count-to :start-val="0" :end-val="svgData.taskCount" :duration="3600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import { getUserNum } from '@/api/dashboard'
export default {
  components: {
    CountTo
  },
  data() {
    return {
      svgData: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    getList() {
      getUserNum().then((res) => {
        this.svgData = res.data
        // console.log(res.data, '本日任务完成状况')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  .card-panel-col {
    margin-bottom: 10px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .card-panel-col {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 26px 26px 0px;
      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
