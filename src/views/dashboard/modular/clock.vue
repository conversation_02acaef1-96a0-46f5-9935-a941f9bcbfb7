<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="clock" />
        <span class="clock">考勤打卡</span>
      </div>
    </el-card>
    <el-dialog
      title="疫情防控"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="35%"
    >

      <view>
        <div>
          <input v-model="customAddress">
          <a @click="addressToPoint">定位</a>
        </div>
        <div>
          {{ address }}
        </div>
        <div>
          {{ location }}
        </div>
        <a @click="getLocation">点击获取位置</a>
        <div id="container" style="height: 500px;width: 100%" />
      </view>

      <!-- <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <span>slot-scope=""</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
      </el-form> -->
    </el-dialog>
  </div>
</template>
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=ff97dpc4n6sKkhXgFLaRXjSkXCp97UlW"></script>
<script>
export default {
  name: 'Clock',
  data() {
    return {
      BMap: null,
      location: null,
      address: '',
      customAddress: '',
      dialogVisible: false
    }
  },
  methods: {
    handleOpen() {
      this.dialogVisible = true
      this.initMap()
    },
     // 获取地理定位
      getLocation() {
        let _this = this
        navigator.geolocation.getCurrentPosition(function (position) {
            let centerPointer = new BMap.Point(position.coords.longitude, position.coords.latitude)
            let convertor = new BMap.Convertor()
            let pointArr = []
            pointArr.push(centerPointer)
            convertor.translate(pointArr, 1, 5, function (data) {
                if(data.status==0){
                    let marker = new BMap.Marker(data.points[0])
                    _this.BMap.centerAndZoom(data.points[0],17)
                    _this.BMap.addOverlay(marker)
                    var myGeo = new BMap.Geocoder({extensions_town: true})
                    // 根据坐标得到地址描述
                    myGeo.getLocation(data.points[0], function(result){
                        if (result){
                            _this.address = result.addressComponents.city + result.addressComponents.district + result.addressComponents.town
                        }
                    })
                }
            })
        }, function(e) {
            _this.address = "获取定位位置信息失败：" + e.message
        }, {
            geocode: true
        }) },
            addressToPoint(){
                //创建地址解析器实例
                let myGeo = new BMap.Geocoder()
                let _this = this
                let address = _this.customAddress
                if (!address){
                    address = '川杨新苑四期'
                }
                // 将地址解析结果显示在地图上，并调整地图视野
                myGeo.getPoint(address, function(point){
                    if(point){
                        _this.BMap.centerAndZoom(point, 17)
                        _this.BMap.addOverlay(new BMap.Marker(point, {title: address}))
                        var myGeo = new BMap.Geocoder({extensions_town: true})
                        // 根据坐标得到地址描述
                        myGeo.getLocation(point, function(result){
                            if (result){
                                _this.address = result.addressComponents.city + result.addressComponents.district + result.addressComponents.town
                            }
                        })
                    }else{
                        alert('您选择的地址没有解析到结果！')
                    }
                }, '上海市')
            },
            initMap(){
                let map = new BMap.Map('container')
                map.centerAndZoom('上海', 11)
                //开启鼠标滚轮缩放
                map.enableScrollWheelZoom(true)
                this.BMap = map
                this.getLocation()
            }
        }
  }

</script>

<style lang="scss" scoped>
.search-icon {
  font-size: 40px;
  display: block;
  color: cadetblue;
}
.clock {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: 0 auto;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.el-card{
  border-radius: 15px;
}
div{
        word-break: break-all
 }
</style>
