<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="fee" />
        <span class="reimbursement">费用申请</span>
      </div>
    </el-card>

    <el-dialog
      title="费用申请"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="40%"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="10">
            <el-form-item label="关联项目" prop="pro_id">
              <el-select
                v-model="temp.pro_id"
                placeholder="请选择关联项目"
                clearable
                style="width: 205px"
                class="filter-item"
              >
                <el-option v-for="item in options" :key="item.id" :label="item.pro_name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="费用类别" prop="category_id">
              <el-select
                v-model="temp.category_id"
                placeholder="请选择费用类型"
                clearable
                style="width: 205px"
                class="filter-item"
              >
                <el-option v-for="item in list" :key="item.id" :label="item.expense_type" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="10">
            <el-form-item label="费用金额" prop="money">
              <el-input v-model="temp.money" placeholder="请输入费用金额" type="number" style="width: 205px" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="审批人" prop="expense_examiner">
              <el-select
                v-model="temp.expense_examiner"
                placeholder="请选择费用审批人"
                clearable
                style="width: 205px"
                class="filter-item"
              >
                <el-option v-for="item in userName" :key="item.id" :label="item.user_username" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="10">
            <el-form-item label="客户" prop="customer_id">
              <el-select
                v-model="temp.customer_id"
                placeholder="请选择往来客户"
                clearable
                style="width: 205px"
                class="filter-item"
              >
                <el-option v-for="item in customer" :key="item.id" :label="item.customer_name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="申请时间" prop="filing_date">
              <el-date-picker
                v-model="temp.filing_date"
                type="date"
                placeholder="选择日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="10">
            <el-form-item label="申请事由" prop="reason">
              <el-input v-model="temp.reason" clearable type="textarea" :rows="2" placeholder="请输入申请事由" style="width: 205px" />
            </el-form-item>
          </el-col>
          <el-col :span="10"><div class="grid-content bg-purple" /></el-col>
        </el-row>

        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="20">
            <el-form-item label="上传凭证">
              <el-upload
                ref="upload"
                v-model="temp.cost_photo"
                class="upload-demo"
                drag
                action="fakeaction"
                :http-request="uploadSectionFile"
                multiple
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="1" />
        </el-row>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getALLPro, getCompanyUsers } from '@/api/porject/project'
import { getReimbursementType, costApplyAdd } from '@/api/finance'
import { CustomerList } from '@/api/customer'
import { fileUpload } from '@/api/system/sys'
export default {
  name: 'Application',
  data() {
    return {
      options: [],
      list: [],
      userName: [],
      customer: [],
      lists: {
        page: 1,
        page_size: 10,
        status: 1
      },
      rules: {
        reason: [{ required: true, message: '请输入申请缘由', trigger: 'blur' }],
        filing_date: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
        pro_id: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
        customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
        expense_examiner: [{ required: true, message: '请选择费用审批人', trigger: 'change' }],
        money: [{ required: true, message: '请输入申请金额', trigger: 'blur' }],
        category_id: [{ required: true, message: '请选择费用类型', trigger: 'change' }]
      },
      temp: {
        pro_id: '',
        category_id: '',
        money: '',
        customer_id: '',
        filing_date: '',
        reason: '',
        expense_examiner: '',
        cost_photo: []
      },
      dialogVisible: false,
      dialogImageUrl: '',
      show: false,
      disabled: false
    }
  },
  created() {

  },
  methods: {
    resetTemp() {
      this.temp = {
        pro_id: '',
        category_id: '',
        money: '',
        customer_id: '',
        filing_date: '',
        reason: '',
        expense_examiner: '',
        cost_photo: []
      }
    },
    handleGetALLPro() {
      // 项目
      getALLPro().then(response => {
        this.options = response.data
      }) // 费用类型
      getReimbursementType({ type: 2 }).then(response => {
        this.list = response.data
      }) // 人员
      getCompanyUsers().then(response => {
        this.userName = response.data
      }) // 客户
      CustomerList(this.lists).then(res => {
        this.customer = res.data.data
      })
    },
    handleOpen() {
      this.handleGetALLPro()
      this.dialogVisible = true
    },
    // 提交
    handleSubmit() {
      this.temp.cost_photo = this.temp.cost_photo.toString()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          costApplyAdd(this.temp).then(() => {
            this.dialogVisible = false
            this.$notify({
              title: '提交成功',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.$refs['dataForm'].resetFields()
          })
        }
      })
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form).then(res => {
        this.temp.cost_photo.push(res.data.url)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.reimbursement {
  display: block;
}
.search-icon {
  font-size: 40px;
  display: block;
  color: cadetblue;
}
.avatar-uploader .el-upload {
  border: 1px dashed #ccc;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.reimbursement {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: auto;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.el-card{
  border-radius: 15px;
}
</style>
