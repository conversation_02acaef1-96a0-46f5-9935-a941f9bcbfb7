<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="card" />
        <span class="card">补卡申请</span>
      </div>
    </el-card>

    <el-dialog
      title="补卡申请"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="35%"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="补卡时间" prop="record_id">
          <el-select
            v-model="temp.record_id"
            placeholder="请选择补卡时间"
            clearable
            style="width: 205px"
            class="filter-item"
          >
            <el-option v-for="item in card_time" :key="item.id" :label="item.work_date" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="补卡理由" prop="reason">
          <el-input v-model="temp.reason" clearable type="textarea" :rows="2" placeholder="请输入补卡理由" style="width: 205px" />
        </el-form-item>

        <el-form-item label="补卡审核人" prop="audit_id">
          <el-select
            v-model="temp.audit_id"
            placeholder="请选择补卡审核人"
            clearable
            style="width: 205px"
            class="filter-item"
          >
            <el-option v-for="item in options" :key="item.id" :label="item.user_username" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { lackCard, retroactive } from '@/api/card'
import { getCompanyUsers } from '@/api/porject/project'
export default {
  name: 'Card',
  data() {
    return {
      rules: {
        audit_id: [{ required: true, message: '请选择审核人', trigger: 'change' }],
        record_id: [{ required: true, message: '请选择补卡时间', trigger: 'change' }],
        reason: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
      },
      card_time: [],
      options: [],
      temp: {
        audit_id: '',
        record_id: '',
        reason: ''
      },
      dialogVisible: false
    }
  },
  created() {
  },
  methods: {
    resetTemp() {
      this.temp = {
        audit_id: '',
        record_id: '',
        retroactive_time: '',
        reason: ''
      }
    },
    getList() {
      lackCard().then(res => {
        this.card_time = res.data
      })
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },
    handleOpen() {
      this.getList()
      this.dialogVisible = true
      this.resetTemp()
    },
    // 提交
    handleSubmit() {
      this.card_time.forEach(item => {
        if (item.id === this.temp.record_id) {
          this.temp.retroactive_time = item.work_date
        }
      })
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          retroactive(this.temp).then(() => {
            this.dialogVisible = false
            this.$notify({
              title: '提交成功',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.$refs['dataForm'].resetFields()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.search-icon {
  font-size: 40px;
  display: block;
  color: cadetblue;
}
.card {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: auto;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.el-card{
  border-radius: 15px;
}
</style>
