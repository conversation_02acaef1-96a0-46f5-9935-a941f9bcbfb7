<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="control" />
        <span class="clock">疫情防控</span>
      </div>
    </el-card>
    <el-dialog
      title="疫情防控"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="35%"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="健康报截图" prop="record_id">
          <el-upload
            ref="upload"
            v-model="temp.health_image"
            action="#"
            list-type="picture-card"
            :file-list="fileList"
            :http-request="uploadSectionFile"
            :limit="1"
          >
            <i slot="default" class="el-icon-plus" />
            <div slot="file" slot-scope="{file}">
              <img
                class="el-upload-list__item-thumbnail"
                :src="file.url"
                alt=""
              >
              <span class="el-upload-list__item-actions">
                <span
                  class="el-upload-list__item-preview"
                  @click="handlePictureCardPreview(file)"
                >
                  <i class="el-icon-zoom-in" />
                </span>
                <span
                  v-if="!disabled"
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file)"
                >
                  <i class="el-icon-delete" />
                </span>
              </span>
            </div>
          </el-upload>
          <el-dialog :visible.sync="show" :append-to-body="true">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </el-form-item>
        <el-form-item label="姓名" prop="reason">
          <el-input v-model="temp.user_name" clearable disabled type="input" style="width: 205px" />
        </el-form-item>
        <el-form-item v-if="temp.day !== ''" label="时间" prop="audit_id">
          <el-input v-model="temp.day + '天'" clearable disabled type="input" style="width: 205px" />
        </el-form-item>

        <el-form-item v-else label="时间" prop="audit_id">
          <el-input v-model="temp.day" clearable disabled type="input" style="width: 205px" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { fileUpload } from '@/api/system/sys'
import { addHealthTreasure } from '@/api/card'
export default {
  name: 'Clock',
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      fileList: [],
      show: false,
      disabled: false,
      temp: {
        user_name: '',
        day: '',
        health_image: ''
      },
      rules: {}
    }
  },
  methods: {
    handleSubmit() {
      console.log(this.temp.health_image)
      addHealthTreasure(this.temp).then((res) => {
        this.temp.day = res.data.days
        this.dialogVisible = false
        this.$notify({
          title: '提交成功',
          message: 'Created Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleOpen() {
      this.dialogVisible = true
      this.temp.user_name = localStorage.getItem('userName')
    },
    handleRemove(file) {
      if (this.fileList.indexOf(this.baseImg + file.url)) {
        this.fileList.splice(this.fileList.indexOf(this.baseImg + file.url), 1)
      }
      for (const i in file) {
        delete file[i]
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.show = true
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form).then(res => {
        this.temp.health_image = res.data.url
        console.log(res.data, 'url')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.search-icon {
  font-size: 40px;
  display: block;
  color: cadetblue;
}
.clock {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: 0 auto;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.el-card{
  border-radius: 15px;
}
</style>
