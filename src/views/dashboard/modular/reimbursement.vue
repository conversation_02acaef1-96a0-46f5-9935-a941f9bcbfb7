<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="reimbursement" />
        <span class="reimbursement">报销申请</span>
      </div>
    </el-card>
    <el-dialog
      title="报销申请"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="40%"
    >
      <el-form
        ref="dataForm"
        :append-to-body="true"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >

        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="10">
            <el-form-item label="关联项目" prop="project_id">
              <el-select
                v-model="temp.project_id"
                placeholder="请选择关联项目"
                clearable
                style="width: 205px"
                class="filter-item"
              >
                <el-option v-for="item in options" :key="item.id" :label="item.pro_name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="10">
            <el-form-item label="审批人" prop="expense_examiner">
              <el-select
                v-model="temp.expense_examiner"
                placeholder="请选择审批人"
                clearable
                style="width: 205px"
                class="filter-item"
              >
                <el-option v-for="item in userName" :key="item.id" :label="item.user_username" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 点击新增一块数据 -->
        <div v-for="(domain, index) in temp.list" :key="domain.key" :prop="'list.' + index + '.value'" style="border-top: 1px solid #f1f1f1; padding-top: 20px">
          <el-row type="flex" class="row-bg" justify="space-around">
            <el-col :span="10">
              <el-form-item label="报销类型" :prop="'list.' + index + '.expense_type'">
                <el-select
                  v-model="domain.expense_type"
                  placeholder="请选择报销类型"
                  clearable
                  style="width: 205px"
                  class="filter-item"
                >
                  <el-option v-for="item in list" :key="item.id" :label="item.expense_type" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="10">
              <el-form-item label="报销金额" :prop="'list.' + index + '.expense_money'">
                <el-input v-model="domain.expense_money" clearable type="number" placeholder="请输入报销金额" style="width: 204px" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row type="flex" class="row-bg" justify="space-around">
            <el-col :span="10">
              <el-form-item label="报销明细" :prop="'list.' + index + '.expense_detail'">
                <el-input v-model="domain.expense_detail" clearable type="textarea" :rows="1" placeholder="请输入报销明细" style="width: 204px" />
              </el-form-item>
            </el-col>

            <el-col :span="10">
              <el-form-item label="报销时间" :prop="'list.' + index + '.expense_time'">
                <el-date-picker
                  v-model="domain.expense_time"
                  unlink-panels
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  type="daterange"
                  clearable
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 230px"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row type="flex" class="row-bg" justify="space-around">
            <el-col :span="10">
              <el-button v-if="index > 0" @click.prevent="removeDomain(domain)">删除本条</el-button>
            </el-col>
            <el-col :span="10" />
          </el-row>
        </div>
        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="20">
            <el-form-item label="上传凭证">
              <el-upload
                ref="upload"
                v-model="temp.expense_photo"
                class="upload-demo"
                drag
                action="fakeaction"
                :http-request="uploadSectionFile"
                multiple
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="1" />
        </el-row>
        <el-row type="flex" class="row-bg" justify="space-around">
          <el-button class="addDomain" @click="addDomain">新增一条</el-button>
        </el-row>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getALLPro, getCompanyUsers } from '@/api/porject/project'
import { addInformalCategory } from '@/api/card'
import { getReimbursementType } from '@/api/finance'
import { fileUpload } from '@/api/system/sys'
export default {
  name: 'Reimbursement',
  data() {
    return {
      options: [],
      list: [],
      userName: [],
      rules: {
        project_id: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
        expense_examiner: [{ required: true, message: '请选择审核人', trigger: 'change' }],
        list: [
          {
            expense_type: [{ required: true, message: '请选择报销类型', trigger: 'change' }],
            expense_money: [{ required: true, message: '请输入报销金额', trigger: 'blur' }],
            expense_detail: [{ required: true, message: '请输入报销明细', trigger: 'blur' }],
            expense_time: [{ required: true, message: '请选择报销时间', trigger: 'change' }]
          }
        ]
      },
      temp: {
        project_id: '',
        expense_examiner: '',
        expense_photo: [],
        list:
          [{
            expense_type: '',
            expense_money: '',
            expense_detail: '',
            expense_time: ''
          }]
      },
      dialogVisible: false,
      dialogImageUrl: '',
      show: false,
      disabled: false
    }
  },
  created() {

  },
  methods: {
    resetTemp() {
      this.temp = {
        project_id: '',
        expense_examiner: '',
        search_time: [],
        expense_photo: [],
        list:
          [{
            expense_type: '',
            expense_money: '',
            expense_detail: '',
            expense_time: ''
          }]
      }
    },
    handleOpen() {
      this.handleGetALLPro()
      this.dialogVisible = true
    },
    handleGetALLPro() {
      // 项目
      getALLPro().then(response => {
        this.options = response.data
      }) // 报销
      getReimbursementType({ type: 1 }).then(response => {
        this.list = response.data
      }) // 人员
      getCompanyUsers().then(response => {
        this.userName = response.data
      })
    },
    // 提交
    handleSubmit() {
      this.temp.list.forEach((item) => {
        item.expense_time = item.expense_time[0] + '——' + item.expense_time[0]
      })
      this.temp.expense_photo = this.temp.expense_photo.toString()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addInformalCategory(this.temp).then(() => {
            this.dialogVisible = false
            this.$notify({
              title: '提交成功',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.$refs['dataForm'].resetFields()
            this.resetTemp()
          })
        }
      })
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form).then(res => {
        this.temp.expense_photo.push(res.data.url)
      })
    },
    removeDomain(item) {
      var index = this.temp.list.indexOf(item)
      if (index !== -1) {
        this.temp.list.splice(index, 1)
      }
    },
    addDomain() {
      this.temp.list.push({
        expense_type: '',
        expense_money: '',
        expense_detail: '',
        expense_time: '',
        key: Date.now()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leave {
  display: block;
}
.search-icon {
  font-size: 40px;
  color: rebeccapurple;
  display: block;
}

.leave {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: auto;
}
.reimbursement {
  text-align: center;
  font-size: 12px;
  padding-top: 5px;
  z-index: 10;
  font-weight: bold;
  margin-top: 5px;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.el-card{
  border-radius: 15px;
}
</style>
