<!-- <template>
    <div class="main">
      任务发布 
      <TaskPublishing class="assembly" />
      <Reimbursement class="assembly" /> 
      <Fee class="assembly" /> 
      <Card class="assembly" /> 
      <Leave class="assembly" /> 
      <Clock class="assembly" /> 
      <Control  class="assembly" />
    </div>
</template>-->
<!-- 
<script>
import TaskPublishing from './taskPublishing.vue'
import Reimbursement from './reimbursement.vue'
import Fee from './fee.vue'
import Leave from './leave.vue'
import Card from './card.vue'
import Clock from './clock.vue'
import Control from './control.vue'

export default {
  name: 'Summarys',
  components: {
    TaskPublishing,
    Reimbursement,
    Fee,
    Card,
    Leave,
    Clock,
    Control
  }
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  justify-content: space-between;
  padding: 0 2vw ;
}
.assembly {
  cursor:pointer;
  margin-top:  2vh;
}
</style> --> 
