<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="leave" />
        <span class="leave">请假申请</span>
      </div>
    </el-card>

    <el-dialog
      title="请假申请"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="35%"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="选择日期" prop="search_time">
          <el-date-picker
            v-model="temp.search_time"
            unlink-panels
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="daterange"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="请假理由" prop="leave_reason">
          <el-input v-model="temp.leave_reason" clearable type="textarea" :rows="1" placeholder="请输入内容" style="width: 350px" />
        </el-form-item>
        <el-form-item label="请假审核人" prop="leave_examiner">
          <el-select
            v-model="temp.leave_examiner"
            placeholder="请选择审核人"
            clearable
            filterable
            style="width: 205px"
            class="filter-item"
          >
            <el-option v-for="item in options" :key="item.id" :label="item.user_username" :value="item.id" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="抄送人" prop="receive_name">
          <el-select
            v-model="temp.receive_name"
            multiple
            filterable
            placeholder="请选择抄送人"
          >
            <el-option
              v-for="item in options1"
              :key="item.user_username"
              :label="item.user_username"
              :value="item.id"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="请假类型" prop="leave_type">
          <el-select
            v-model="temp.leave_type"
            placeholder="请选择请假类型"
            clearable
            filterable
            style="width: 205px"
            class="filter-item"
          >
            <el-option v-for="item in list" :key="item.id" :label="item.leave_type" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="上传凭证" prop="leave_photo">
          <el-upload
            ref="upload"
            v-model="temp.leave_photo"
            class="upload-demo"
            drag
            action="fakeaction"
            :http-request="uploadSectionFile"
            multiple
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { leaveTypeList } from '@/api/workAttendance'
import { getCompanyUsers } from '@/api/porject/project'
import { leaveRequest } from '@/api/card'
import { fileUpload } from '@/api/system/sys'
export default {
  name: 'Leave',
  data() {
    return {
      rules: {
        leave_type: [{ required: true, message: '请选择请假类型', trigger: 'change' }],
        search_time: [{ required: true, message: '请选择请假时间', trigger: 'change' }],
        leave_reason: [{ required: true, message: '请输入请假理由', trigger: 'blur' }],
        leave_examiner: [{ required: true, message: '请选择审核人', trigger: 'change' }]
      },
      temp: {
        leave_stoptime: '',
        leave_starttime: '',
        leave_reason: '',
        leave_examiner: '',
        leave_type: '',
        leave_photo: [],
        search_time: []
      },
      list: [],
      options: [],
      dialogVisible: false,
      dialogImageUrl: '',
      show: false,
      disabled: false
    }
  },
  created() {

  },
  methods: {
    resetTemp() {
      this.temp = {
        leave_stoptime: '',
        leave_starttime: '',
        leave_reason: '',
        leave_examiner: '',
        leave_type: '',
        search_time: [],
        leave_photo: []
      }
    },
    getList() {
      leaveTypeList().then(response => {
        this.list = response.data
      })
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },
    handleOpen() {
      this.getList()
      this.dialogVisible = true
    },
    // 提交
    handleSubmit() {
      this.temp.leave_starttime = this.temp.search_time[0]
      this.temp.leave_stoptime = this.temp.search_time[1]
      this.temp.leave_photo = this.temp.leave_photo.toString()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          leaveRequest(this.temp).then(() => {
            this.dialogVisible = false
            this.$notify({
              title: '提交成功',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.$refs['dataForm'].resetFields()
          })
        }
      })
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form).then(res => {
        this.temp.leave_photo.push(res.data.url)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leave {
  display: block;
}
.search-icon {
  font-size: 40px;
  color: rebeccapurple;
  display: block;
}

.leave {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: auto;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.el-card{
  border-radius: 15px;
}
</style>
