<template>
  <div>
    <el-card shadow="hover" class="content">
      <div class="box" @click="handleOpen">
        <svg-icon class-name="search-icon" icon-class="task2" />
        <span class="task">任务发布</span>
      </div>
    </el-card>

    <el-dialog
      title="任务发布"
      :append-to-body="true"
      :visible.sync="dialogVisible"
      width="35%"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >

        <el-form-item label="任务名称" prop="task_name">
          <el-input v-model="temp.task_name" placeholder="请选择任务名称" style="width: 205px" />
        </el-form-item>

        <el-form-item label="关联项目" prop="project_id">
          <el-select
            v-model="temp.project_id"
            filterable
            placeholder="请选择关联项目"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.pro_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="执行人" prop="receive_id">
          <el-select
            v-model="temp.receive_id"
            multiple
            filterable
            placeholder="请选择执行人"
          >
            <el-option
              v-for="item in options1"
              :key="item.user_username"
              :label="item.user_username"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="任务类型" prop="task_category">
          <el-select
            v-model="temp.task_category"
            placeholder="任务类型"
            clearable
            filterable
            style="width: 205px"
            class="filter-item"
          >
            <el-option v-for="(item,cou) in importanceOptions1" :key="cou" :label="item.task_name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="任务级别" prop="task_level">
          <el-select
            v-model="temp.task_level"
            placeholder="选择任务等级"
            clearable
            style="width: 205px"
            class="filter-item"
          >
            <el-option v-for="(item, index) in leavel" :key="index" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="选择日期" prop="search_time">
          <el-date-picker
            v-model="temp.search_time"
            unlink-panels
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="daterange"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="备注" prop="content">
          <el-input v-model="temp.content" type="textarea" :rows="2" placeholder="备注" style="width: 350px" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getALLPro, getCompanyUsers, getTaskCategory, getTaskLevel, addTask } from '@/api/porject/project'
export default {
  data() {
    return {
      dialogVisible: false,
      options: [],
      options1: [],
      importanceOptions1: [],
      leavel: [],
      temp: {
        project_id: '',
        task_level: '',
        task_name: '',
        task_category: '',
        search_time: '',
        receive_name: '',
        content: '',
        end_time: '',
        receive_id: '',
        start_time: ''
      },
      rules: {
        project_id: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
        task_level: [{ required: true, message: '请选择任务级别', trigger: 'change' }],
        task_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        task_category: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
        receive_id: [{ required: true, message: '请选择执行人', trigger: 'blur' }],
        search_time: [{ required: true, message: '请选择日期', trigger: 'change' }]
      }
    }
  },
  created() {

  },
  methods: {
    // 清空
    resetTemp() {
      this.temp = {
        project_id: '',
        task_level: '',
        task_name: '',
        task_category: '',
        search_time: '',
        receive_name: '',
        content: '',
        end_time: '',
        receive_id: '',
        start_time: ''
      }
    },
    handleOpen() {
      this.dialogVisible = true
      this.handleGetALLPro()
    },
    // 提交
    handleSubmit() {
      this.temp.start_time = this.temp.search_time[0]
      this.temp.end_time = this.temp.search_time[1]
      this.temp.task_level = Number(this.temp.task_level)
      this.temp.receive_id = this.temp.receive_id.toString()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addTask(this.temp).then(() => {
            this.dialogVisible = false
            this.$notify({
              title: '提交成功',
              message: '任务发布成功',
              type: 'success',
              duration: 2000
            })
            this.$refs['dataForm'].resetFields()
            this.resetTemp()
          })
        }
      })
    },
    // 获取任务项目任务等接口数据
    handleGetALLPro() {
      getALLPro().then(response => {
        this.options = response.data
      })
      getCompanyUsers().then(response => {
        this.options1 = response.data
      })
      getTaskCategory().then(response => {
        this.importanceOptions1 = response.data
      })
      getTaskLevel().then(res => {
        this.leavel = []
        for (var i in res.data) {
          this.leavel.push({
            id: i,
            label: res.data[i]
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.task {
  display: block;
}
.search-icon {
  font-size: 40px;
  color: red;
  display: block;
}
.content {
  width: 95px;
  height: 95px;
  box-sizing: border-box;
}
.task {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
.search-icon {
  display: block;
  margin: auto;
}
.el-card{
  border-radius: 15px;
}
</style>
