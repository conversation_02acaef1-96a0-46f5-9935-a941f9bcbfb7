<template>
  <div class="mixin-components-container">
    <el-date-picker
          v-model="searchTime"
          unlink-panels
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="daterange"
          clearable
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleFilter"
        />
    <el-row :gutter="20" style="margin-top:20px;">



      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>任务标签统计</span>
          </div>
          <Task />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>年龄分布</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top:20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>学历分布</span>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>岗位分布</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped>
.mixin-components-container {
  background-color: #f0f2f5;
  padding: 30px;
  min-height: calc(100vh - 84px);
}
</style>
<script>
import Task from '@/views/portrait/components/label/task'

var date = new Date(); 
export default {
  components: {
    Task
  },
  data() {
    return { 
        searchTime: [], 
    }
  },
  created(){ 
    this.searchTime.push(this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date()))
  
  },
  methods:{
   
    formatDate(value) { // 时间戳转换日期格式方法
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    handleFilter(){
      console.log(this.listQuery.search_time)
    }
  }
}
</script>
