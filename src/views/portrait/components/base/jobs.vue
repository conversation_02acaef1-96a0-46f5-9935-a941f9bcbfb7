<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '../mixins/resize'
import { positionRatio } from '@/api/staff'

export default {
  name: 'Jobs',
  mixins: [resize],
  props: {
    task: {
      type: Object,
      default: () => {
      }
    },
    fourObj: {
      type: Object,
      default: () => {
      }
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async initChart() {
      await  positionRatio().then((res) => {
      this.count = res.data
      })

      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        title: {
          text: '',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '岗位',
            type: 'pie',
            radius: '50%',
            data: this.count,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }, true)
    }
  }
}
</script>

