<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '../mixins/resize'
import { ageRatio } from '@/api/staff'

export default {
  name: 'Age',
  mixins: [resize],
  props: {
    task: {
      type: Object,
      default: () => {
      }
    },
    fourObj: {
      type: Object,
      default: () => {
      }
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async initChart() {
      this.age = []
      this.age_distribution = []
      await ageRatio().then((res) => {

          res.data.forEach(element => {
            this.age.push(element.value)
            this.age_distribution.push(element.name)
          });
      })
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        title: {
          text: '年龄分布'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },

        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.age_distribution
        },
        series: [
          {
            name: '人数',
            type: 'bar',
            data: this.age
          }
        ]
      }, true)
    }
  }
}
</script>

