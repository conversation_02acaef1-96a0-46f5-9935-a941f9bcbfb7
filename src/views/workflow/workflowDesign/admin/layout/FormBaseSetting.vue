<template>
  <el-row>
    <el-col :span="10">
      <img src="@/assets/images/workflow.jpg" style="  margin-top: 20%;
           margin-left: 8%;  width: 100%" alt="">
    </el-col>

    <el-col :span="14">
      <div class="base-setup" @click="showIconSelect = false">
        <el-form ref="baseSetting" :model="setup" label-position="top" label-width="80px">
          <el-form-item label="表单名称" :rules="getRule('请输入表单名称')" prop="formName">
            <el-input v-model="setup.formName" size="medium"></el-input>
          </el-form-item>

          <el-form-item label="所在分组" :rules="getRule('请选择表单分组')" class="group" prop="groupId">
            <el-select v-model="setup.groupId" placeholder="请选择分组" size="medium">
              <el-option v-for="(op, index) in categoryData" :key="index" v-show="op.id > 0"
                         :label="op.name" :value="op.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="管理员">
            <el-select v-model="setup.settings.admin" @click.native="selectUser('admin')" value-key="name"
                       class="select-u" placeholder="请选择表单管理员"
                       size="medium" clearable multiple
            >
              <el-option v-for="(wc, index) in setup.settings.admin" :label="wc.name" :key="index" :value="wc"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="表单说明">
            <el-input placeholder="请输入表单说明" v-model="setup.remark" type="textarea" show-word-limit
                      :autosize="{ minRows: 5, maxRows: 10}" maxlength="500"
            ></el-input>
          </el-form-item>

        </el-form>
        <org-picker title="表单管理员" multiple ref="orgPicker" :selected="select" @ok="selected"></org-picker>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import OrgPicker from '@/components/common/OrgPicker'
import { getFormGroups } from '@/api/design'
import iconfont from '@/assets/iconfont/iconfont.json'

export default {
  name: 'FormBaseSetting',
  components: { OrgPicker },
  data() {
    return {
      nowUserSelect: null,
      showIconSelect: false,
      select: [],
      newGroup: '',
      categoryData: [],
      fromGroup: [],
      notifyTypes: [
        { type: 'APP', name: '应用内通知' },
        { type: 'EMAIL', name: '邮件通知' },
        { type: 'SMS', name: '短信通知' },
        { type: 'WX', name: '微信通知' },
        { type: 'DING', name: '钉钉通知' }
      ],
      colors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      icons: [
        'el-icon-delete-solid',
        'el-icon-s-tools',
        'el-icon-s-goods',
        'el-icon-warning',
        'el-icon-circle-plus',
        'el-icon-camera-solid',
        'el-icon-s-promotion',
        'el-icon-s-cooperation',
        'el-icon-s-platform',
        'el-icon-s-custom',
        'el-icon-s-data',
        'el-icon-s-check',
        'el-icon-s-claim'
      ],
      rules: {
        formName: [{}],
        groupId: []
      }
    }
  },
  computed: {
    setup() {
      return this.$store.state.workflow.design
    }
  },
  created() {
    this.loadIconfont()
  },
  mounted() {
    this.getFormGroups()
  },
  methods: {
    getRule(msg) {
      return [{ required: true, message: msg, trigger: 'blur' }]
    },
    getFormGroups() {
      getFormGroups().then(response => {
        this.categoryData = response.data
      })
    },
    loadIconfont() {
      if (iconfont && iconfont.id) {
        iconfont.glyphs.forEach(icon => {
          this.icons.push(`${iconfont.font_family} ${iconfont.css_prefix_text}${icon.font_class}`)
        })
      }
    },
    selected(select) {
      this.$set(this.setup.settings, this.nowUserSelect, select)
      //this.setup[this.nowUserSelect] = select
    },
    selectUser(key) {
      this.select = this.setup.settings[key]
      this.nowUserSelect = key
      this.$refs.orgPicker.show()
    },
    validate() {
      this.$refs.baseSetting.validate()
      let err = []
      if (!this.$isNotEmpty(this.setup.formName)) {
        err.push('表单名称未设置')
      }
      if (!this.$isNotEmpty(this.setup.groupId)) {
        err.push('表单分组未设置')
      }
      return err
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-select-dropdown {
  display: none;
}

.icon-select {
  display: flex;
  flex-wrap: wrap;

  i {
    cursor: pointer;
    font-size: large;
    padding: 10px;
    max-width: 38px !important;

    &:hover {
      box-shadow: 0 0 10px 2px #C2C2C2;
    }
  }
}

/deep/ .select-u {
  width: 100%;
}

.base-setup {
  overflow: auto;
  width: 600px;
  height: calc(100vh - 105px);
  background: #ffffff;
  margin: 10px auto 0;
  padding: 15px 20px;

  i:first-child {
    position: relative;
    cursor: pointer;
    font-size: xx-large;
    color: #ffffff;
    border-radius: 10px;
    padding: 10px;
  }

  .change-icon {
    margin-left: 20px;

    span {
      font-size: small;
      color: #7a7a7a;
      margin-right: 15px;
    }

    i {
      cursor: pointer;
      color: #7a7a7a;
      font-size: x-large;
    }
  }

  /deep/ .el-form-item__label {
    padding: 0;
    font-weight: bold;
  }

  /deep/ .el-form-item {
    margin-bottom: 5px;
  }
}

/deep/ .group {
  .el-select {
    width: calc(100%);
  }

  .el-button {
    margin-left: 10px;
    width: 120px;
  }
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #f8f8f8;
}

::-webkit-scrollbar-thumb {
  border-radius: 16px;
  background-color: #e8e8e8;
}
</style>
