<template>
  <el-row :gutter="20">
    <div class="app-container" style=" display: flex; ">
      <el-col :span="3">
        <el-row class="tac">
          <el-col :span="24">
            <h5>流程分类</h5>
            <el-menu
              class="el-menu-vertical-demo"
              @select="handleClick"
            >
              <el-menu-item v-for="(val,index) in categoryData"   :index="(val.id).toString()" :key="index">
                <i class="el-icon-orange"></i>
                <span>{{ val.name }}</span>
              </el-menu-item>
            </el-menu>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="21">
        <div>
          <div class="filter-container" style="position: relative">
            <el-col :span="4">
              <div style="display: inline-block;">
                <el-input v-model="listQuery.form_name" placeholder="名称" clearable class="filter-item"
                          @input="handleFilter"/>
              </div>
            </el-col>
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
              搜索
            </el-button>
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="newProcess()">
              新增表单
            </el-button>
            <el-button v-waves class="filter-item" type="primary" @click="category">
              分类管理
            </el-button>
          </div>
          <el-dialog title="分类管理" :visible.sync="dialogTableVisible">
            <el-button type="primary"   @click="creatFormGroups">新增</el-button>
            <el-table :data="categoryData" style="margin-top: 30px">
              <el-table-column align="center" property="id" label="ID" width="50"></el-table-column>
              <el-table-column align="center" property="name" label="名称" width="150"></el-table-column>
              <el-table-column align="center" property="create_by" label="创建人" width="200"></el-table-column>
              <el-table-column align="center" property="created_at" label="创建时间"></el-table-column>
              <el-table-column align="center" property="address" label="操作">
                <template slot-scope="{row}">
                  <el-button type="primary" size="mini" @click="editFormGroups(row)">
                    编辑
                  </el-button>
                  <el-button type="warning" size="mini" @click="delFormGroups(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-dialog>
          <el-table
            :key="tableKey"
            v-loading="listLoading"
            :data="list"
            border
            fit
            style="width: 100%;"
          >
            <el-table-column
              label="序号"
              type="index"
              :index="indexAdd"
              sortable="custom"
              align="center"
              width="80"
            />
            <el-table-column label="名称" align="center">
              <template slot-scope="{row}">
                <span>{{ row.form_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属分组" align="center">
              <template slot-scope="{row}">
                <span>{{ row.group_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属部门" align="center">
              <template slot-scope="{row}">
            <span
              style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;"
            >{{ row.dept_id }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center">
              <template slot-scope="{row}">
                <span>{{ row.create_by }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center">
              <template slot-scope="{row}">
                <span>{{ row.created_at }}</span>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" align="center">
              <template slot-scope="{row}">
                <span>{{ row.updated_at }}</span>
              </template>
            </el-table-column>
            <el-table-column label="表单状态" align="center">
              <template slot-scope="{row}">
                <span>{{ row.is_stop }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('table.actions')" align="center" width="200"
                             class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="handleUpdate(row)">
                  编辑
                </el-button>
                <el-button type="warning" size="mini" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.page_size"
            @pagination="getList"
          />
        </div>
      </el-col>
    </div>
  </el-row>
</template>
<script>

import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { getFormGroups, creatFormGroups, getFormItems, delFormGroups, editFormGroups ,delForm} from '@/api/design'


export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      name: '',
      dialogTableVisible: false,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        form_name: '',
        group_id: ''
      },
      categoryData: [],
      listData: {
        page: 1,
        page_size: 10
      },
      options: [],
      temp: {
        form_name: '',
        group_id: ''
      },
      data: [],
      dialogFormVisible: false,
      dialogStatus: '',
      innerVisible: false,
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClick(item) {
      this.listQuery.group_id = item
      this.getList()
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return (page - 1) * pagesize + index + 1
    },
    getList() {
      this.listLoading = true
      getFormItems(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
      // 获取分组
     this.getFormGroups()
    },
    getFormGroups() {
      getFormGroups().then(response => {
        this.categoryData = response.data
      })
    },
    handleFilter() {
      this.getList()
    },
    handleDelete(row, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delForm({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.list.splice(index, 1)
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        this.$notify({
          title: 'info',
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    },
    newProcess() {
      this.$router.push('/admin/design')
    },
    category() {
      this.dialogTableVisible = true
    },
    handleUpdate(row) {
      this.$router.push('/admin/design?code=' + row.id + '&group=' + row.group_id)
    },
    creatFormGroups() {
      this.$prompt('', '请输入分组名称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        // 创建分组
        creatFormGroups({ name: value }).then(response => {
          if (response.meta.status === 200) {
            this.getFormGroups()
            this.$notify({
              title: 'Success',
              message: '成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        })
      })
    },delFormGroups(row) {
      this.$confirm('是否删除分组', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delFormGroups({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getFormGroups()
            this.$notify({
              title: 'Success',
              message: '成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      })
    },
    editFormGroups(row) {
      this.$prompt('', '请输入分组名称', {
        confirmButtonText: '确定',
        inputValue: row.name,
        cancelButtonText: '取消'
      }).then(({ value }) => {
        // 创建分组
        editFormGroups({ name: value, id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getFormGroups()
            this.$notify({
              title: 'Success',
              message: '成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      })
    },
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0;
  margin-left: 10px;
  padding-bottom: 10px;
}

::v-deep .el-tree-node__content {
  height: auto !important;
  position: relative;
  margin-left: 30px;
}

::v-deep .el-tree-node__expand-icon {
  position: absolute;
  top: 0;
  left: 0;
}
</style>

