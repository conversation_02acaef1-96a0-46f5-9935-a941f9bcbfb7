<template>
  <el-row :gutter="20">
    <div class="app-container" style=" display: flex; ">
      <el-col :span="3">
        <el-row class="tac">
          <el-col :span="24">
            <h5>流程任务状态</h5>
            <el-menu
              default-active="wait"
              class="el-menu-vertical-demo"
              @select="handleClick"
            >
              <el-menu-item index="wait">
                <i class="el-icon-orange"></i>
                <span>待我处理</span>
              </el-menu-item>

              <el-menu-item index="success">
                <i class="el-icon-orange"></i>
                <span>已处理的</span>
              </el-menu-item>

              <el-menu-item index="owner">
                <i class="el-icon-orange"></i>
                <span>我发起的</span>
              </el-menu-item>

              <el-menu-item index="attention">
                <i class="el-icon-orange"></i>
                <span>我得关注</span>
              </el-menu-item>

              <el-menu-item index="all">
                <i class="el-icon-orange"></i>
                <span>全部流程</span>
              </el-menu-item>
            </el-menu>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="21">
        <div>
          <div class="filter-container" style="position: relative">
            <div style="display: inline-block;">
            </div>
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
              搜索
            </el-button>
          </div>
          <el-table
            :key="tableKey"
            v-loading="listLoading"
            :data="list"
            border
            fit
          >
            <el-table-column label="流水号" width="80" align="center">
              <template slot-scope="{row}">
                <span>{{ row.run_id }}</span>
              </template>
            </el-table-column>

            <el-table-column label="所属分类" width="80" align="center">
              <template slot-scope="{row}">
                <span>{{ row.group_name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="流程名称"  align="center">
              <template slot-scope="{row}">
                <span>{{ row.run_name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="发起人" width="80" align="center">
              <template slot-scope="{row}">
                <span>{{ row.begin_user }}</span>
              </template>
            </el-table-column>


            <el-table-column label="当前接收人" width="120"  align="center">
              <template slot-scope="{row}">
                <span>{{ row.receive_user }}</span>
              </template>
            </el-table-column>

            <el-table-column label="节点流转" width="120" align="center">
              <template slot-scope="{row}">
                <span>{{ row.process_name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="流程状态" width="80"  align="center">
              <template slot-scope="{row}">
                <span>{{ row.run_flag }}</span>
              </template>
            </el-table-column>

            <el-table-column label="发起时间" width="120" align="center">
              <template slot-scope="{row}">
                <span>{{ row.created_at }}</span>
              </template>
            </el-table-column>

            <el-table-column :label="$t('table.actions')" align="center" width="150"
                             class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini"  :style="{ display: row.check === true ? '' : 'none' }" @click="approve(row)">
                  审批
                </el-button>
                <el-button type="warning" size="mini" :style="{ display: row.collect === true ? '' : 'none' }"  @click="collect(row)" >
                  关注
                </el-button>
              </template>
            </el-table-column>

          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.page_size"
            @pagination="getList"
          />
        </div>
      </el-col>
    </div>
  </el-row>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import {workflowRunList,workflowCollect} from '@/api/design'

export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      dialogTableVisible: false,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        type: "wait"
      },
      listQueryAdd: {
        company_id: ''
      },
      search_data: [],
      listData: {
        page: 1,
        page_size: 10
      },
      options: [],
      temp: {
      },
      data: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取流程列表
    getList() {
      this.listLoading = true
      workflowRunList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleClick(item) {
      this.listQuery.type = item
      this.getList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    approve(row) {
      this.$router.push({
        path: 'admin/initProcess',
        query: {
          formId: row.flow_id,
          runId: row.run_id,

          processId: row.process_id,
          run_process_id: row.run_process_id,
        }
      })
    },
    collect(row) {
      workflowCollect({run_id: row.run_id}).then(response => {
        if (response.meta.status === 200) {
          this.$notify({
            title: 'Success',
            message: '收藏成功',
            type: 'success',
            duration: 2000
          })
        }

        this.getList();
      })
    },
    getTemplateData(data, group) {
      return data
    },
    newProcess() {
      this.$store.commit('setTemplate', this.getTemplateData())
      this.$store.commit('setIsEdit', false)
      this.$router.push('/admin/design')
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0;
  margin-left: 10px;
}

::v-deep .el-tree-node__content {
  height: auto !important;
  position: relative;
  margin-left: 30px;
}

::v-deep .el-tree-node__expand-icon {
  position: absolute;
  top: 0;
  left: 0;
}
</style>

