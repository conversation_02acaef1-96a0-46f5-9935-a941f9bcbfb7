<template>
  <el-row :gutter="20">
    <div class="app-container" style=" display: flex; ">
      <el-col :span="4">
        <el-row class="tac">
          <el-col :span="24">
            <h5>流程分类</h5>
            <el-menu
              class="el-menu-vertical-demo"
              @select="handleClick"
            >
              <el-menu-item v-for="(val,index) in categoryData"   :index="(val.id).toString()" :key="index">
                <i class="el-icon-orange"></i>
                <span>{{ val.name }}</span>
              </el-menu-item>
            </el-menu>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="20">
        <div>
          <div class="filter-container" style="position: relative">
            <el-col :span="4">
              <div style="display: inline-block;">
                <el-input v-model="listQuery.form_name" placeholder="名称" clearable class="filter-item"
                          @input="handleFilter"/>
              </div>
            </el-col>
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
              搜索
            </el-button>
          </div>
          <el-row >
            <el-col :span="5" style="margin-top: 25px" v-for="(item, index) in list" :key="item.id" :offset="(index+1)%5 > 1 ? 1 : 0">
              <div @click="flowRunStart(item)">
                <el-card  class="card_body" :body-style="{ padding: '0px' }">
                  <el-row>
                    <el-col :span="12" style="width:90px;padding: 20px 0 10px 5px; ">
                      <img src="@/assets/images/workflow.png" height="90" width="90" alt="">
                    </el-col>
                    <el-col :span="12">
                      <div style="padding: 14px;">
                        <p>名称 :<B>{{item.form_name }} </B></p>
                        <p>类型 :<span>{{item.group_name }} </span></p>
                        <p>状态 :<span>{{item.is_stop }} </span></p>
                      </div>
                    </el-col>
                  </el-row>
                </el-card>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </div>
  </el-row>
</template>
<script>
import waves from '@/directive/waves'
import { getFormGroups, getFormItems, workflowRunStart } from '@/api/design'

export default {
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        form_name: ''
      },
      search_data: [],
      listData: {
        page: 1,
        page_size: 50
      },
      options: [],
      importanceOptions: [],
      showReviewer: false,
      categoryData: [],
      temp: {
        form_name: '',
        group_id: ''
      },
      data: []
    }
  },
  created() {
    this.getList()

  },
  methods: {
    //  获取流程
    getList() {
      this.listLoading = true
      getFormItems(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
      getFormGroups().then(response => {
        this.categoryData = response.data
      })
    },
    handleFilter() {
      this.getList()
    },
    handleClick(item) {
      this.listQuery.group_id = item
      this.getList()
    },
    // 发起流程
    flowRunStart(vale) {
      const flow_name = vale.form_name
      this.$confirm('发起流程: ' + flow_name, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        workflowRunStart({ flow_id: vale.id }).then(response => {
          const run_id = response.data.run_id
          const process_id = response.data.process_id
          const run_process_id = response.data.run_process_id
          this.$router.push({
            path: 'admin/initProcess',
            query: {
              formId: vale.id,
              processId: process_id,
              run_process_id: run_process_id,
              runId: run_id
            }
          })
        })
      }).catch(()=>{});
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0;
  margin-left: 10px;
}

.card_body {
  cursor: pointer;
  font-size: 12px;
}
</style>

