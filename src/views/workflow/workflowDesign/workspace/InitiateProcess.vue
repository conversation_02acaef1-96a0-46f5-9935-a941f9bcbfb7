<template>
  <el-row :gutter="20">
    <div class="app-container" style=" display: flex; ">
      <el-col :span="12">
        <el-tabs tab-position="top" style="height: 200px;">
          <el-tab-pane label="表单信息">
            <form-render class="process-form" ref="form" :forms="forms" v-model="formData"/>
          </el-tab-pane>
          <el-tab-pane label="流程信息">
            <process-design/>
          </el-tab-pane>

          <el-dialog title="转交下一步"
                     custom-class="dialog"
                     :visible.sync="dialogFormVisible">
            <div>
              <p><b> 选择接收人</b></p>
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选
              </el-checkbox>
              <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                <el-checkbox style="margin-top:25px;" v-for="val in userList" :label="val" :key="val.id">
                  {{ val.admin_name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div style="margin-top: 25px">
              <p><b> 新增评论</b></p>
              <el-input type="textarea" placeholder="请添加评论" :rows="5" v-model="discussMsg"></el-input>
            </div>
            <div slot="footer" class="dialog-footer">
              <el-button @click="dialogFormVisible = false">取 消</el-button>
              <el-button type="primary" @click="next">确 定</el-button>
            </div>
          </el-dialog>

          <el-dialog title="驳回"
                     custom-class="dialog"
                     :visible.sync="dialogRejectVisible">
            <div style="margin-top: 25px">
              <p><b>回退节点</b></p>
              <el-select v-model="backNode"  clearable placeholder="请选择回退节点">
                <el-option
                  v-for="item in fallbackNode"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </div>

            <div style="margin-top: 25px">
              <p><b> 退回意见</b></p>
              <el-input type="textarea" placeholder="请填写退回意见" :rows="5" v-model="discussMsg"></el-input>
            </div>

            <div slot="footer" class="dialog-footer">
              <el-button @click="dialogRejectVisible = false">取 消</el-button>
              <el-button type="primary" @click="fallback">确 定</el-button>
            </div>
          </el-dialog>

          <!-- 按钮组-->
          <el-row class="button-arr">
            <el-button :style="{ display: btn.submit===true ? '' : 'none' }" type="primary" @click="submit">提交
            </el-button>
            <el-button :style="{ display: btn.cancel===true ? '' : 'none' }" type="warning" @click="workflowCancel">取消
            </el-button>
            <el-button :style="{ display: btn.agree===true ? '' : 'none' }" type="primary" @click="agree">同意
            </el-button>
            <el-button :style="{ display: btn.reject===true ? '' : 'none' }" type="warning" @click="reject">驳回
            </el-button>
            <el-button :style="{ display: btn.end===true ? '' : 'none' }" type="success" @click="workflowEnd">结束
            </el-button>
          </el-row>

        </el-tabs>
      </el-col>
      <el-col :span="11">
        <div class="block">
          <el-timeline>
            <el-timeline-item v-for="(item,index) in discuss" :key="index" :timestamp="item.created_at"
                              placement="top">
              <el-card>
                <p> <b>{{ item.name }}</b> : {{ item.discuss }}  </p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-col>
    </div>
  </el-row>
</template>

<script>
import FormRender from '@/views/workflow/workflowDesign/common/form/FormRender'
import FormDesignRender from '@/views/workflow/workflowDesign/admin/layout/form/FormDesignRender'
import ProcessDesign from '@/views/workflow/workflowDesign/admin/layout/ProcessDesign'
import {
  getFormDetail,
  getNextUser,
  nextUser,
  workflowEnd,
  workflowCancel,
  getFlowDiscuss,
  rollbackNode ,
  getRollbackNode
} from '@/api/design'

export default {
  name: 'InitiateProcess',
  components: { FormDesignRender, FormRender, ProcessDesign},
  data() {
    return {
      loading: false,
      formId: this.$route.query.formId,
      runId: this.$route.query.runId,
      formData:{},
      process_id: this.$route.query.processId ?? 1,
      runProcessId: this.$route.query.run_process_id ,
      userList: [],
      dialogFormVisible: false,
      dialogRejectVisible: false,
      checkAll: false,
      discussMsg: '',
      checkedCities: [],
      isIndeterminate: true,
      btn: {},
      discuss: [],
      fallbackNode: '',
      backNode:  '',
      form: {
        formId: '',
        formName: '',
        formItems: [],
        process: {},
        remark: ''
      }
    }
  },
  mounted() {
    this.loadFormInfo(this.formId)
  },
  computed: {
    forms() {
      return this.$store.state.workflow.design.formItems
    }
  },
  methods: {
    loadFormInfo(formId) {
      this.loading = true
      this.getFlowDiscuss()
      getFormDetail({ form_id: formId, 'process_id': this.process_id,'run_id':this.runId}).then(rsp => {
        this.loading = false
        let form = rsp.data
        form.formItems = JSON.parse(form.formItems)
        form.process = JSON.parse(form.process)
        this.formData = JSON.parse(form.formData)
        this.form = form
        this.btn = form.btn_auth
        this.$store.state.workflow.design = form
      }).catch(err => {
        this.loading = false
        this.$message.error(err)
      })
    },
    validate(call) {
      this.$refs.form.validate(call)
    },
    submit(){
      this.discussMsg = "发起流程"
      this.getNextUser()
    },agree(){
      this.discussMsg = "同意"
      this.getNextUser()
    }, reject(){
      this.discussMsg = "不同意"
      this.getRollbackNode()
    },
    fallback(){
      rollbackNode({
        'form_id': this.formId,
        'run_id': this.runId,
        'run_process_id': this.runProcessId,
        'process_id': this.process_id,
        'back_node': this.backNode,
        'discuss': this.discussMsg
      }).then(rsp => {
        this.dialogRejectVisible = false
      }).catch(err => {
        this.loading = false
        this.$message.error(err)
      })
    },
    // 获取回退节点
    getRollbackNode() {
      getRollbackNode({
        'form_id': this.formId,
        'run_id': this.runId,
        'run_process_id': this.runProcessId,
        'process_id': this.process_id
      }).then(rsp => {
        this.dialogRejectVisible = true
        this.fallbackNode = rsp.data
      }).catch(err => {
        this.loading = false
        this.$message.error(err)
      })
    },
    //  获取下一个步骤审批人
    getNextUser() {
      getNextUser({'process_id':this.process_id}).then(rsp => {
        this.userList = rsp.data.userList
        this.dialogFormVisible = true
      }).catch(err => {
        this.loading = false
        this.$message.error(err)
      })
    },
    // 转交下一个步骤
    next() {
      const data = {
        'form_id': this.formId,
        'run_id': this.runId,
        'run_process_id': this.runProcessId,
        'process_id': this.process_id,
        'form_data': this.formData,
        'process_num': this.process_num,
        'discuss_msg': this.discussMsg,
        'user_list': this.checkedCities
      }
      nextUser(data).then(rsp => {
        if (rsp.meta.status === 200) {
          this.$message.success('转交成功')
          this.$router.push('/WorkManage')
          this.dialogFormVisible = false
        }
      }).catch(err => {
        this.loading = false
        this.$message.error(err)
      })
    },
    workflowEnd() {
      const data = {
        'run_process_id': this.runProcessId,
        'run_id': this.runId
      }
      workflowEnd(data).then(rsp => {
        if (rsp.meta.status === 200) {
          this.$message.success('流程结束')
          this.$router.push('/WorkManage')
          this.dialogFormVisible = false
        }
      }).catch(err => {
        this.loading = false
        this.$message.error(err)
      })
    },
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.userList : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.userList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.userList.length
    },
    workflowCancel() {
      this.$confirm('取消成功:', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        workflowCancel({
          'run_id': this.runId
        }).then(rsp => {
          if (rsp.meta.status === 200) {
            this.$message.success('取消成功')
            this.$router.push('/WorkSpace')
          }
        })
      }).catch(() => {
      })
    },
    to(path) {
      this.$emit('input', path)
    },
    getFlowDiscuss() {
      getFlowDiscuss({
        'run_id': this.runId
      }).then(rsp => {
        if (rsp.meta.status === 200) {
          this.discuss = rsp.data
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.process-form {
  /deep/ .el-form-item__label {
    padding: 0 0;
  }

}

  /deep/ .dialog .el-dialog__body {
    padding: 0 20px !important;
  }

  /deep/ .dialog .el-dialog__header {
    border-bottom: 1px solid #ddd !important;
  }

.button-arr {
  text-align: center;
  position: fixed;
  bottom: 50px;
}
</style>
