<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane>
      <span slot="label">正常</span>
      <disable :lists="lists" />
    </el-tab-pane>
    <el-tab-pane>
      <span slot="label">禁用</span>
      <activation :lists="lists" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import disable from '@/components/administrators/disable.vue'
import activation from '@/components/administrators/activation.vue'
import { companyAdminList } from '@/api/system/admin'

export default {
  name: '',
  components: {
    disable,
    activation
  },
  data() {
    return {
      normal: {
        name: 0
      },
      listQuery: {
        page: 1,
        page_size: 10,
        status: 2
      },
      lists:[]
    }
  },
  created() {

  },
  methods: {
    handleClick(tab) {
      if (tab.index == 1) {
        this.listQuery.status = 2
        companyAdminList(this.listQuery).then(response => {
          this.lists = response.data.data
          this.total = response.data.total
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      }
      if (tab.index == 0) {
        this.listQuery.status = 1
        companyAdminList(this.listQuery).then(response => {
          this.lists = response.data.data

          this.total = response.data.total
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
