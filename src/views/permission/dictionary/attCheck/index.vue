<template>
  <div class="app-container" style="">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="140px"
      class="demo-ruleForm"
    >
      <el-form-item label="经营日报提交人">
        <el-select
          v-model="ruleForm.analysis_examiner"
          multiple
          filterable
          placeholder="经营日报提交人"
          :clearable="false"
        >
          <el-option
            v-for="item in options_check"
            :key="item.id"
            :label="item.user_username"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

<!--      <el-form-item label="请假审核人">-->
<!--        <el-select-->
<!--          v-model="ruleForm.leave_examiner"-->
<!--          multiple-->
<!--          filterable-->
<!--          placeholder="请选择请假审核人"-->
<!--          :clearable="false"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in options_leave"-->
<!--            :key="item.id"-->
<!--            :label="item.user_username"-->
<!--            :value="item.id"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--        <el-form-item label="请假审核人">-->
<!--        <el-select-->
<!--          v-model="ruleForm.leave_examiner"-->
<!--          multiple-->
<!--          filterable-->
<!--          placeholder="请选择请假审核人"-->
<!--          :clearable="false"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in options_leave"-->
<!--            :key="item.id"-->
<!--            :label="item.user_username"-->
<!--            :value="item.id"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->


      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm()"
        >提交
        </el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {attendanceSysInfo, attendanceSet} from '@/api/workAttendance'
import {getCompanyUsers} from '@/api/porject/project'

export default {
  name: 'SetUp',
  data() {
    return {
      value1: '',
      value2: '',
      options_check: [],
      options_leave: [],
      ruleForm: {
        analysis_examiner: [],
        leave_examiner: []
      },
      temp: {
        analysis_examiner: [],
        leave_examiner: []
      },
      rules: {
        leave_examiner: [
          {
            required: true,
            message: '请选择请假审核人',
            trigger: 'change'
          }
        ],
        analysis_examiner: [
          {
            required: true,
            message: '请选择报销审核人',
            trigger: 'change'
          }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getCompanyUsers()
  },
  methods: {
    getList() {
      attendanceSysInfo().then((response) => {
        const data = this.getValueOfUser(response.data[0])
        this.ruleForm.analysis_examiner = data[0]
        this.ruleForm.leave_examiner = data[1]
      })
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options_check = response.data
        this.options_leave = response.data
      })
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (this.ruleForm.analysis_examiner === '') {
            this.$notify({
              title: 'error',
              message: '报销审核人不能为空',
              type: 'error',
              duration: 2000
            })
            return
          }

          // if (this.ruleForm.leave_examiner === '') {
          //   this.$notify({
          //     title: 'error',
          //     message: '请假审核人不能为空',
          //     type: 'error',
          //     duration: 2000
          //   })
          //   return
          // }

          this.ruleForm.analysis_examiner = this.ruleForm.analysis_examiner.toString()
          // this.ruleForm.leave_examiner = this.ruleForm.leave_examiner.toString()

          attendanceSet(this.ruleForm).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    getValueOfUser(data) {
      const analysis_examiner_id = []
      // const leave_examiner_id = []
      for (let i = 0; i < data.analysis_examiner.length; i++) {
        analysis_examiner_id.push(data.analysis_examiner[i].id)
      }

      // for (let i = 0; i < data.leave_examiner.length; i++) {
      //   leave_examiner_id.push(data.leave_examiner[i].id)
      // }

      return [analysis_examiner_id]
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0;
  margin-left: 0;
}
</style>
