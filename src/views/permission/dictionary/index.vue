<template xmlns="">
  <div class="app-container">
    <el-row class="tac">
      <el-col :span="24">
        <el-tabs tab-position="left">

          <el-tab-pane label="项目类型">
            <ProjectType></ProjectType>
          </el-tab-pane>

          <el-tab-pane label="任务类型">
            <TaskType></TaskType>
          </el-tab-pane>

          <el-tab-pane label="复盘题库">
            <Problem></Problem>
          </el-tab-pane>

          <el-tab-pane label="资讯类型">
            <ConsultType></ConsultType>
          </el-tab-pane>

          <el-tab-pane label="文件加密">
            <Encryption></Encryption>
          </el-tab-pane>

          <el-tab-pane label="请假类型">
            <LeaveType></LeaveType>
          </el-tab-pane>

          <el-tab-pane label="审核设置">
            <AttCheck></AttCheck>
          </el-tab-pane>

          <el-tab-pane label="审批设置">
            <Approvers></Approvers>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ProjectType from './projectType/index.vue'
import TaskType from './taskType/index.vue'
import Problem from './problem/index.vue'
import ConsultType from './consultType/index.vue'
import Encryption from './encryption/index.vue'
import LeaveType from './leaveType/index.vue'
import AttCheck from './attCheck/index.vue'
import Approvers from './approvers/index.vue'

export default {
  components: {
    Problem,
    ProjectType,
    ConsultType,
    Encryption,
    LeaveType,
    AttCheck,
    Approvers,
    TaskType
  },
  data() {
    return {}
  },
  created() {
  },
  methods: {}
}
</script>


<style>
.el-tabs--left .el-tabs__header.is-left {
  float: left;
  margin-bottom: 0;
  margin-top: 25px;
  width: 170px;
  margin-right: 10px;
}

.el-tabs--left .el-tabs__item.is-left {
  height: 50px;
  text-align: center;
  line-height: 50px;
}
</style>
