<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">

      <el-select
        v-model="listQuery.reportType"
        filterable
        style="width: 200px; margin-left: 10px; padding: 10px"
        placeholder="流程名称"
      >
        <el-option
          v-for="item in modelList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>

      <el-button class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate">
        新增
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" width="80" align="center"/>
      <el-table-column label="流程名称" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.report_type_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="步骤名称" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.step_title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="步骤" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.step }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.department_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批人名称" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.approver_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.actions')" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{ row, $index }">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            {{ $t('table.edit') }}
          </el-button>
          <el-button v-if="row.status != 'deleted'" size="mini" type="danger" @click="handleDelete(row, $index)">
            {{ $t('table.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-if="!noPagination && total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="110px"
        style="margin-left: 50px;"
      >
        <el-form-item label="流程名称" prop="report_type">
          <el-select v-model="temp.report_type">
            <el-option
              v-for="item in modelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="步骤" prop="step">
          <el-input v-model="temp.step" style="width: 240px;"/>
        </el-form-item>

        <el-form-item label="步骤名称" prop="step">
          <el-input v-model="temp.step_title" style="width: 240px;"/>
        </el-form-item>

        <el-form-item label="部门" prop="department_id">
          <treeselect v-model="temp.department_id" :options="deptOptions" :normalizer="normalizer" :show-count="true"
                      placeholder="请选择部门"
          />
        </el-form-item>

        <el-form-item label="审批人" prop="approver_id">
          <el-select v-model="temp.approver_id" filterable placeholder="请选择" @change="changeIndex">
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.user_username"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading"
                   @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { creatApprovers, delApprovers, editApprovers, getApprovers, getApproversModel } from '@/api/system/approvers'
import { getCompanyDept, getCompanyUsers } from '@/api/system/admin'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Approvers',
  components: { Treeselect, Pagination },
  props: {
    noPagination: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      deptOptions: [],
      listLoading: true,
      listQuery: {
        page: 1,
        reportType: '',
        page_size: 10
      },
      temp: {
        report_type: '',
        step: '',
        step_title: '',
        department_id: 0,
        approver_id: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitLoading: false,
      textMap: {
        update: '编辑审批设置',
        create: '新增审批设置'
      },
      modelList: [],
      rules: {
        report_type: [{ required: true, message: '请输入类型', trigger: 'blur' }],
        step: [{ required: true, message: '请输入步骤', trigger: 'blur' }],
        department_id: [{ required: true, message: '请选择部门', trigger: 'blur' }],
        approver_id: [{ required: true, message: '请输入审批人', trigger: 'blur' }],
        step_title: [{ required: true, message: '请输入审批人', trigger: 'blur' }]
      },
      options: [] // 审批人用户列表
    }
  },
  created() {
    this.getModelList()
    this.getTreeSelect()
    this.getCompany()
    this.getList()
  },
  methods: {
    handleFilter() {
      this.getList()
    },
    normalizer(node) {
      if (node.child && !node.child.length) {
        delete node.child
      }
      return {
        id: node.id,
        label: node.department_name,
        children: node.child
      }
    },
    changeIndex(user_id) {
      this.temp.approver_id = user_id
    },
    getCompany() {
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },
    indexAdd(index) {
      const { page, page_size } = this.listQuery
      return index + 1 + (page - 1) * page_size
    },
    resetTemp() {
      this.temp = {
        step_title: '',
        report_type: '',
        step: '',
        department_id: 0,
        approver_id: ''
      }
    },
    getList() {
      this.listLoading = true
      getApprovers(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    getModelList() {
      getApproversModel().then(response => {
        this.modelList = response.data
      })
    },
    getTreeSelect() {
      getCompanyDept().then(response => {
        this.deptOptions = []
        const dept = { id: 0, department_name: '主类目', child: [] }
        dept.child = response.data
        this.deptOptions.push(dept)
      })
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          creatApprovers(this.temp).then(() => {
            this.dialogFormVisible = false
            this.submitLoading = false
            this.getList()
            this.$notify({
              title: 'Success',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitLoading = false
          })
        }
      })
    },
    handleUpdate(row) {

      this.temp = { ...row }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          const tempData = { ...this.temp }
          editApprovers(tempData).then(() => {
            this.dialogFormVisible = false
            this.submitLoading = false
            this.getList()
            this.$notify({
              title: 'Success',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitLoading = false
          })
        }
      })
    },
    handleDelete(row, index) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delApprovers({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.list.splice(index, 1)
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        this.$notify({
          title: 'Info',
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 0px;
}

.vue-treeselect {
  width: 200px;
}
</style>
