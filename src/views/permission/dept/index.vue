<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form :inline="true">
        <el-form-item label="部门名称">
          <el-input
            v-model="queryParams.department_name"
            placeholder="请输入部门名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            size="small"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="handleAdd"
          >新增</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-table
          v-loading="loading"
          :data="deptList"
          style="width: 100%;margin-bottom: 20px;"
          row-key="id"
          border
          default-expand-all
          :tree-props="{children: 'child'}"
        >
          <el-table-column prop="department_name" label="部门名称" align="left" sortable />
          <el-table-column prop="sort" label="排序" align="left" sortable />
          <el-table-column prop="name" label="状态" align="center" sortable width="200">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.status === '1' ? 'danger' : 'success'"
                disable-transitions
              >{{ scope.row.status === '1' ? '停用' : '正常' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="address" align="center" label="创建时间">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" align="center" label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)">新增</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

      </div>

    </el-card>
    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级菜单" prop="pid" style="width: 95%">
              <treeselect
                v-model="form.pid"
                :options="deptOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="部门名称" prop="department_name">
              <el-input v-model="form.department_name" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" :min="0" :step="1" placeholder="请输入排序" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="部门状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { listUser } from '@/api/workflow/sysuser'
import { getCompanyDept, delCompanyDept, addCompanyDept, upCompanyDept, getDeptList } from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Dept',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      options: [],
      // 弹出层标题
      title: '',
      isEdit: false,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        page_size: 10,
        deptName: undefined,
        status: undefined
      },
      users: [],
      // 表单参数
      form: {
        id: undefined,
        pid: undefined,
        department_name: undefined,
        sort: 0
      },
      // 表单校验
      rules: {
        pid: [
          { required: true, message: '上级部门不能为空', trigger: 'blur' }
        ],
        department_name: [
          { required: true, message: '部门名称不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' },
          { type: 'number', message: '排序必须为数字', trigger: 'blur' }
        ]
      },
      statusOptions: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  },
  created() {
    this.getList()
    // this.superior()
  },
  methods: {
    open1() {
      this.$notify({
        message: '编辑成功',
        type: 'success'
      })
    },
    /** 查询部门列表 */
    getList() {
      this.loading = true
      getCompanyDept(this.queryParams).then(response => {
        this.deptList = response.data
        this.options = response.data
        this.loading = false
      })
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.child && !node.child.length) {
        delete node.child
      }
      return {
        id: node.id,
        label: node.department_name,
        children: node.child
      }
    },
    /** 查询部门下拉树结构 */
    getTreeselect(e) {
      getDeptList().then(response => {
        this.deptOptions = []
        if (e === 'update') {
          const dept = { id: 0, department_name: '主类目', child: [] }
          dept.child = response.data
          this.deptOptions.push(dept)
        } else {
          const dept = { id: 0, department_name: '主类目', child: [] }
          dept.child = response.data
          this.deptOptions.push(dept)
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        pid: undefined,
        department_name: undefined,
        sort: 0,
        status: '0'
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.open = true
      this.reset()
      this.getTreeselect('add')
      if (row !== undefined) {
        this.form.pid = row.id
      }
      this.title = '添加部门'
      this.isEdit = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect('update')
      this.form = row
      this.open = true
      this.title = '修改部门'
      this.isEdit = true
      // })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const payload = {
            id: this.form.id,
            pid: this.form.pid,
            department_name: this.form.department_name,
            sort: this.form.sort
          }

          if (this.title === '修改部门') {
            upCompanyDept(payload).then(response => {
              if (response.meta.status === 200) {
                this.open = false
                this.$notify({
                  message: '编辑成功',
                  type: 'success'
                })
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addCompanyDept(payload).then(response => {
              if (response.meta.status === 200) {
                this.open1()
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(
        '是否确认删除名称为"' + row.department_name + '"的数据项?',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(function() {
          return delCompanyDept({ id: row.id })
        })
        .then(() => {
          this.getList()
          this.msgSuccess('删除成功')
        })
        .catch(function() {})
    }
  }
}
</script>
