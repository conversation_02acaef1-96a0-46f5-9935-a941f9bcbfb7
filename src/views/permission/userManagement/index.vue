<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane>
      <span slot="label">在职</span>
      <Normal :lists="lists" />
    </el-tab-pane>
    <el-tab-pane label="离职">
      <Forbidden :lists="lists" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import Normal from '@/components/userManagement/normal.vue'
import Forbidden from '@/components/userManagement/forbidden.vue'
import { companyUserList, } from '@/api/administrators'
export default {
  name: '',
  components: {
    Normal,
    Forbidden
  },
  data() {
    return {
      listQuery: {
        page: 1,
        page_size: 10,
        state: 2
      },
      lists:[]
    }
  },
  methods: {
    handleClick(tab) {
      if (tab.index == 1) {
        this.listQuery.state = 2
        companyUserList(this.listQuery).then(response => {
          this.lists = response.data.data
          this.total = response.data.total
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      }
      if (tab.index == 0) {
        this.listQuery.state = 1
        companyUserList(this.listQuery).then(response => {
          this.lists = response.data.data
          this.total = response.data.total
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      }
    }
  }
}
</script>
<style scoped>

</style>
