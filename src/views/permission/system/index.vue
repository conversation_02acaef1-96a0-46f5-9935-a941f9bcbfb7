<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane>
      <span slot="label">系统管理</span>
      <System />
    </el-tab-pane>
    <el-tab-pane label="文件加密">
      <Encryption />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import System from '@/views/permission/dictionary/attCheck'
import Encryption from '@/views/permission/dictionary/encryption'

export default {
  name: '',
  components: {
    System,
    Encryption
  },
  data() {
    return {}
  },
  created() {

  },
  methods: {
    handleClick(tab, event) {
      // 费用申请(tab, event)
    }
  }
}
</script>
<style scoped>

</style>
