<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form ref="queryForm" :model="queryParams" :inline="true">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="queryParams.roleName" placeholder="请输入角色名称" clearable size="small" style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" border :data="roleList" @selection-change="handleSelectionChange">
        <el-table-column label="序号" type="index" prop="index" align="center" width="150" />
        <el-table-column label="角色名称" prop="role_name" :show-overflow-tooltip="true" align="center" width="150" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status == '1' ? 'success' : 'danger'" disable-transitions>{{ scope.row.status ==
              '1' ? '正常' : '禁止'
            }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" align="center" width="150" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">{{
              scope.row.status == '1' ? '禁用' : '启用'
            }}
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-unlock" @click="handleJurisdiction(scope.row)">标签权限
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 标签权限弹框 -->
      <el-dialog title="标签权限" :visible.sync="dialogVisible" width="60%">
        <el-tabs v-model="editableTabsValue" type="border-card" @tab-click="handleSwitch">
          <el-tab-pane v-for="(item, index) in msgList" :key="index" :label="item.category_name" @change="category(item)">
            <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%;">
              <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="80" />
              <el-table-column label="标签名称" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.label_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="标签规则" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.label_role }}</span>
                </template>
              </el-table-column>
              <el-table-column width="400px" label="标签等级" align="center">
                <template slot-scope="{row}">
                  <template v-if="row.edit">
                    <el-input v-model="row.label_level" class="edit-input" size="small" />
                    <el-button class="cancel-btn" size="small" icon="el-icon-refresh" type="warning"
                      @click="cancelEdit(row)">
                      返回
                    </el-button>
                  </template>
                  <span v-else>{{ row.label_level }}</span>
                </template>
              </el-table-column>

              <el-table-column align="center" label="操作" width="120">
                <template slot-scope="{row}">
                  <el-button v-if="row.edit" type="success" size="small" icon="el-icon-circle-check-outline"
                    @click="confirmEdit(row)">
                    Ok
                  </el-button>
                  <el-button v-else type="text" size="small" icon="el-icon-edit" @click="edits(row)">
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>
      <!-- 添加或修改角色配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="40%">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="form.roleName" placeholder="请输入角色名称" :disabled="isEdit" />
          </el-form-item>
          <!--  -->
          <div class="block">
            <el-tree ref="menu" :data="menuOptions" show-checkbox :default-expand-all="true" :expand-on-click-node="false"
              node-key="id" empty-text="加载中，请稍后" :props="defaultProps">
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span v-if="data.is_auth !== 0">
                  <el-radio v-model="radio[data.id]" label="1">个人</el-radio>
                  <el-radio v-model="radio[data.id]" label="2">部门</el-radio>
                  <el-radio v-model="radio[data.id]" label="3">公司</el-radio>
                </span>
              </span>
            </el-tree>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { getCompanyRole, getRole, delRole, addRole, upCompanyRole } from '@/api/system/role'
import { getMenuList } from '@/api/system/menu'
import { labelList, getCategoryList } from '@/api/smartLabel'
// import { isArray } from '@/utils/validate'
// import Pagination from '@/components/Pagination' 分页
export default {
  name: 'Role',
  // components: { Pagination },
  data() {
    return {
      data: [],
      radio: [],
      editableTabsValue: '0',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      isEdit: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        roleName: undefined,
        roleKey: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        id: '',
        arrId: []
      },
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '权限字符不能为空', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      listQuery: {
        page: 1,
        page_size: 200,
        category: undefined,
        created_at: undefined,
        is_belong_peo: 2,
        role_level: '' // 点击标签权限的id
      },
      list: [],
      options1: [],
      temp: {
        label_name: '',
        label_role: '',
        label_level: '',
        label_color: '',
        category_name: '',
        category_id: '',
        category: ''
      },
      totals: 0,
      items: null,
      listLoading: false,
      msgList: [],
      weight: '',
      partId: '',
      storageData: [],
      arr: [],
      subbtn_list: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    edits(row) {
      if (row.edit === false) {
        row.edit = true
      } else {
        row.edit = false
      }
    },
    // tab过滤列表
    handleSwitch(tab) {
      const index = parseInt(tab.paneName)
      const aa = this.msgList[index]
      this.msgList.id = aa.id
      this.listQuery.role_level = this.partId
      this.listQuery.category = parseInt(tab.paneName) + 1
      this.labelList()
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true
      getCompanyRole(this.addDateRange(this.queryParams, this.dateRange)).then(
        response => {
          this.roleList = response.data
          this.total = response.data.count
          this.loading = false
        }
      )
    },
    // 标签权限
    handleJurisdiction(row) {
      this.partId = row.id
      this.listQuery.role_level = row.id
      this.getCategoryList()
      this.editableTabsValue = '0'
      this.dialogVisible = true
      this.listQuery.category = 1
      this.labelList()
    },
    // 请求列表
    labelList() {
      labelList(this.listQuery).then(response => {
        this.list = response.data.data
        this.totals = response.data.total
        this.items = this.list.map(v => {
          this.$set(v, 'edit', false)
          v.originalTitle = v.title
          return v
        })
      })
    },
    // 标签类型
    getCategoryList() {
      getCategoryList({ category: this.temp.category }).then(response => {
        this.msgList = response.data
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.handleJurisdiction()
    },
    // 确认编辑按钮
    confirmEdit(row) {
      this.list.forEach((item) => {
        this.$set(this.arr, item.id, item.label_level)
      })
      row.edit = false
      row.label_level = parseFloat(row.label_level)
      this.arr.forEach((item, index) => {
        if (index === row.id) {
          this.arr[index] = parseFloat(row.label_level)
        }
      })
      let aa = ''
      this.arr.map((item, index) => {
        aa += index + ':' + item + ','
      })
      aa = aa.substr(0, aa.length - 1)
      const obj = {
        id: this.partId, // 点击标签权限的id
        label_type: this.msgList.id,
        label_data: aa
      }
      if (obj.label_type === undefined) {
        obj.label_type = 1
      }
      upCompanyRole(obj).then((res) => {
        if (res) {
          this.$message({
            message: '等级已编辑',
            type: 'success'
          })
        }
      })
    },
    cancelEdit(row) {
      // row.label_level = row.label_level
      row.edit = false
      this.$message({
        message: '所有权已恢复到原值',
        type: 'warning'
      })
      this.handleJurisdiction()
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      getMenuList().then(response => {
        this.menuOptions = response.data
      })
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      const checkedKeys = this.$refs.menu.getHalfCheckedKeys()
      // 半选中的菜单节点
      const halfCheckedKeys = this.$refs.menu.getCheckedKeys()
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
      return checkedKeys
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(data) {
      // 勾选菜单
      var roleId = data.role_id.split(',')
      // 勾选菜单权限
      var auth = data.auth_id.split(',')
      const authData = []
      auth.map((item, index) => {
        var param = item.split(':')
        authData[param[0]] = param[1]
        return authData
      })
      this.radio = authData
      this.getMenuTreeselect()
      this.$nextTick(() => {
        this.$refs.menu.setCheckedKeys(roleId)
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedKeys([])
      }
      this.form = {
        id: '',
        roleId: '',
        roleName: '',
        roleKey: '',
        menuIds: [],
        remark: '',
        arrId: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roleId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getMenuTreeselect()
      this.open = true
      this.title = '添加角色'
      this.isEdit = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.id = row.id
      getRole({ id: row.id }).then(response => {
        this.form.roleName = response.data[0].role_name
        this.open = true
        this.title = '修改角色'
        this.isEdit = true
        this.getRoleMenuTreeselect(response.data[0])
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== '') {
            this.form.menuIds = this.getMenuAllCheckedKeys()
            const arr = []
            this.radio.forEach((item, index) => {
              this.form.menuIds.forEach(val => {
                if (index === val) {
                  arr.push(val + ':' + item)
                }
              })
            })
            this.radio.forEach((item, index) => {
              this.form.menuIds.forEach(val => {
                if (index !== val) {
                  item = 0
                  arr.push(val + ':' + item)
                }
              })
            })
            this.form.menuIds = arr.join(',')
            upCompanyRole(this.form).then(response => {
              if (response.meta.status === 200) {
                this.msgSuccess('修改成功')
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {

            const arr = []
            this.form.menuIds = this.getMenuAllCheckedKeys()

            this.radio.forEach((item, index) => {
              this.form.menuIds.forEach(val => {
                if (index === val) {
                  arr.push(val + ':' + item)
                }
              })
            })

            this.radio.forEach((item, index) => {
              this.form.menuIds.forEach(val => {
                if (index !== val) {
                  item = 0
                  arr.push(val + ':' + item)
                }
              })
            })
            this.form.menuIds = arr.join(',')

            console.log( this.form.menuIds )


            this.form.role_name = this.form.roleName
            addRole(this.form).then(response => {
              if (response.meta.status === 200) {
                this.msgSuccess('新增成功')
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      const status = row.status > 0 ? -1 : 1
      this.$confirm('是否修角色"' + row.role_name + '"的数据项状态?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delRole({ 'id': id, 'status': status })
      }).then(() => {
        this.getList()
        this.msgSuccess('禁用成功')
      }).catch(function () {
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

::v-deep .el-dialog {
  margin-top: 5vh;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
