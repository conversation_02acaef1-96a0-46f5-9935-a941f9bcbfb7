<template>
  <div class="app-container">
    <div class="filter-container" style="display: flex; align-items: center;  ">
      <div style="display: inline-block;">
        <el-date-picker
            v-model="searchTime"
            unlink-panels
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="daterange"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleFilter"
        />
      </div>
      <el-select
          v-model="receiveId"
          multiple
          filterable
          style="width: 200px; margin-left: 10px; padding: 10px"
          placeholder="用户"
          @visible-change="getCompanyUsers"
          @change="handleFilter"
      >
        <el-option
            v-for="item in options_task"
            :key="item.user_username"
            :label="item.user_username"
            :value="item.id"
        />
      </el-select>
      <el-input
          v-model="listQuery.module"
          style="width: 7vw;margin-right: 7px;"
          placeholder="请输入模块名称"
          @change="changModel"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button
          :loading="downloadLoading"
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleDownload"
      >
        导出
      </el-button>
    </div>
    <el-table v-loading="listLoading" :data="list" border highlight-current-row default-expand-all>
      <el-table-column label="用户名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.dept }}</span>
        </template>
      </el-table-column>
      <el-table-column label="日期" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="模块名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.module }}</span>
        </template>
      </el-table-column>
      <el-table-column label="日志标题" align="center">
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="日志内容" align="center">
        <template slot-scope="{row}">
          <span>{{ row.content }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作次数" align="center">
        <template slot-scope="{row}">
          <span>{{ row.count }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.page_size"
        @pagination="getList"
    />
  </div>
</template>
<script>
import {
  getCompanyUsers,getUserLog
} from '@/api/system/admin'

import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  data() {
    return {
      receiveId: [],
      uid: [],
      list: [],
      total: 0,
      options_task: [],
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        user_id: '',
        search_time: '',
        remind: '',
        module: ''
      },
      searchTime: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 1 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      options: [],
      options1: [],
      downloadLoading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    changModel(e) {
      this.listQuery.module = e
      this.getList()
    },
    formatDate(value) { // 时间戳转换日期格式方法
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options_task = response.data
      })
    },
    getList() {
      if (this.searchTime) {
        this.listQuery.search_time = this.searchTime[0] + '——' + this.searchTime[1]
      }
      this.listLoading = true
      getUserLog(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleFilter() {
      this.listQuery.user_id = this.receiveId.toString()
      this.listQuery.remind = this.uid
      this.listQuery.page = 1
      this.getList()
      if (this.searchTime == null) {
        this.listQuery.search_time = ''
      }
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['用户名称', '部门', '日期', '模块名称', '日志标题', '日志内容', '访问次数']
        const filterVal = ['user_id', 'dept', 'created_at', 'module', 'name', 'content', 'count']
        let explode_data = []
        this.listQuery.page_size = 1000
        await getUserLog(this.listQuery).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data.data
            this.listQuery.page_size = 10
          }
        })
        const data = this.formatJson(explode_data, filterVal)

        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '系统日志'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
          filterVal.map((j) => {
            return v[j]
          })
      )
    }
  }
}
</script>
