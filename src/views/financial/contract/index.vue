<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane>
      <span slot="label">收入合同</span>
      <Income />
    </el-tab-pane>
    <el-tab-pane label="支出合同">
      <Expenditure />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import Income from '@/components/contract/income.vue'
import Expenditure from '@/components/contract/expenditure.vue'

export default {
  name: '',
  components: {
    Income,
    Expenditure
  },
  data() {
    return {}
  },
  created() {

  },
  methods: {
    handleClick(tab, event) {
      // 费用申请(tab, event)
    }
  }
}
</script>
<style scoped>

</style>
