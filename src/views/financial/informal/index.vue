<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane label="费用报销">
      <reimburse/>
    </el-tab-pane>
    <el-tab-pane label="付款申请">
      <application/>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import reimburse from '@/components/informal/reimburse'
import application from '@/components/informal/application'
export default {
  name: '',
  components: {
    reimburse,
    application
  },
  data() {
    return {}
  },
  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>
<style scoped>

</style>
