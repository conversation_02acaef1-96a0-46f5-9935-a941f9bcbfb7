<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane label="客户">
      <Normal />
    </el-tab-pane>
    <el-tab-pane label="供应商">
      <Forbidden />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import Normal from '@/components/customer/normal.vue'
import Forbidden from '@/components/customer/forbidden.vue'

export default {
  name: '',
  components: {
    Normal,
    Forbidden
  },
  data() {
    return {}
  },
  created() {

  },
  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>

