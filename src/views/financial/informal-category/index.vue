<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane>
      <span slot="label">报销类型</span>
      <reimbursement />
    </el-tab-pane>
    <el-tab-pane label="费用类型">
      <cost />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import reimbursement from '@/components/informal-category/reimbursement.vue'
import cost from '@/components/informal-category/cost.vue'
export default {
  name: '',
  components: {
    reimbursement,
    cost
  },
  data() {
    return {}
  },
  created() {
  },
  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>
