<template>
  <div>
    <div style="padding: 15px 0">
      <!-- 搜索 -->
      <el-select v-model="listQuery.userId" placeholder="申请人" clearable multiple filterable style="width: 200px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, inx) in importanceOptions4" :key="inx" :label="item.user_username" :value="item.id" />
      </el-select>

      <el-date-picker
        v-model="listQuery.search_time"
        style="margin: 0 10px"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleFilter"
      />

      <el-select v-model="listQuery.pro_id" placeholder="项目名称" clearable filterable style="width: 190px;margin-right: 20px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in options1" :key="i" :label="item.pro_name" :value="item.id" />
      </el-select>

      <el-select v-model="listQuery.customer_id" placeholder="按客户名称搜索" clearable filterable class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in options" :key="i" :label="item.customer_name" :value="item.id" />
      </el-select>

      <el-button type="primary" style="margin-left: 10px" @click="handleFilter">搜索</el-button>
      <el-button type="primary" @click="handleCreate()">新增</el-button>
    </div>
    <div>
      <!-- 表单 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%;margin: auto"
        border
      >
        <el-table-column
          label="序号"
          type="index"
          sortable="custom"
          align="center"
          width="80"
        />

        <el-table-column prop="name" label="项目编号">
          <template slot-scope="{row}">
            <span>{{ row.item_num }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="项目名称">
          <template slot-scope="{row}">
            <span>{{ row.pro_name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="项目类型">
          <template slot-scope="{row}">
            <span>{{ row.pro_type }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="发票代码">
          <template slot-scope="{row}">
            <span>{{ row.invoice_code }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="发票号码">
          <template slot-scope="{row}">
            <span>{{ row.invoice_num }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="开票时间">
          <template slot-scope="{row}">
            <span>{{ row.date }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="发票类型">
          <template slot-scope="{row}">
            <span>{{ row.invoice_type }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="开票内容">
          <template slot-scope="{row}">
            <span>{{ row.invoice_text }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="购买方名称">
          <template slot-scope="{row}">
            <span>{{ row.customer_id }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="不含税金额">
          <template slot-scope="{row}">
            <span>{{ row.tax_money }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="税率">
          <template slot-scope="{row}">
            <span>{{ row.tax_rate }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="税额">
          <template slot-scope="{row}">
            <span>{{ row.tax_paid }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="发票金额">
          <template slot-scope="{row}">
            <span>{{ row.invoice_text }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('table.actions')"
          align="center"
          width="200px"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.page_size"
        @pagination="getList"
      />
    </div>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        :inline="true"
        size="medium"
        label-width="150px"
      >
        <el-form-item label="申请人" prop="user_id">
          <el-select
            v-model="temp.user_id"
            placeholder="请选择申请人员"
            clearable
            filterable
            style="width: 190px"
            class="filter-item"
          >
            <el-option
              v-for="(item, i) in importanceOptions4"
              :key="i"
              :label="item.user_username"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="申请部门" prop="department">
          <el-input v-model="temp.department" type="number" placeholder="请选择申请部门" disabled />
        </el-form-item>

        <el-form-item label="申请日期" prop="date">
          <el-date-picker
            v-model="temp.date"
            type="date"
            style="width: 190px"
            placeholder="选择日期"
          />
        </el-form-item>

        <el-form-item label="项目名称" prop="pro_id">
          <el-select
            v-model="temp.pro_id"
            placeholder="请选择项目"
            clearable
            filterable
            style="width: 190px"
            class="filter-item"
          >
            <el-option
              v-for="(item, i) in options1"
              :key="i"
              :label="item.pro_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="客户" prop="customer_id">
          <el-select
            v-model="temp.customer_id"
            placeholder="请选择客户"
            clearable
            filterable
            style="width: 190px"
            class="filter-item"
          >
            <el-option
              v-for="(item, i) in options"
              :key="i"
              :label="item.customer_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="发票类型" prop="invoice_type">
          <el-input v-model="temp.invoice_type" placeholder="请输入发票类型" />
        </el-form-item>

        <el-form-item label="开票金额" prop="invoice_amount">
          <el-input v-model="temp.invoice_amount" type="number" style="width: 190px" placeholder="请输入开票金额" />
        </el-form-item>

        <el-form-item label="税率" prop="tax_rate">
          <el-input v-model="temp.tax_rate" type="number" style="width: 190px" placeholder="请输入税率" />%
        </el-form-item>

        <el-form-item label="发票抬头" prop="invoice_title">
          <el-input v-model="temp.invoice_title" placeholder="请输入发票抬头" />
        </el-form-item>

        <el-form-item label="发票代码" prop="invoice_code">
          <el-input v-model="temp.invoice_code" placeholder="请输入发票代码" />
        </el-form-item>

        <el-form-item label="发票号码" prop="invoice_num">
          <el-input v-model="temp.invoice_num" type="number" placeholder="请输入发票号码" />
        </el-form-item>

        <el-form-item label="不含税金额" prop="tax_money">
          <el-input v-model="temp.tax_money" type="number" placeholder="请输入不含税金额" />
        </el-form-item>

        <el-form-item label="	税额" prop="tax_paid">
          <el-input v-model="temp.tax_paid" type="number" placeholder="请输入税额" />
        </el-form-item>

        <el-form-item label="开票内容" prop="invoice_text">
          <el-input v-model="temp.invoice_text" type="textarea" :rows="2" placeholder="请输入开票内容" style="width: 30vw" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getproList } from '@/api/porject/project'
import { getAllUserInfo } from '@/api/user'
import { CustomerList } from '@/api/customer'
import { invoiceList, invoiceAdd, invoiceEdit } from '@/api/invoice'
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  data() {
    return {
      options1: [], // 项目名称
      options: [], // 按客户搜
      importanceOptions4: [], // 申请人
      listQuery: {
        page: 1,
        page_size: 10,
        user_id: '',
        userId: [],
        date: '',
        pro_id: '',
        customer_id: '',
        search_time: ''
      },
      rules: {
        user_id: [{ required: true, message: '请选择申请人', trigger: 'change' }],
        date: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
        customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
        pro_id: [{ required: true, message: '请选择项目', trigger: 'change' }],
        invoice_type: [{ required: true, message: '请输入发票类型', trigger: 'blur' }],
        invoice_text: [{ required: true, message: '请输入开票内容', trigger: 'blur' }],
        invoice_amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        tax_rate: [{ required: true, message: '请输入税率', trigger: 'blur' }],
        invoice_title: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }]
      },
      tableData: [],
      textMap: {
        create: '新增销项发票',
        update: '编辑销项发票'
      },
      loading: false,
      total: 0,
      temp: {
        user_id: '',
        department: 1,
        date: '',
        customer_id: '',
        invoice_type: '',
        invoice_text: '',
        invoice_amount: '',
        tax_rate: '',
        invoice_title: '',
        id: '',
        tax_paid: '',
        tax_money: '',
        invoice_num: '',
        invoice_code: ''
      },
      dialogVisible: false
    }
  },
  created() {
    this.getList()
    this.handleCustomerList()
  },
  methods: {
    getList() {
      this.loading = true
      invoiceList(this.listQuery).then(response => {
        this.tableData = response.data.data
        this.total = response.data.total
      })
      if (this.listQuery.search_time) {
        this.listQuery.date = this.formatDate(this.listQuery.search_time[0]) + '——' + this.formatDate(this.listQuery.search_time[1])
      }
      if (this.listQuery.userId !== '') {
        this.listQuery.user_id = this.listQuery.userId.join(',')
      }
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    // 搜索
    handleFilter() {
      this.listLoading = true
      if (this.listQuery.search_time == null) {
        this.listQuery.date = ''
      }
      this.listQuery.page = 1
      this.getList()
      setTimeout(() => {
        this.listLoading = false
      }, 500)
    },
    resetTemp() {
      this.temp = {
        user_id: '',
        department: '1',
        date: '',
        customer_id: '',
        invoice_type: '',
        invoice_text: '',
        invoice_amount: '',
        tax_rate: '',
        invoice_title: '',
        id: '',
        tax_paid: '',
        tax_money: '',
        invoice_num: '',
        invoice_code: ''
      }
    },
    // 新增
    handleCreate() {
      this.dialogVisible = true
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.date = this.formatDate(new Date())
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, handleMerge() {
      this.show_pop = true
    },
    createData() {
      this.listLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          invoiceAdd(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
      this.getList()
    },
    // 编辑
    handleUpdate(row) {
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.temp.id = row.id
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.id = row.id
      this.temp.department = 1
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          invoiceEdit(this.temp).then(response => {
            this.dialogVisible = false
            this.getList()
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleCustomerList() {
      // 获取项目名称
      getproList({ page: 1, page_size: 40 }).then(response => {
        this.options1 = response.data.data
      }) // 获取申请人
      getAllUserInfo().then((response) => {
        this.importanceOptions4 = response.data
      }) // 客户列表
      CustomerList({ page: 1, page_size: 10, status: 1 }).then(res => {
        this.options = res.data.data
      })
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
