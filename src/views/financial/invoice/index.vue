<template>
  <el-tabs type="border-card" @tab-click="handleClick">
    <el-tab-pane>
      <span slot="label">销项发票</span>
      <Output />
    </el-tab-pane>
    <el-tab-pane label="进项发票">
      <Receipts />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import Output from '@/components/invoice/output.vue'
import Receipts from '@/components/invoice/receipts.vue'

export default {
  name: '',
  components: {
    Output,
    Receipts
  },
  data() {
    return {}
  },
  created() {

  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event)
    }
  }
}
</script>
<style scoped>

</style>
