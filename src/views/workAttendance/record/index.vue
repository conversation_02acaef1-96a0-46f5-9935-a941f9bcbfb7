<template>
  <div class="app-container" style=" display: flex; ">
    <div
      style="width:228px; min-height:400px; border: 1px solid #e5eaf4; height:auto; margin-right: 20px; padding:20px ;  "
    >
      <el-input v-model="usertree" placeholder="请选择人员" clearable />
      <el-tree
        ref="tree"
        style="margin-top: 30px"
        class="filter-tree"
        :default-expand-all="true"
        :data="user_tree"
        :props="defaultProps"
        :accordion="true"
        :filter-node-method="filterNode"
      >
        <span slot-scope=" { node, data }" class="custom-tree-node">
          <div class="tree-name">{{ data.department_name }}</div>
          <div v-for="(item,index) in data.user_list" :key="index" class="tree-title" @click="handleClick(item)">{{ item.label }}</div>
        </span>
      </el-tree>
    </div>
    <div style=" flex: 1; border: 1px solid #e5eaf4 ; padding: 20px;">
      <div class="filter-container" style="position: relative">
        <div style="display: inline-block;">
          <el-date-picker
            v-model="search_data"
            unlink-panels
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="daterange"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleFilter"
          />
        </div>
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
        <el-button
          v-waves
          :loading="downloadLoading"
          class="filter-item"
          type="primary"
          icon="el-icon-download"
          @click="handleDownload"
        >
          导出
        </el-button>
      </div>
      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
      >
        <el-table-column
          label="序号"
          type="index"
          :index="indexAdd"
          sortable="custom"
          align="center"
          width="80"
        />
        <el-table-column label="日期" align="center">
          <template slot-scope="{row}">
            <span>{{ row.work_date }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template slot-scope="{row}">
            <span>{{ row.user_id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上班打卡时间" align="center">
          <template slot-scope="{row}">
            <span
              style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;"
            >{{ row.work_time_go }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上班打卡状态" align="center">
          <template slot-scope="{row}">
            <span>{{ row.status }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上班打卡位置" align="center">
          <template slot-scope="{row}">
            <span>{{ row.work_location_go }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下班打卡时间" align="center">
          <template slot-scope="{row}">
            <span>{{ row.work_time_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下班打卡状态" align="center">
          <template slot-scope="{row}">
            <span>{{ row.off_work_status }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下班打卡位置" align="center">
          <template slot-scope="{row}">
            <span>{{ row.work_location_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column label="日工时" align="center">
          <template slot-scope="{row}">
            <span>{{ row.work_hours }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.page_size"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import { projectAdd, getCompanyUsers, projectEdit, projectDel } from '@/api/porject/project'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import { getAllUserInfo } from '@/api/user'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { attendanceList, resultingtree } from '@/api/workAttendance'

export default {
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'child',
        label: 'department_name'
      },
      importanceOptions4: [],
      popshow: false,
      tableKey: 0,
      user_tree: [],
      usertree: '',
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 50,
        end_time: undefined,
        user_id: undefined,
        search_time: null
      },
      listQueryAdd: {
        company_id: ''
      },
      search_data: [],
      listData: {
        page: 1,
        page_size: 50
      },
      options: [],
      importanceOptions: [],
      importanceOptions1: [],
      importanceOptions2: [
        {
          status: '待完成',
          id: '1'
        },
        {
          status: '已完成',
          id: '2'
        }
      ],
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      statusOptions: ['published', 'draft', 'deleted'],
      showReviewer: false,
      temp: {
        company_id: '',
        pro_name: '',
        pro_managers: '',
        pro_type: '',
        search_time: '',
        pro_managers_id: ''
      },
      data: [],
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      pvData: [],
      rules: {
        company_id: [{ required: true, message: '请选择公司名称', trigger: 'change' }],
        pro_name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        pro_managers: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
        pro_type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
        search_time: [{ required: true, message: '请选择日期', trigger: 'change' }]
      },
      downloadLoading: false,
      numId: 0
    }
  },
  watch: {
    usertree(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList() // 考勤记录
    this.getTree() // 公司结构树
    this.deFault()
  },
  methods: {
    handleClick(item) {
      this.usertree = item.label
      this.listQuery.user_id = item.user_id
      this.getList()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return (page - 1) * pagesize + index + 1
    },
    searchList() {
      getAllUserInfo().then(response => {
        this.importanceOptions4 = response.data
      })
    },
    deFault() {
      this.listLoading = true
      this.userLabel = []
      this.user_id = localStorage.getItem('userID')
      attendanceList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
      })
      this.listLoading = false
    },
    handleNodeClick(data) {
      this.listLoading = true
      this.usertree = data.label
      this.listQuery.user_id = data.user_id
      attendanceList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 500)
      })
    },
    getTree() {
      // this.listLoading = true
      // resultingtree(this.listQuery).then(response => {
      //   this.handleData(response.data)
      //   this.handleDatas(response.data[0].user_list)
      //   this.user_tree = response.data[0].user_list
      //   this.handleChild(this.user_tree)
      //   setTimeout(() => {
      //     this.listLoading = false
      //   }, 100)
      // })
      this.listLoading = true
      resultingtree(this.listQuery).then(response => {
        this.user_tree = response.data
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleChild(list) {
      list.forEach((item, index) => {
        if (item.child) {
          item.children = JSON.parse(JSON.stringify(item.child))
          this.handleChild(item.children)
        }
      })
    },
    handleData(list) {
      if (list.length) {
        list.map(item => {
          if (item.user_list) {
            item.user_list.map(itm => {
              itm.child = item.child
              this.handleData(itm.child)
            })
          }
        })
      }
    },
    handleDatas(list) {
      list.map(it => {
        const a = []
        it.child.map(i => {
          if (i.user_list) {
            a.push(...i.user_list)
          }
        })
        it.child = a
        this.handleDatas(it.child)
      })
    },
    changeIndex(user_id) {
      this.temp.pro_managers_id = user_id
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },
    open() {
      this.popshow = true
    },
    closePop() {
      this.popshow = false
    },
    // 时间戳转换日期格式方法
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    // 获取打卡记录
    getList() {
      if (this.search_data.length > 0) {
        this.listQuery.end_time = this.search_data[0] + '——' + this.search_data[1]
      } else {
        this.search_data = [this.formatDate(+new Date()), this.formatDate(+new Date())]
        this.listQuery.end_time = this.search_data[0] + '——' + this.search_data[1]
      }

      this.listLoading = true
      attendanceList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleFilter() {
      if (this.usertree === '') {
        this.listQuery.user_id = undefined
      }
      this.listQuery.page = 1
      if (this.listQuery.end_time != null) {
        this.listQuery.end_time = ''
        this.listQuery.search_time = ''
      }
      this.getList()
    },
    handleModifyStatus(row, status) {
      this.$message({
        message: '操作Success',
        type: 'success'
      })
      row.status = status
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        company_id: '',
        pro_name: '',
        pro_managers: '',
        pro_type: '',
        pro_start_time: '',
        pro_end_time: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.pro_start_time = this.temp.search_time[0]
          this.temp.pro_end_time = this.temp.search_time[1]
          projectAdd(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)

      this.temp.company_id = row.company_id
      this.temp.pro_managers = row.pro_managers
      this.temp.pro_managers_id = row.pro_managers_id
      this.temp.pro_type = row.pro_type_id

      this.temp.timestamp = new Date(this.temp.timestamp)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.pro_start_time = this.temp.search_time[0]
          this.temp.pro_end_time = this.temp.search_time[1]
          const tempData = Object.assign({}, this.temp)
          tempData.pro_managers = this.temp.pro_managers_id

          projectEdit(tempData).then(() => {
            this.list.findIndex(v => v.id === this.temp.id)
            //  this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleDelete(row, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectDel({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.list.splice(index, 1)
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        this.$notify({
          title: 'info',
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    },
    handleDownload() {
      this.listQuery.page_size = 1000
      if (this.listQuery.search_time) {
        this.listQuery.end_time = this.listQuery.search_time[0] + '——' + this.listQuery.search_time[1]
      }
      this.listLoading = true
      attendanceList(this.listQuery).then(response => {
        this.data = response.data.data
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
      setTimeout(() => {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['序号', '日期', '姓名', '上班打卡时间', '上班打卡状态', '上班打卡位置', '下班打卡时间', '下班打卡状态', '下班打卡位置', '日工时']
          const filterVal = ['work_date', 'user_id', 'work_time_go', 'status', 'work_location_go', 'work_time_leave',  'off_work_status','work_location_leave', 'work_hours']
          const data = this.formatJson(filterVal)
          data.forEach((v, k) => {
            this.numId = k + 1
            v.forEach((kv, kk) => {
              if (kk === 0) {
                v.unshift(this.numId)
              }
            })
          })
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: '考勤记录台账'
          })
          this.downloadLoading = false
        })
      }, 2000)
    },
    formatJson(filterVal) {
      return this.data.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 10px;
}
.tree {
  background: white;
  padding: 10px;
  border: 0;
}
::v-deep .el-tree-node__content {
  height: auto !important;
  position: relative;
  margin-left: 30px;
}
.tree-name {
  font-size: 13px;
  font-weight: bold;
}
.tree-title {
  padding: 5px 10px;
  font-size: 12px;
  text-align: center;
}
.custom-tree-node {
  padding: 5px 0;
}
::v-deep .el-tree-node__expand-icon {
  position: absolute;
  top: 0;
  left: 0;
}
</style>

