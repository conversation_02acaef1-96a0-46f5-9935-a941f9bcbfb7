<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <div style="display: inline-block;">
        <el-date-picker v-model="listQuery.search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                        type="daterange" clearable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        @change="handleFilter" />
      </div>

      <el-select v-model="listQuery.leave_id" style="width: 110px;margin-left:10px;" clearable filterable
                 placeholder="用户名称" @visible-change="getCompanyUsers" @change="handleFilter">
        <el-option v-for="item in options" :key="item.user_username" :label="item.user_username" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="审核状态" clearable style="width: 110px" class="filter-item"
                 @change="handleFilter">
        <el-option v-for="(item, ind) in importanceOptions2" :key="ind" :label="item.status" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.leave_type" placeholder="请假类型" clearable style="width: 110px" class="filter-item"
                 @visible-change="leaveTypeList" @change="handleFilter">
        <el-option v-for="(item, idx) in importanceOptions1" :key="idx" :label="item.leave_type" :value="item.id" />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>

      <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
                 @click="handleDownload">
        导出
      </el-button>
    </div>

    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%;"
              @sort-change="sortChange">
      <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="80"
                       :class-name="getSortClass('id')">
      </el-table-column>
      <el-table-column label="申请人" align="center">
        <template slot-scope="{row}">
          <span>{{ row.leave_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center">
        <template slot-scope="{row}">
          <span>{{ row.leave_type }}</span>
        </template>
      </el-table-column>
      <el-table-column label="摘要" align="center">
        <template slot-scope="{row}">
          <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;">{{
              row.leave_reason
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center">
        <template slot-scope="{row}">
          <span style="">{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center">
        <template slot-scope="{row}">
          <span style="">
            <span v-if="!row.leave_starting_type ">{{row.leave_starttime }}</span>
            <span v-else>{{row.leave_starttime.slice(0,10) }} {{ row.leave_starting_type }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center">
        <template slot-scope="{row}">
          <span style="">
            <span v-if="!row.leave_stoptime_type " >{{ row.leave_stoptime }}</span>
            <span v-else >{{ row.leave_stoptime.slice(0,10) }}{{ row.leave_stoptime_type }}</span>
            </span>
        </template>
      </el-table-column>
      <el-table-column label="时长" align="center">
        <template slot-scope="{row}">
          <span style="">{{ row.day_num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驳回理由" align="center">
        <template slot-scope="{row}">
          <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;">{{
              row.refuse_reason
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" width="100px" class-name="status-col">
        <template slot-scope="{row}">
          <el-tag v-if="row.state == '待审核'" type="danger">
            {{ row.state }}
          </el-tag>
          <el-tag v-if="row.state == '已通过'" type="success">
            {{ row.state }}
          </el-tag>
          <el-tag v-if="row.state == '已驳回'" type="warning">
            {{ row.state }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="$t('table.actions')" align="center" width="200px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button v-if="row.state == '待审核'" type="primary" size="mini" @click="handleAdopt(row)">
            通过
          </el-button>
          <el-button v-if="row.state == '待审核'" size="mini" type="danger" @click="handleReject(row)">
            驳回
          </el-button>

          <el-button v-if="row.state !== '待审核'" type="primary" size="mini" disabled @click="handleAdopt(row)">
            通过
          </el-button>
          <el-button v-if="row.state !== '待审核'" size="mini" disabled type="danger" @click="handleReject(row)">
            驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
                @pagination="getList" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="80px"
               style="width: 430px; margin-left:50px;">
        <el-form-item label="驳回理由" prop="refuse_reason" class="labelName">
          <el-input v-model="temp.refuse_reason" style="width: 300px" type="textarea" :rows="3" placeholder="请输入内容"
                    maxlength="100" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="rejectData">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanyUsers } from '@/api/porject/project'
import { leaveList, leaveTypeList, leaveActive } from '@/api/workAttendance'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'LeaveDay',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  props: {
    WatchObj: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      explodeData: [],
      popshow: false,
      tableKey: 0,
      list: [],
      srcList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        end_time: undefined,
        leave_id: undefined,
        status: undefined,
        leave_type: undefined,
        search_time: null
      },
      listQueryAdd: {
        id: '',
        status: ''
      },
      listData: {
        page: 1,
        page_size: 10
      },
      options: [],
      importanceOptions1: [],
      importanceOptions2: [
        {
          status: '已审核',
          id: '1'
        },
        {
          status: '待审核',
          id: '0'
        },
        {
          status: '已驳回',
          id: '2'
        }
      ],
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      statusOptions: ['published', 'draft', 'deleted'],
      showReviewer: false,
      temp: {
        id: '',
        state: 2,
        refuse_reason: ''
      },
      textMap: {
        reject: '驳回请假'

      },
      rules: {
        refuse_reason: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      pvData: [],
      downloadLoading: false,
      numId: 0
    }
  },
  watch: {
    'WatchObj': {
      deep: true,
      handler() {
        if (this.WatchObj.name != '') {
          this.getList()
        }
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },
    leaveTypeList() {
      leaveTypeList().then(response => {
        this.importanceOptions1 = response.data
      })
    },
    handleUrl(file) {
      this.srcList.push(file.leave_photo)
    },
    getList() {
      if (this.listQuery.search_time) {
        this.listQuery.end_time = this.listQuery.search_time[0] + '——' + this.listQuery.search_time[1]
      }
      this.listLoading = true
      leaveList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      if (this.listQuery.search_time == null) {
        this.listQuery.end_time = ''
      }
    },
    handleModifyStatus(row, status) {
      this.$message({
        message: '操作Success',
        type: 'success'
      })
      row.status = status
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
    handleAdopt(row) {
      this.$confirm('是否确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        leaveActive({ id: row.id, state: 1 }).then(response => { })
        this.$message({
          type: 'success',
          message: '审核通过!'
        })
        this.getList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消审核'
        })
      })
    },
    resetTemp() {
      this.temp = {
        id: '',
        state: 2,
        refuse_reason: ''
      }
    },
    handleReject(row) {
      this.resetTemp()
      this.temp.id = row.id
      this.dialogStatus = 'reject'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    rejectData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          leaveActive({ id: this.temp.id, state: 2, refuse_reason: this.temp.refuse_reason }).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: '驳回成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async (excel) => {
        const tHeader = ['序号', '申请人', '类型', '摘要', '驳回理由', '提交时间', '开始时间', '结束时间', '时长', '任务状态']
        const filterVal = ['leave_id', 'leave_type', 'leave_reason', 'refuse_reason', 'created_at', 'leave_starttime', 'leave_stoptime', 'day_num', 'state']
        if (this.listQuery.search_time) {
          this.listQuery.end_time = this.listQuery.search_time[0] + '——' + this.listQuery.search_time[1]
        }
        this.listLoading = true
        this.listQuery.page_size = 1000

        await leaveList(this.listQuery).then(response => {
          this.explodeData = response.data.data
          this.listQuery.page_size = 10
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
        const data = this.formatJson(this.explodeData, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '请假列表'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
          filterVal.map((j) => {
            if (j === 'timestamp') {
              return parseTime(v[j])
            } else {
              return v[j]
            }
          })
      )
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 10px;
}
</style>
