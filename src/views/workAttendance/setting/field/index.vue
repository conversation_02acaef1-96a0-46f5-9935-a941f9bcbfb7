<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate">
        新增
      </el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column label="序号" type="index" align="center" width="80" :class-name="getSortClass('id')" />
      <el-table-column label="开始时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.start_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.end_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户列表" align="left">
        <template slot-scope="{row}">
          <span v-for="(item,index) in row.user_id" :key="index">{{ item.user_username }}<span v-if="index < row.user_id.length - 1">, </span></span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="{row}">
          <el-switch v-model="row.statuss" @change="changflog(row)" active-color="#13ce66" inactive-color="#ff4949" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.actions')" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">{{ $t('table.edit') }}</el-button>
          <el-button type="danger" size="mini" @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size" @pagination="getList" />
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="temp" label-position="left" label-width="110px" class="dialog-form" :rules="rules">
        <el-form-item label="开始结束时间" prop="search_time">
          <el-date-picker v-model="temp.search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="daterange" clearable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" />
        </el-form-item>
        <el-form-item label="外勤人员">
          <el-transfer v-model="value4" filterable :titles="['正常考勤', '外勤人员']" :button-texts="['到左边', '到右边']" :data="datas" @change="handleChange" class="transfer-large">
            <span slot-scope="{ option }">{{ option.label }}</span>
          </el-transfer>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { attGroupList, attGroupAdd, attGroupDel, attGroupOff, attGroupEdit } from '@/api/workAttendance'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { getAllUserInfo } from '@/api/user'

export default {
  name: 'SetLeaveDay',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      title: '新增外勤人员',
      value4: [],
      datas: [],
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10
      },
      temp: {
        user_id: [],
        search_time: []
      },
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        search_time: [
          { required: true, message: '请选择开始和结束时间', trigger: 'change' }
        ]
      },
    }
  },
  created() {
    this.getList()
    this.get_person()
  },
  methods: {
    del(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        attGroupDel({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getList()
            this.$notify({ title: 'Success', message: '删除成功', type: 'success', duration: 2000 })
          }
        })
      }).catch(() => {
        this.$notify({ title: 'info', message: '已取消删除', type: 'info', duration: 2000 })
      })
    },
    changflog(e) {
      let status = e.status === 1 ? 2 : 1
      attGroupOff({ id: e.id, status }).then(() => {
        this.getList()
      })
    },
    handleChange(value) {
      this.temp.user_id = value
    },
    get_person() {
      getAllUserInfo().then(res => {
        res.data.forEach(item => {
          item.key = item.id
          item.label = item.user_username
        })
        this.datas = res.data
      })
    },
    handleSearch() {
      if (this.temp.search_time && this.temp.search_time.length === 2) {
        this.temp.start_time = this.temp.search_time[0] + ' 00:00'
        this.temp.end_time = this.temp.search_time[1] + ' 23:59'
      }
    },
    getList() {
      this.listLoading = true
      attGroupList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.list.forEach(item => {
          item.statuss = item.status === 1
        })
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleCreate() {
      this.title = '新增外勤人员'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.temp.search_time = []
      this.value4 = []
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.temp.id = ''
          this.temp.user_id = this.temp.user_id.toString()
          if (this.temp.search_time && this.temp.search_time.length === 2) {
            this.temp.start_time = this.temp.search_time[0] + ' 00:00'
            this.temp.end_time = this.temp.search_time[1] + ' 23:59'
          }
          attGroupAdd(this.temp).then(response => {
            if (response.meta.status === 200) {
              this.dialogFormVisible = false
              this.$notify({ title: 'Success', message: 'Created Successfully', type: 'success', duration: 2000 })
              this.getList()
            }
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp.search_time = [row.start_time, row.end_time]
      this.value4 = row.user_id.map(item => item.id)
      this.temp.id = row.id
      this.title = '编辑外勤人员'
      this.dialogStatus = 'edit'
      this.dialogFormVisible = true
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.temp.user_id = this.temp.user_id.toString()
          if (!this.temp.start_time && this.temp.search_time && this.temp.search_time.length === 2) {
            this.temp.start_time = this.temp.search_time[0] + ' 00:00'
            this.temp.end_time = this.temp.search_time[1] + ' 23:59'
          }
          if (!this.temp.user_id) {
            this.temp.user_id = this.value4.toString()
          }
          attGroupEdit(this.temp).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({ title: 'Success', message: 'Update Successfully', type: 'success', duration: 2000 })
          })
        }
      })
    },
    getSortClass(key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 24px;
}
.filter-container {
  margin-bottom: 16px;
}
.filter-item {
  margin-left: 0;
}
.dialog-form {
  width: 50vw;
  margin-left: 50px;
}
.dialog-footer {
  text-align: right;
  padding: 10px 0 0 0;
}
/* 保证el-transfer宽度充足 */
.transfer-large {
  min-width: 480px;
}
</style>


