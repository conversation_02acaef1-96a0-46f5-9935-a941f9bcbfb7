<template>
  <el-card class="app-container">
    <el-steps :active="activeStep" finish-status="success" align-center style="margin-bottom: 30px;">
      <el-step title="考勤组设置"/>
      <el-step title="考勤信息"/>
      <el-step title="考勤地点"/>
      <el-step title="考勤规则"/>
    </el-steps>
    <el-form :model="obj" :rules="rules" ref="formRef" label-width="200px">
      <!-- Step 0: 考勤组设置 -->
      <el-card v-show="activeStep === 0" shadow="never" style="margin-bottom: 24px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考勤组名称" prop="group_name" required>
              <el-input v-model="obj.group_name" placeholder="请输入考勤组名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组成员选择" prop="group_members" required>
              <el-select v-model="obj.group_members" multiple filterable placeholder="请选择组成员" style="width: 100%">
                <el-option v-for="user in users" :key="user.id" :label="user.user_username" :value="user.id"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- Step 1: 考勤信息 -->
      <el-card v-show="activeStep === 1" shadow="never" style="margin-bottom: 24px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认上班打卡时间" prop="work_time" required>
              <el-time-select v-model="obj.work_time" :picker-options="{ start: '07:30', step: '00:15', end: '11:30' }"
                              placeholder="开始时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认下班打卡时间" prop="off_work_time" required>
              <el-time-select v-model="obj.off_work_time"
                              :picker-options="{ start: '17:00', step: '00:15', end: '24:59' }" placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上班最早可提前(分钟)" prop="work_time_start" required>
              <el-input-number v-model="obj.work_time_start" :min="0" style="width: 120px;"/>
            </el-form-item>
            <el-form-item label="上班晚于(分钟)记为迟到" prop="work_time_limit" required>
              <el-input-number v-model="obj.work_time_limit" :min="0" style="width: 120px;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下班最晚可延后(分钟)" prop="off_work_time_end" required>
              <el-input-number v-model="obj.off_work_time_end" :min="0" style="width: 120px;"/>
            </el-form-item>
            <el-form-item label="下班最早可提前(分钟)" prop="off_work_time_limit" required>
              <el-input-number v-model="obj.off_work_time_limit" :min="0" style="width: 120px;"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="午休开始时间" prop="lunch_break_start" required>
              <el-time-select v-model="obj.lunch_break_start"
                              :picker-options="{ start: '10:30', step: '00:15', end: '14:30' }" placeholder="起始时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="午休结束时间" prop="lunch_break_end" required>
              <el-time-select v-model="obj.lunch_break_end"
                              :picker-options="{ start: '11:30', step: '00:15', end: '15:30' }" placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="弹性打卡时间(分钟)" prop="flexible_time" required>
              <el-input-number v-model="obj.flexible_time" :min="0" style="width: 120px;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="严重迟到(分钟)" prop="severely_late" required>
              <el-input-number v-model="obj.severely_late" :min="0" style="width: 120px;"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- Step 2: 考勤地点 -->
      <el-card v-show="activeStep === 2" shadow="never" style="margin-bottom: 24px;">
        <el-form-item label="打卡区域" prop="clock_location" required>
          <el-input v-model="obj.clock_location" type="textarea" :rows="2" style="width: 500px"
                    placeholder="请输入打卡区域"
          />
        </el-form-item>
        <el-form-item label="考勤范围(米)" prop="clock_scope" required>
          <el-input-number v-model="obj.clock_scope" :min="0" style="width: 120px;"/>
        </el-form-item>
      </el-card>
      <!-- Step 3: 考勤规则 -->
      <el-card v-show="activeStep === 3" shadow="never" style="margin-bottom: 24px;">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="晚走次日晚到" prop="late_switch">
              <el-switch v-model="obj.late_switch" active-color="#13ce66" inactive-color="#ff4949"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="规则设置" required>
          <div v-for="(item, index) in list" :key="index" style="margin-bottom: 16px;">
            <el-row :gutter="10" align="middle">
              <el-col :span="8">
                <span>规则{{ index + 1 }}：下班晚走</span>
                <el-input v-model="item.work_time" :min="0" :max="24" style="width: 100px; margin: 0 4px;"/>
                小时
              </el-col>
              <el-col :span="8">
                次日可晚到
                <el-input v-model="item.late_time" :min="0" :max="24" style="width: 100px; margin: 0 4px;"/>
                小时
              </el-col>
              <el-col :span="4">
                <el-button type="danger" icon="el-icon-delete" @click="del(item)" size="mini" circle/>
              </el-col>
            </el-row>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="addRole" style="margin-top: 8px;">新增规则</el-button>
        </el-form-item>
      </el-card>
      <!-- 步骤按钮 -->
      <el-form-item style="text-align: center;">
        <el-button v-if="activeStep > 0" @click="prevStep" style="margin-right: 24px;">上一步</el-button>
        <el-button v-if="activeStep < 3" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="activeStep === 3" type="primary" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { createAttGroup, editAtteGroup, getAtteGroupInfo } from '@/api/workAttendance'
import { getCompanyUsers } from '@/api/porject/project'

export default {
  name: 'SetLeaveDay',
  props: {
    obj: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      list: [],
      obj: {
        group_name: '',
        group_members: [],
        work_time: '',
        off_work_time: '',
        work_time_start: '',
        work_time_limit: '',
        off_work_time_end: '',
        off_work_time_limit: '',
        lunch_break_start: '',
        lunch_break_end: '',
        flexible_time: '',
        severely_late: '',
        clock_location: '',
        clock_scope: '',
        late_switch: ''
      },
      activeStep: 0,
      users: [],
      rules: {
        group_name: [
          { required: true, message: '请输入考勤组名称', trigger: 'blur' }
        ],
        group_members: [
          { required: true, type: 'array', min: 1, message: '请选择组成员', trigger: 'change' }
        ],
        work_time: [
          { required: true, message: '请选择默认上班打卡时间', trigger: 'change' }
        ],
        off_work_time: [
          { required: true, message: '请选择默认下班打卡时间', trigger: 'change' }
        ],
        work_time_start: [
          { required: true, type: 'number', message: '请输入上班最早可提前分钟', trigger: 'change' }
        ],
        work_time_limit: [
          { required: true, type: 'number', message: '请输入上班晚于多少分钟记为迟到', trigger: 'change' }
        ],
        off_work_time_end: [
          { required: true, type: 'number', message: '请输入下班最晚可延后分钟', trigger: 'change' }
        ],
        off_work_time_limit: [
          { required: true, type: 'number', message: '请输入下班最早可提前分钟', trigger: 'change' }
        ],
        lunch_break_start: [
          { required: true, message: '请选择午休开始时间', trigger: 'change' }
        ],
        lunch_break_end: [
          { required: true, message: '请选择午休结束时间', trigger: 'change' }
        ],
        flexible_time: [
          { required: true, type: 'number', message: '请输入弹性打卡时间', trigger: 'change' }
        ],
        severely_late: [
          { required: true, type: 'number', message: '请输入严重迟到分钟', trigger: 'change' }
        ],
        clock_location: [
          { required: true, message: '请输入打卡区域', trigger: 'blur' }
        ],
        clock_scope: [
          { required: true, type: 'number', message: '请输入考勤范围', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        if (val) {
          getAtteGroupInfo({ id: val }).then(res => {
            if (res.data) {
              this.setForm(res.data)
            }
          })
        }
      }
    }
  },
  created() {
    if (this.id) {
      getAtteGroupInfo({ id: this.id }).then(res => {
        if (res.data) {
          this.setForm(res.data)
        }
      })
    }
    this.getCompanyUsers()
  },
  methods: {
    setForm(data) {
      // 赋值表单
      this.obj = {
        group_name: data.group_name || '',
        group_members: data.group_members || [],
        work_time: data.work_time || '',
        off_work_time: data.off_work_time || '',
        work_time_start: data.work_time_start || '',
        work_time_limit: data.work_time_limit || '',
        off_work_time_end: data.off_work_time_end || '',
        off_work_time_limit: data.off_work_time_limit || '',
        lunch_break_start: data.lunch_break_start || '',
        lunch_break_end: data.lunch_break_end || '',
        flexible_time: data.flexible_time || '',
        severely_late: data.severely_late || '',
        clock_location: data.clock_location || '',
        clock_scope: data.clock_scope || '',
        late_switch: data.late_switch || ''
      }
      // 赋值规则list，并补充唯一date
      this.list = Array.isArray(data.role)
        ? data.role.map(item => ({
            ...item,
            date: item.date || new Date().getTime() + Math.random()
          }))
        : []
    },
    addRole() {
      this.list.push({
        work_time: 0,
        late_time: 0,
        date: new Date().getTime() + Math.random() // 保证唯一
      })
    },
    del(e) {
      const arr = []
      this.list.forEach((item) => {
        if (item.date !== e.date) {
          arr.push(item)
        }
      })
      this.list = arr
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.users = response.data
      })
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return
        // 校验规则设置
        if (!this.list.length) {
          this.$notify({
            title: '提示',
            message: '请至少添加一条考勤规则',
            type: 'warning',
            duration: 2000
          })
          return
        }
        let hasEmpty = false
        this.list.forEach(item => {
          if (item.work_time === '' || item.late_time === '') {
            hasEmpty = true
          }
        })
        if (hasEmpty) {
          this.$notify({
            title: '提示',
            message: '请完善所有考勤规则的内容',
            type: 'warning',
            duration: 2000
          })
          return
        }
        this.obj.role = this.list
        this.obj.id = this.id // 新增：提交时带上id
        editAtteGroup(this.obj).then((response) => {
          if (response.meta.status === 200) {
            this.$notify({
              title: '成功',
              message: '添加成功',
              type: 'success',
              duration: 2000
            })
            this.$refs.formRef.resetFields() // 重置表单
            this.list = []                   // 清空规则
            this.activeStep = 0              // 回到第一步
            this.$emit('close')
            this.$emit('refresh')
          }
        })
      })
    },
    prevStep() {
      if (this.activeStep > 0) this.activeStep--
    },
    nextStep() {
      // 当前步骤校验
      let stepProps = [
        ['group_name', 'group_members'],
        ['work_time', 'off_work_time', 'work_time_start', 'work_time_limit', 'off_work_time_end', 'off_work_time_limit', 'lunch_break_start', 'lunch_break_end', 'flexible_time', 'severely_late'],
        ['clock_location', 'clock_scope']
      ]
      const fields = stepProps[this.activeStep]
      let errorCount = 0
      let checked = 0
      fields.forEach((field) => {
        this.$refs.formRef.validateField(field, (errorMessage) => {
          checked++
          if (errorMessage) errorCount++
          if (checked === fields.length && errorCount === 0) {
            if (this.activeStep < 3) this.activeStep++
          }
        })
      })
    }
  }
}
</script>
