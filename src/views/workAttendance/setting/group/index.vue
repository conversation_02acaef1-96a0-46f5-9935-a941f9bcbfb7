<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <el-button class="filter-item" style="" type="primary" icon="el-icon-edit" @click="handleCreate">
        新增考勤组
      </el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row
              style="width: 100%;"
    >
      <el-table-column label="序号" prop="index" type="index" sortable="custom" align="center" width="80px"
                       :class-name="getSortClass('id')"
      />
      <el-table-column label="考勤组名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考勤组成员" align="left">
        <template slot-scope="{row}">
          <span v-for="(item,index) in row.groupsUser" :key="index">{{ item.user_username }},</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.actions')" align="center" width="230"
                       class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            {{ $t('table.edit') }}
          </el-button>
          <el-button type="danger" size="mini" @click="del(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
                @pagination="getList"
    />
    <el-drawer
      :title="title"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
    >
      <Add v-if="!editMode" @close="drawerVisible = false" @refresh="handleRefresh"/>
     <Edit v-else :id="temp.id" :obj="temp" :key="editKey" @close="drawerVisible = false" @refresh="handleRefresh"/>
    </el-drawer>
  </div>
</template>

<script>
import { getAttGroup, delAtteGroup, attGroupAdd, attGroupOff, attGroupEdit } from '@/api/workAttendance'
import Pagination from '@/components/Pagination'
import { getAllUserInfo } from '@/api/user'
import Add from './add.vue'
import Edit from './edit.vue'

export default {
  name: 'SetLeaveDay',
  components: { Pagination, Add, Edit },
  data() {
    return {
      title: '新增外勤人员',
      value4: [],
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10
      },
      temp: {
        user_id: []
      },
      drawerVisible: false,
      dialogStatus: '',
      editMode: false, // 新增
      editKey: Date.now(), // 新增，强制 Edit 组件重建
    }
  },
  created() {
    this.getList()
    this.get_person()
  },
  methods: {
    del(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delAtteGroup({ id: row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getList()
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        this.$notify({
          title: 'info',
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    },
    changflog(e) {
      let status = e.status === 1 ? 2 : 1
      const obj = { id: e.id, status: status }
      attGroupOff(obj).then(() => {
        this.getList()
      })
    },
    get_person() {
      getAllUserInfo().then(res => {
        res.data.forEach((item) => {
          item.key = item.id
          item.label = item.user_username
        })
        this.datas = res.data
      })
    },
    getList() {
      this.listLoading = true
      getAttGroup(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    handleCreate() {
      this.editMode = false
      this.title = '新增外勤人员'
      this.dialogStatus = 'create'
      this.drawerVisible = true
      this.search_time = []
      this.value4 = []
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = ''
          this.temp.user_id = Array.isArray(this.temp.user_id) ? this.temp.user_id.toString() : this.temp.user_id
          attGroupAdd(this.temp).then((response) => {
            if (response.meta.status === 200) {
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
              this.getList()
            }
          })
        }
      })
    },
    handleUpdate(row) {
      this.editMode = true
      this.title = '编辑考勤组'
      this.dialogStatus = 'update'
      this.drawerVisible = true
      this.temp = { ...row } // 传递当前行数据
      this.editKey = Date.now() // 每次都变，强制 Edit 组件重建
    },
    handleRefresh() {
      this.getList();
      this.drawerVisible = false;
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.user_id = Array.isArray(this.temp.user_id) ? this.temp.user_id.toString() : this.temp.user_id
          if (!this.temp.start_time) {
            this.temp.start_time = this.search_time[0] + ' ' + '00:00'
            this.temp.end_time = this.search_time[1] + ' ' + '23:59'
          }
          if (!this.temp.user_id) {
            this.temp.user_id = this.value4.toString()
          }
          attGroupEdit(this.temp).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    getSortClass: function(key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 0px;
}
</style>


