<template>
  <div class="app-container">
    <div>
      <div class="filter-container" style="position: relative">
        <div style="display: inline-block;">
          <el-date-picker v-model="search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
            type="daterange" :clearable="clearable" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            @change="handleSearch" />
        </div>
        <el-select v-model="value" multiple filterable style="width: 230px; margin-left: 10px; padding: 10px"
          placeholder="搜索用户" class="filter-item" @change="handleTime">
          <el-option v-for="item in projectManager" :key="item.user_username" :label="item.user_username"
            :value="item.id" />
        </el-select>

        <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
          @click="handleDownload">
          导出
        </el-button>
        <el-button v-waves  class="filter-item" type="primary" icon="el-icon-search"
          @click="Search">
          搜索
        </el-button>
      </div>
      <!-- 表格 -->
      <el-table v-loading="listLoading" :data="tableData.data" style="width: 100%;">
        <el-table-column  prop="date" :fixed="true" label="姓名" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.user_name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="date" :fixed="true" label="部门" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.department }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="date" :fixed="true" label="职务" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.job }}</span>
          </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in tableData.header" :key="index" :label="index" align="center">
          <el-table-column :label="item.week" align="center">
            <el-table-column label="早" width="120" align="center">
              <template slot-scope="{row}">
                <span v-if="row.date[index]">{{ row.date[index]['morning'] }}</span>
                <span v-if="!row.date[index]" />
              </template>
            </el-table-column>
            <el-table-column label="晚" width="120" align="center">
              <template slot-scope="{row}">
                <span v-if="row.date[index]">{{ row.date[index]['afternoon'] }}</span>
                <span v-if="!row.date[index]" />
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>

        <el-table-column prop="date" label="应出勤(小时)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.should_att_h }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="实出勤(小时)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.actual_att_h }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="应出勤(天数)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.should_att_d }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="实出勤(天数)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.actual_att_d }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="date" label="迟到(次)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.late }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="严重迟到(次)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.severely_late }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="早退(次)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.leave_early }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="上班缺卡(次)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.morning_not_clock }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="下班缺卡(次)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.after_not_clock }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="date" label="公休(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.not_work_day }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="法休(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.holiday }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="倒休(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.falls }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="事假(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.personal_leave }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="date" label="婚假(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.marriage_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="产假(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.maternity_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="病假(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.sick_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="丧假(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.funeral_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="加班(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.work_in_holidays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="年假(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.annual_leave }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="年假余额(天)" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.annual_leaves }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="出勤率" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.attendance_rate }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
      @pagination="getList" />
  </div>
</template>
<script>
import { attendanceStatistical } from '@/api/workAttendance'
import waves from '@/directive/waves'
import { getCompanyUsers } from '@/api/porject/project'
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      clearable: false,
      projectManager: [],
      value: '',
      total: 0,
      listQuery: {
        page: 1,
        page_size: 10,
        user_id: '',
        search_time: ''
      },
      search_time: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())],
      tableData: {
        data: [],
        date: [],
        header: []
      },
      afternoon: [],
      morning: [],
      list: [],
      objectList: [],
      listLoading: true,
      downloadLoading: false
    }
  },
  created() {
    this.getList()
    this.getCompanyUsers()
  },
  methods: {
    Search() {
      this.listLoading = true
      // this.listQuery.search_time =

      this.getList()
      this.listLoading = false
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const resize = this.listQuery.page_size // 每页条数
      return (page - 1) * resize + index + 1
    },
    // 按时间搜索
    handleSearch() {
      this.listLoading = true

      this.getList()
      this.listLoading = false
    },
    handleTime(id) {
      this.listLoading = true
      this.listQuery.user_id = id.toString()
      this.getList()
      this.listLoading = false
    },
    // 获取人员列表
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.projectManager = response.data
      })
    },
    async getList() {

      this.listLoading = true
      this.listQuery.search_time = this.search_time[0] + '——' + this.search_time[1]

      await attendanceStatistical(this.listQuery).then(response => {
        this.total = response.data.total
        this.tableData.data = response.data.data
        this.tableData.header = response.data.header
        this.tableData.date = response.data.date
      })
      this.listLoading = false
    },
    // 导出
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async excel => {
        let header_time = []
        let explode_data = []
        this.listQuery.page_size = 50
        await attendanceStatistical(this.listQuery).then((response) => {
          header_time = response.data.header
          explode_data = response.data.data
          this.listQuery.page_size = 10
        })
        const values = Object.values(header_time)
        const two_header = []
        const three_header = []
        values.forEach((item) => {
          item.befor = '早'
          item.night = '晚'
          three_header.push(item.befor, item.night)
          two_header.push(item.week, '')
        })
        two_header.unshift('', '', '')
        two_header.push('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '')
        three_header.unshift('', '', '')
        three_header.push('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '')
        const keys = Object.keys(header_time)
        const time_concat = []
        keys.map(i => {
          time_concat.push(i)
          time_concat.push('')
        })
        const tHeader = ['姓名', '部门', '职务']
        let header = ['应出勤(小时)', '实出勤(小时)', '应出勤(天数)', '实出勤(天数)', '迟到(次)', '严重迟到(次)', '早退(次)', '上班缺卡(次)', '下班缺卡(次)', '公休(天)', '法休(天)', '倒休(天)', '事假(天)', '病假(天)', '婚假(天)', '丧假(天)', '产假(天)', '加班(天)', '年假(天)', '年假余额(天)', '出勤率']
        const merges = this.alphabet(values, header)
        // 合并header
        Array.prototype.push.apply(tHeader, time_concat)
        Array.prototype.push.apply(tHeader, header)

        const filterVal = ['user_name', 'department', 'job', 'date', 'should_att_h', 'actual_att_h', 'should_att_d', 'actual_att_d', 'late', 'severely_late', 'leave_early', 'morning_not_clock', 'after_not_clock', 'not_work_day', 'holiday', 'falls', 'personal_leave', 'sick_leave', 'marriage_leave', 'funeral_leave', 'maternity_leave', 'work_in_holidays', 'annual_leave', 'annual_leaves', 'attendance_rate']
        const arr = this.formatJson(explode_data, filterVal, keys)
        const data = []
        // 处理数组
        for (const i in arr) {
          data[i] = []
          const obj = arr[i]
          for (const k in obj) {
            const val = obj[k]
            if (k != 3) {
              data[i].push(val)
            } else {
              for (const kk in val) {
                data[i].push(val[kk])
              }
            }
          }
        }
        const multiHeader = [tHeader, two_header, three_header]
        header = []
        console.log(multiHeader,'multiHeader')
        console.log(header,'header')
        console.log(data,'data')
        console.log(merges,'merges')
        excel.export_json_to_excel({
          multiHeader,
          header,
          data,
          filename: '考勤统计台账',
          merges
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal, keys) {
      return explode_data.map(v => filterVal.map(j => {
        if (j === 'date') {
          const obj = v[j]
          const date = []
          for (const i in keys) {
            const objs = obj[keys[i]]
            if (objs) {
              for (const k in objs) {
                date.push(objs[k])
              }
            } else {
              date.push('')
              date.push('')
            }
          }
          return date
        }
        return v[j]
      }))
    },
    // 时间戳转换日期格式方法
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    // 获取导出英文表头
    alphabet(values, mainMerges) {
      const merges = ['A1:A3', 'B1:B3', 'C1:C3']
      const arr = this.generateColumn()
      // 第二部分
      arr.forEach((item, index) => {
        if ((index % 2) === 0 && index < (values.length * 2)) {
          merges.push(item + '1' + ':' + arr[index + 1] + '1')
          merges.push(item + '2' + ':' + arr[index + 1] + '2')
        }
      })

      // 第三部分
      const res = []
      mainMerges.forEach((val, key) => {
        const keys = (merges.length - 3) + key
        res.push(arr[keys] + '1' + ':' + arr[keys] + '3')
      })

      Array.prototype.push.apply(merges, res)
      return merges
    },
    // 动态生成长度
    generateColumn() {
      const res = []
      const arr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
      for (let i = 0; i < arr.length; i++) {
        for (let k = 0; k < arr.length; k++) {
          res.push(arr[i] + arr[k])
        }
      }
      Array.prototype.push.apply(arr, res)
      arr.splice(0, 3)
      return arr
    }
  }
}
</script>

