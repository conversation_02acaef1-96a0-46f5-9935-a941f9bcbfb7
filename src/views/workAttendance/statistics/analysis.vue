<template>
  <div class="mixin-components-container">

    <el-row :gutter="24">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <div style="display: inline-block;">
              <el-date-picker v-model="search_time" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                              type="daterange" :clearable="clearable" range-separator="至" start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              @change="handleSearch"
              />
            </div>
            <el-select v-model="value" multiple filterable style="width: 230px; margin-left: 10px; padding: 10px"
                       placeholder="搜索用户" class="filter-item" @change="handleTime"
            >
              <el-option v-for="item in projectManager" :key="item.user_username" :label="item.user_username"
                         :value="item.id"
              />
            </el-select>
          </div>
          <PieChart :parameter="parameter"/>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import PieChart from '@/views/workAttendance/statistics/PieChart'
import { getCompanyUsers } from '@/api/porject/project'

export default {
  name: 'ComponentMixinDemo',
  components: {
    PieChart
  },
  data() {
    return {
      projectManager: [],
      value: [],
      clearable: false,
      parameter: {
        user_id: localStorage.getItem('userID'),
        search_time: this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000) + '——' + this.formatDate(+new Date())
      },
      search_time: [this.formatDate(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 3600 * 1000), this.formatDate(+new Date())]
    }
  },
  created() {
    this.getCompanyUsers()
    let userId = Number(localStorage.getItem('userID'))
    this.value.push(userId)
  },
  methods: {
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    handleSearch() {
      this.listLoading = true
      this.parameter.search_time = this.search_time[0] + '00:00:00' + '——' + this.search_time[1] + '23:59:59'
      this.listLoading = false
    },
    handleTime(id) {
      this.listLoading = true
      this.parameter.user_id = id.toString()
      this.parameter.search_time = this.search_time[0] + '00:00:00' + '——' + this.search_time[1] + '23:59:59'
      this.listLoading = false
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.projectManager = response.data
      })
    }
  }
}
</script>

<style scoped>
.mixin-components-container {
  background-color: #f0f2f5;
  padding: 30px;
  min-height: calc(100vh - 84px);
}
</style>
