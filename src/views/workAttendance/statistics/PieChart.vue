<template>
  <!-- 考勤统计 -->
  <div :class="className" :style="{height:height,width:width}"/>
</template>

<script>
import echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/components/mixins/resize'
import { getAttendanceList } from '@/api/dashboard'

export default {
  name: '<PERSON><PERSON><PERSON>',
  mixins: [resize],
  props: {
    parameter: {
      type: Object,
      default: () => {
      }
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '500px'
    }
  },
  data() {
    return {
      chart: null,
      day: [],
      searchTime: [],
      morning: [],
      after: []
    }
  },
  watch: {
    parameter: {
      deep: true,
      immediate: true,
      handler() {
        this.chart.resize()
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async initChart() {
      await getAttendanceList(this.parameter).then((res) => {
        this.day = Object.keys(res.data.day).map(item => ({ key: item, value: res.data.day[item] }))
        this.data = res.data.data
      })
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        title: {
          text: '考勤记录分析'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {},
        toolbox: {
          show: true,
          feature: {
            dataView: { readOnly: false },
            magicType: { type: ['line', 'bar'] },
            restore: {},
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          axisLine: {
            onZero: false
          },
          boundaryGap: false,
          data: this.day
        },
        yAxis: {
          type: 'value',
          inverse: true,
          splitNumber: 12,
          axisLabel: {
            formatter: '{value}:00'
          }
        },
        series: this.data
      }, true)
    }, formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }

}
</script>
