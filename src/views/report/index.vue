<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-form ref="queryForm" :model="queryParams" :inline="true">
        <!-- 日期搜索 -->
        <el-form-item>
          <el-date-picker
            v-model="search_data"
            unlink-panels
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="daterange"
            :clearable="clearable"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSearch"
          />
        </el-form-item>

        <!-- 类型搜索 -->
        <el-form-item>
          <el-select
            v-model="queryParams.report_type"
            placeholder="汇报类型"
            clearable
            class="filter-item report-type-select"
            @change="handleSearch"
          >
            <el-option
              v-for="(item, idx) in reportTypeList"
              :key="idx"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 汇报人 -->
        <el-form-item>
          <el-select
            v-model="queryParams.user_id"
            multiple
            filterable
            placeholder="汇报人"
            class="filter-item reporter-select"

            @change="handleSearch"
          >
            <el-option
              v-for="item in userList"
              :key="item.user_username"
              :label="item.user_username"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 26px;">
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loading"
        border
        :data="postList"
        class="report-table"
      >
        <el-table-column label="序号" width="50" align="center" type="index"/>
        <el-table-column label="汇报人" width="80" align="center" prop="user_id"/>
        <el-table-column label="汇报类型" width="100" align="center" prop="report_type"/>
        <el-table-column label="汇报标题" width="100" align="center" prop="title"/>
        <el-table-column label="汇报总结" width="280" align="left" prop="content">
          <template slot-scope="scope">
            <div class="pre-wrap-ellipsis">
              {{ scope.row.content }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下阶段计划" width="280" align="left" prop="plan">
          <template slot-scope="scope">
            <div class="pre-wrap-ellipsis">
              {{ scope.row.plan }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="问题/需协调资源" width="250" align="left" prop="remark">
          <template slot-scope="scope">
            <div class="pre-wrap-ellipsis">
              {{ scope.row.remark }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="接收人" width="80" align="left" prop="reviewer_list">
          <template slot-scope="scope">
            <div class="pre-wrap-break">
              {{
                scope.row.reviewer_list && scope.row.reviewer_list.user_username ? scope.row.reviewer_list.user_username : ''
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="领导点评" align="left" prop="comment">
          <template slot-scope="scope">
            <div class="pre-wrap-break">
              {{ scope.row.comment ? scope.row.comment : '未点评' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" prop="create_time" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.create_time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleInfo(scope.row)"
            >查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.page_size"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情抽屉 -->
    <el-drawer
      title="汇报详情"
      :visible.sync="detailDrawer"
      direction="rtl"
      size="60%"
      :with-header="true"
      @close="closeDrawer"
    >
      <div v-if="detailRow" class="drawer-content-padding">
        <el-tabs v-model="activeTab" stretch>
          <el-tab-pane label="工作汇报" name="report">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="汇报人">{{ detailRow.user_id }}</el-descriptions-item>
              <el-descriptions-item label="汇报类型">{{ detailRow.report_type }}</el-descriptions-item>
              <el-descriptions-item label="汇报标题">{{ detailRow.title }}</el-descriptions-item>
              <el-descriptions-item label="汇报总结">
                <div class="pre-wrap-break">{{ detailRow.content }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="下阶段计划">
                <div class="pre-wrap-break">{{ detailRow.plan }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="问题/需协调资源">
                <div class="pre-wrap-break">{{ detailRow.remark }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="附件">
                <div
                  v-if="validAttachmentImg.length > 0"
                  class="attachment-img-list"
                >
                  <div v-for="(item, index) in validAttachmentImg" :key="index">
                    <el-image
                      :src="item"
                      class="attachment-img"
                      :preview-src-list="validAttachmentImg"
                      @click="big_image(item)"
                      alt=""
                    />
                  </div>
                </div>

                <div class="attachment-file-list">
                  <div v-for="(file_item, filedex) in validAttachmentFile" :key="filedex">
                    <i class="el-icon-document-remove" />
                    <a :href="file_item" title="点击预览打印" target="_blank">汇报附件</a>
                  </div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="接收人">
                <div class="pre-wrap-break">
                  {{
                    detailRow.reviewer_list && detailRow.reviewer_list.user_username ? detailRow.reviewer_list.user_username : ''
                  }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="抄送人">
                <div class="pre-wrap-break">
                  <div
                    v-for="(user, index) in detailRow.cc_user_list"
                    :key="index"
                  >
                    {{ user.cc_user_name && user.cc_user_name.user_username ? user.cc_user_name.user_username : '' }}
                  </div>
                </div>
              </el-descriptions-item>

              <el-descriptions-item label="领导点评">
                <div class="pre-wrap-break">
                  {{ detailRow.comment ? detailRow.comment : '未点评' }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                <span>{{ detailRow.create_time }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="AI分析" name="ai">
            <MarkdownViewer :value="detailRow.analyse || ''"/>
          </el-tab-pane>


          <el-tab-pane label="关联任务" name="task">
            <el-table
              :data="detailRow.taskList || []"
              style="width: 100%"
              border
              size="small"
            >
              <el-table-column width="600" label="任务内容" prop="task_name"/>
              <el-table-column label="开始时间" prop="start_time"/>
              <el-table-column label="结束时间" prop="end_time"/>
              <el-table-column label="完成时间" prop="complete_time">
                <template #default="scope">
                  {{ scope.row.complete_time || '待完成' }}
                </template>
              </el-table-column>
              <el-table-column label="审核人">
                <template #default="scope">
                  {{ scope.row.user_name }}
                </template>
              </el-table-column>

              <el-table-column label="审核状态" prop="status"/>
            </el-table>
          </el-tab-pane>


        </el-tabs>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getReportList, getReportType } from '@/api/report/report'
import { getCompanyUsers } from '@/api/porject/project'
import MarkdownViewer from '@/components/MarkdownViewer'

export default {
  name: 'Post',
  components: { Pagination, MarkdownViewer },
  data() {
    return {
      total: 0,
      postList: [],
      srcList: [' '],
      queryParams: {
        page: 1,
        page_size: 10,
        search_data: [],
        report_type: ''
      },
      activeTab: 'report',
      search_data: [],
      userList: [],
      clearable: true,
      detailDrawer: false,
      detailRow: null,
      loading: false,
      reportTypeList: []
    }
  },
  computed: {
    renderedAnalyse() {
      if (!this.detailRow || !this.detailRow.analyse) return ''
      return marked(this.detailRow.analyse)
    },
    validAttachmentFile() {
      return (this.detailRow.attachment_file || []).filter(f => f.trim() !== '');
    },
    validAttachmentImg() {
      return (this.detailRow.attachment_img || []).filter(i => i.trim() !== '');
    }
  },
  created() {
    this.getReportTypeList()
    this.getCompanyUsers()
    this.getList()
  },
  methods: {
    getReportTypeList() {
      getReportType().then(res => {
        this.reportTypeList = res.data
      })
    },
    getList() {
      this.queryParams.search_data = (this.search_data && this.search_data.length === 2) ? this.search_data : []
      this.loading = true
      getReportList(this.queryParams).then(response => {
        this.postList = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    handleSearch() {
      this.queryParams.page = 1
      this.getList()
    },
    big_image(item) {
      this.srcList[0] = item
    },
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm && this.resetForm('queryForm')
      this.handleQuery()
    },
    handleInfo(row) {
      this.detailRow = row
      this.detailDrawer = true
    },
    closeDrawer() {
      this.detailDrawer = false
      this.detailRow = null
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.userList = response.data
      })
    }
  }
}
</script>

<style scoped>
.inline-block {
  display: inline-block;
}

.report-type-select {
  width: 110px;
  left: 20px;
}

.reporter-select {
  width: 230px;
  left: 20px;
}

.report-table {
  width: 100%;
  margin-top: 20px;
}

.pre-wrap-break {
  white-space: pre-wrap;
  word-break: break-word;
}

.drawer-content-padding {
  padding: 20px;
}

.attachment-img-list {
  display: flex;
  flex-wrap: wrap;
}

.attachment-img {
  width: 50px;
  height: 50px;
  margin: 2px;
}

.attachment-file-list {
  display: flex;
  justify-content: space-around;
}

.pre-wrap-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 最多显示三行 */
  overflow: hidden;
  text-overflow: ellipsis;

  white-space: pre-wrap; /* 保留\n换行符 */
  word-break: break-word; /* 允许长单词换行 */
}
</style>
