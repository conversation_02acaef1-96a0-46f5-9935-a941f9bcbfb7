<template>
  <div style="padding:25px;">
    <div class="header" style="padding-bottom: 20px; padding-left: 20px">
      <el-button type="primary" @click="handletw">新增图文</el-button>
      <el-button type="primary" @click="handlesph">新增视频号</el-button>
      <el-button type="primary" @click="handlewbsp">新增外部视频</el-button>
    </div>
    <el-tabs :tab-position="tabPosition" @tab-click="classify">
      <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.name">
        <div class="surface">
          <el-table :key="tableKey" v-loading="listLoading" :data="dataList" border fit highlight-current-row
            style="width: 100%;">
            <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="80" />
            <el-table-column label="名称" align="left">
              <template slot-scope="{row}">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" align="left">
              <template slot-scope="{row}">
                <span>{{ row.type_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center">
              <template slot-scope="{row}">
                <span>{{ row.create_by_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="接收人" width="500px" align="left">
              <template slot-scope="{row}">
                <span>{{ row.receive_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="接收平台" align="center">
              <template slot-scope="{row}">
                <span>{{ row.push_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center">
              <template slot-scope="{row}">
                <span>{{ row.created_at }}</span>
              </template>
            </el-table-column>

            <el-table-column label="上次推送时间" align="center">
              <template slot-scope="{row}">
                <span>{{ row.pushed_at }}</span>
              </template>
            </el-table-column>

            <el-table-column :label="$t('table.actions')" align="center" width="300px"
              class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="handleUpdate(row)">
                  编辑
                </el-button>
                <el-button type="danger" size="mini" @click="getdelKnowledgeList(row)">
                  删除
                </el-button>
                <el-button type="success" size="mini" @click="getknowledgeSend(row)">
                  推送
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
            @pagination="getList" />
        </div>
      </el-tab-pane>
      <!-- 外部视频弹框 -->
      <el-dialog :title="wbspMap[wbspStatus]" :visible.sync="show_wbsp" width="55vw" height="80vh">
        <el-form ref="dataForm" :inline="true" size="medium" label-width="150px">
        <el-form-item label="接收人：">
            <el-select v-model="wbsp_obj.receive_id" multiple filterable placeholder="接收人" class="filter-item"
              @change="handleTime">
              <el-option v-for="item in allUser" :key="item.user_username" :label="item.user_username"
                :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型：">
            <el-select v-model="wbsp_obj.category" filterable placeholder="文章类型" class="filter-item">
              <el-option v-for="item in list" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="推送平台：">
            <el-select multiple v-model="wbsp_obj.push_type" filterable placeholder="推送平台" class="filter-item">
              <el-option v-for="item in push_platform" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="名称：">
            <el-input v-model="wbsp_obj.name" rows="3" placeholder="请输入名称" />
          </el-form-item>




          <el-form-item label="跳转路径：">
            <el-select  v-model="tw_obj.path" filterable placeholder="跳转路径" class="filter-item">
              <el-option v-for="item in path_list" :key="item.name" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="标注：">
            <el-tag :key="tag" v-for="tag in dynamicTags" :closable="true" :disable-transitions="false"
                    @close="handleClose(tag)">
              {{ tag }}
            </el-tag>

            <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"
                      @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
            </el-input>
            <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
          </el-form-item>

          <el-form-item label="内容：">
            <el-input v-model="wbsp_obj.content" type="textarea" rows="3" style="    width: 600px;"
                      placeholder="请输入内容"
            />
          </el-form-item>

          <el-form-item label="外链地址：">
            <el-input v-model="wbsp_obj.src" type="textarea" rows="3" style="    width: 600px;" placeholder="请输入地址"/>
          </el-form-item>


        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="show_wbsp = false">
            取消
          </el-button>
          <el-button type="primary" @click="wbspStatus === 'create' ? wbspCreate() : wbspUpdate()">
            提交
          </el-button>
        </div>
      </el-dialog>
      <!-- 视频号弹框 -->
      <el-dialog :title="sphMap[sphStatus]" :visible.sync="show_sph" width="50vw" height="80vh">
        <el-form ref="dataForm" :inline="true" size="medium" label-width="150px">
          <el-form-item label="接收人：">
            <el-select v-model="sph_obj.receive_id" multiple filterable placeholder="接收人" class="filter-item"
              @change="handleTime">
              <el-option v-for="item in allUser" :key="item.user_username" :label="item.user_username"
                :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型：">
            <el-select v-model="sph_obj.category" filterable placeholder="文章类型" class="filter-item">
              <el-option v-for="item in list" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="推送平台：">
            <el-select multiple v-model="sph_obj.push_type" filterable placeholder="推送平台" class="filter-item">
              <el-option v-for="item in push_platform" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="视频号：">
            <el-input v-model="sph_obj.finder_username" rows="3" placeholder="请输入视频号名称" />
          </el-form-item>
          <el-form-item label="feed_id：">
            <el-input v-model="sph_obj.feed_id" placeholder="请输入视频号ID" />
          </el-form-item>
          <el-form-item label="名称：">
            <el-input v-model="sph_obj.name" rows="3" placeholder="请输入名称" />
          </el-form-item>

          <el-form-item label="跳转路径：">
            <el-select  v-model="tw_obj.path" filterable placeholder="跳转路径" class="filter-item">
              <el-option v-for="item in path_list" :key="item.name" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="标注：">
            <el-tag :key="tag" v-for="tag in dynamicTags" :closable="true" :disable-transitions="false"
              @close="handleClose(tag)">
              {{ tag }}
            </el-tag>
            <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"
              @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
            </el-input>
            <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
          </el-form-item>


        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="show_sph = false">
            取消
          </el-button>
          <el-button type="primary" @click="sphStatus === 'create' ? sphCreate() : sphUpdate()">
            提交
          </el-button>
        </div>
      </el-dialog>
      <!-- 图文弹框 -->
      <el-dialog :title="twMap[twStatus]" :visible.sync="show_tw">
        <el-form ref="dataForm" :inline="true" size="medium" label-width="150px">

          <el-form-item label="接收人：">
            <el-select v-model="tw_obj.receive_id" multiple filterable placeholder="接收人" class="filter-item"
              @change="handleTime">
              <el-option v-for="item in allUser" :key="item.user_username" :label="item.user_username"
                :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="类型：">
            <el-select v-model="tw_obj.category" filterable placeholder="文章类型" class="filter-item">
              <el-option v-for="item in list" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="推送平台：">
            <el-select multiple v-model="tw_obj.push_type" filterable placeholder="推送平台" class="filter-item">
              <el-option v-for="item in push_platform" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="名称：">
            <el-input v-model="tw_obj.name" rows="3" placeholder="请输入名称" />
          </el-form-item>

          <el-form-item label="跳转路径：">
            <el-select  v-model="tw_obj.path" filterable placeholder="跳转路径" class="filter-item">
              <el-option v-for="item in path_list" :key="item.name" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>


          <el-form-item label="标注：">
            <el-tag :key="tag" v-for="tag in dynamicTags" :closable="true" :disable-transitions="false"
              @close="handleClose(tag)">
              {{ tag }}
            </el-tag>
            <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"
              @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
            </el-input>
            <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
          </el-form-item>



          <el-form-item label="内容：">
            <wangEditor v-model="tw_obj.content" style="display:flex; justify-content:center;width: 700px;"  />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="show_tw = false">
            取消
          </el-button>
          <el-button type="primary" @click="twStatus === 'create' ? twCreate() : twUpdate()">
            提交
          </el-button>
        </div>
      </el-dialog>
      <!-- 选人组件 -->
      <el-dialog title="接收人" :visible.sync="show_person" width="55vw" height="80vh">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane label="部门" name="first">
            <div style="display: flex;">
              <div>
                <div v-for="(item, index) in departmentList" :key="index">
                  <span class="department_color" @click="departmen_once(item)">{{ item.department_name }}</span>
                </div>
              </div>
              <div>
                <div v-for="(item, index) in department_userList" :key="index">
                  <el-checkbox v-model="item.checked" @change="checkuser(item)"> {{ item.user_username }} </el-checkbox>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="角色">
            <div style="display: flex;">
              <div>
                <div v-for="(item, index) in RoleOptions" :key="index">
                  <span class="department_color" @click="role_once(item)">{{ item.role_name }}</span>
                </div>
              </div>
              <div>
                <div v-for="(item, index) in role_userList" :key="index">
                  <el-checkbox v-model="item.checked" @change="checkusers(item)"> {{ item.user_username }}
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="已选择">
            <div v-for="(item, index) in check_userList" :key="index">
              <el-checkbox v-model="item.checked" @change="initial(item)"> {{ item.user_username }} </el-checkbox>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>
    </el-tabs>
  </div>
</template>
<script>
import { getCompanyDept, getCompanyRole } from '@/api/system/admin'
import { knowledgeSend, addKnowledgeList, listKnowledgeCate, editKnowledgeList, delKnowledgeList, knowledgeList } from '@/api/know'
import { getCompanyUsers } from '@/api/system/admin'
import Pagination from '@/components/Pagination'
import { fileUpload } from '@/api/system/sys'
import WangEditor from '@/components/WangEditor'
import { companyUserList, } from '@/api/system/admin'
export default {
  components: {
    Pagination,
    WangEditor
  },
  data() {
    return {
      show_person: false,
      activeName: 'first',
      role_userList: [],
      RoleOptions: [],
      check_userList: [],
      department_userList: [],
      departments: [],
      department: [],
      departmentList: [],
      inputVisible: false,
      inputValue: '',
      dynamicTags: [],
      twStatus: '',
      sphStatus: '',
      wbspStatus: '',
      sph_obj: {},
      tw_obj: {},
      wbsp_obj: {},
      allUser: [],
      list: [],
      dataList: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        page_size: 10,
        category: 5
      },
      listQuerys: {
        page: 1,
        page_size: 100,
        company_role_id: '',

      },
      datap4: ['wmv', 'asf', 'asx', 'rm', 'rmvb', 'mpg', 'mpeg', 'mpe', 'mov', 'mp4', 'm4v', 'avi', 'dat', 'mkv', 'flv', 'vob'],
      show_sph: false,
      show_wbsp: false,
      show_tw: false,
      tabPosition: 'left',
      tableKey: '0',
      wbspMap: {
        create: '新增外部视频',
        update: '编辑外部视频'
      },
      sphMap: {
        create: '新增视频号',
        update: '编辑视频号'
      },
      twMap: {
        create: '新增图文',
        update: '编辑图文'
      },
      wbsp_type: [],
      flag: false,
      department_id: {},
      company_roleId: {},
      path_list: [
        {
          name: '登录页面',
          value: 'pages/home/<USER>'
        }, {
          name: '资讯页面',
          value: 'subpackages/information/index'
        }
      ], push_platform: [
        {
          id: 1,
          name: '微信订阅消息'
        }, {
          id: 2,
          name: '手机短信提醒'
        }, {
          id: 3,
          name: '小程序内部提醒'
        }, {
          id: 4,
          name: '后台客户端提醒'
        }
      ]
    }
  },
  created() {
    this.getAlluser()
    this.getList()
    this.getDataList()
    this.getTreeselect()
    this.getcompanyRole()
  },
  methods: {
    // 获取所有角色
    getcompanyRole() {
      getCompanyRole().then(response => {
        this.RoleOptions = response.data
      })
    },
    // 标签卡
    handleClick(tab, event) {
      if (tab.index == 0) {
        this.departmen_once(this.department_id)
      }
      if (tab.index == 1) {
        this.role_once(this.company_roleId)
      }
      if (tab.index == 2) {
        var arr = []
        this.check_userList.forEach((item) => {
          if (item.checked == true) {
            arr.push(item)
          }
        })
        this.check_userList = arr
      }
    },
    // 获取每个角色的用户role_userList
    role_once(e) {
      this.role_userList = []
      this.listLoading = true
      this.listQuerys.state = 1
      this.listQuerys.department_id = ''
      this.listQuerys.company_role_id = e.id
      this.company_roleId = e
      companyUserList(this.listQuerys).then((response) => {
        response.data.data.map(item => {
          let res = this.check_userList.some(itm => {
            return itm.id === item.id
          })
          if (res) {
            item.checked = true
          }
        })
        this.role_userList = response.data.data
        this.listLoading = false
      })
    },
    initial() {
      var arr = []
      this.check_userList.forEach((item) => {
        if (item.checked == true) {
          arr.push(item)
        }
      })
      this.check_userList = arr
    },
    checkuser(e) {

      if (e.checked == true) {
        this.check_userList.push(e)
      } else {
        var arr = []
        this.check_userList.forEach((item) => {
          if (e.id != item.id) {
            arr.push(item)
          }
        })
        this.check_userList = arr
      }
    },
    checkusers(e) {

      if (e.checked == true) {
        this.check_userList.push(e)
      } else {
        var arr = []
        this.check_userList.forEach((item) => {
          if (e.id != item.id) {
            arr.push(item)
          }
        })
        this.check_userList = arr
      }
    },
    // 获取每个部门对应的人
    departmen_once(e) {
      this.department_userList = []
      this.listLoading = true
      this.listQuerys.state = 1
      this.department_id = e
      this.listQuerys.department_id = e.id
      this.listQuerys.company_role_id = ''
      companyUserList(this.listQuerys).then((response) => {
        response.data.data.map(item => {
          let res = this.check_userList.some(itm => {
            return itm.id === item.id
          })
          if (res) {
            item.checked = true
          }
        })
        this.department_userList = response.data.data
        this.listLoading = false
      })
    },
    // 获取部门并整合
    getTreeselect() {
      getCompanyDept().then(response => {
        response.data.forEach((item) => {
          item.checked = false
          this.departments.push(item)
          item.child.forEach((itm) => {
            itm.checked = false
            this.department.push(itm)
          })
        })
        this.departmentList = this.departments.concat(this.department)
      })
    },
    // 标签的删除
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 新建标签
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = '';
    },
    // 删除视频地址
    clear() {
      this.wbsp_obj.src = ''
    },
    // 赋值为 : NULL
    nullData() {
      this.sph_obj = {
        category: '',
        category_name: "",
        content: "",
        create_by: '',
        create_by_name: "",
        created_at: "",
        feed_id: "",
        finder_username: "",
        id: "",
        label: "",
        name: "",
        push_type: "",
        type_name: "",
        push_name: "",
        receive_id: [],
        receive_name: "",
        pushed_at: "",
        src: "",
        type: ""
      }
      this.tw_obj = {
        category: '',
        category_name: "",
        content: "",
        create_by: '',
        create_by_name: "",
        created_at: "",
        feed_id: "",
        finder_username: "",
        id: "",
        label: [],
        name: "",
        push_type: "",
        type_name: "",
        push_name: "",
        receive_id: [],
        receive_name: "",
        pushed_at: "",
        src: "",
        type: "",
      }
      this.wbsp_obj = {
        category: '',
        category_name: "",
        content: "",
        create_by: '',
        create_by_name: "",
        created_at: "",
        feed_id: "",
        finder_username: "",
        id: "",
        label: "",
        name: "",
        push_type: "",
        type_name: "",
        push_name: "",
        receive_id: [],
        pushed_at: "",
        receive_name: "",
        src: "",
        type: ""
      }
    },
    // 点击编辑显示对应的弹窗
    handleUpdate(e) {
      if (typeof (e.push_type[0]) != 'number') {
        e.push_type = e.push_type.split(',')
        e.push_type = e.push_type.map(Number)
      }
      if (e.label.constructor !== Array) {
        this.dynamicTags = e.label.split(',')
      }
      if (e.type == 2) {
        this.wbspStatus = 'update'
        this.show_wbsp = true
        if (typeof (e.receive_id[0]) != 'number') {
          e.arr = []
          e.receive_id.forEach((item) => {
            e.arr.push(item.id)
          })
          e.receive_id = e.arr
        }
        this.wbsp_obj = e
      }
      if (e.type == 1) {
        this.sphStatus = 'update'
        this.show_sph = true
        if (typeof (e.receive_id[0]) != 'number') {
          e.arr = []
          e.receive_id.forEach((item) => {
            e.arr.push(item.id)
          })
          e.receive_id = e.arr
        }
        this.sph_obj = e
      }
      if (e.type == 3) {
        this.twStatus = 'update'
        this.show_tw = true
        if (typeof (e.receive_id[0]) != 'number') {
          e.arr = []
          e.receive_id.forEach((item) => {
            e.arr.push(item.id)
          })
          e.receive_id = e.arr
        }
        this.tw_obj = e
      }
    },
    // 图文新增提交
    twCreate() {
      this.tw_obj.push_type = this.tw_obj.push_type.toString()
      this.listLoading = true
      this.tw_obj.label = this.dynamicTags.toString()
      this.tw_obj.receive_id = this.tw_obj.receive_id.toString()
      addKnowledgeList(this.tw_obj).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '添加成功',
            type: 'success'
          })
          this.show_tw = false
          this.getDataList()
          Object.keys(this.tw_obj).forEach(key => (person[key] = ''));
          this.listLoading = false
        }
      })
    },
    // 图文编辑提交
    twUpdate() {
      this.tw_obj.label = this.dynamicTags.toString()
      this.tw_obj.push_type = this.tw_obj.push_type.toString()
      this.tw_obj.receive_id = this.tw_obj.receive_id.toString()
      editKnowledgeList(this.tw_obj).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '编辑成功',
            type: 'success'
          })
          this.show_tw = false
          this.getDataList()
          Object.keys(this.tw_obj).forEach(key => (person[key] = ''));
        }
      })
    },
    // 视频号新增提交
    sphCreate() {
      this.sph_obj.label = this.dynamicTags.toString()
      this.sph_obj.push_type = this.sph_obj.push_type.toString()
      this.listLoading = true
      this.sph_obj.receive_id = this.sph_obj.receive_id.toString()
      addKnowledgeList(this.sph_obj).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '添加成功',
            type: 'success'
          })
          this.show_sph = false
          this.getDataList()
          Object.keys(this.sph_obj).forEach(key => (person[key] = ''));
          this.listLoading = false
        }
      })
    },
    //视频号编辑提交
    sphUpdate() {
      this.sph_obj.label = this.dynamicTags.toString()
      this.sph_obj.push_type = this.sph_obj.push_type.toString()
      this.sph_obj.receive_id = this.sph_obj.receive_id.toString()
      editKnowledgeList(this.sph_obj).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '编辑成功',
            type: 'success'
          })
          this.show_sph = false
          this.getDataList()
          Object.keys(this.sph_obj).forEach(key => (person[key] = ''));
        }
      })
    },
    //外部视频编辑提交
    wbspUpdate() {
      this.wbsp_obj.push_type = this.wbsp_obj.push_type.toString()
      this.wbsp_obj.label = this.dynamicTags.toString()

      this.wbsp_obj.receive_id = this.wbsp_obj.receive_id.toString()
      editKnowledgeList(this.wbsp_obj).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '编辑成功',
            type: 'success'
          })
          this.show_wbsp = false
          this.getDataList()
          Object.keys(this.wbsp_obj).forEach(key => (person[key] = ''));

        }
      })
    },
    // 外部视频新增提交
    wbspCreate() {
      this.listLoading = true
      this.wbsp_obj.push_type = this.wbsp_obj.push_type.toString()
      this.wbsp_obj.label = this.dynamicTags.toString()

      this.wbsp_obj.receive_id = this.wbsp_obj.receive_id.toString()
      addKnowledgeList(this.wbsp_obj).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '添加成功',
            type: 'success'
          })
          this.show_wbsp = false
          this.getDataList()
          Object.keys(this.wbsp_obj).forEach(key => (person[key] = ''));
          this.listLoading = false
        }
      })
    },
    // 上传外部视频
    uploadSectionFile(params) {
      this.listLoading = true
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form)
        .then(res => {
          if (res.meta.status == 200) {
            this.listLoading = false
            var url = res.data.url
            var result1 = url.substring(url.length - 3, url.length);
            var result2 = url.substring(url.length - 2, url.length);
            var result3 = url.substring(url.length - 4, url.length);
            this.datap4.forEach((item) => {
              if (item == result1 || item == result2 || item == result3) {
                this.flag = false
              } else {
                this.flag = true
              }
            })
            if (this.flag == true) {
              this.wbsp_obj.src = res.data.url
              this.$message({
                message: '上传成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: '选择正确格式',
                type: 'error'
              })
            }
          }
        })
    },
    // 删除
    getdelKnowledgeList(e) {
      this.listLoading = true
      delKnowledgeList({ id: e.id }).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.getDataList()
          this.listLoading = false
        }
      })
    },
    // 分享
    getknowledgeSend(e) {
      this.listLoading = true
      knowledgeSend({ id: e.id }).then(res => {
        if (res.meta.status == 200) {
          this.$message({
            message: '推送成功',
            type: 'success'
          })
          this.listLoading = false
        }
      })
    },
    // 接收人
    handleTime(e) {
    },
    //点击不同类型
    classify(tab, event) {
      this.list.forEach((item, index) => {
        if (index == tab.index) {
          this.listQuery.category = item.id
          this.getDataList()
        }
      })
    },
    // 页数
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    // 获取所有用户
    getAlluser() {
      getCompanyUsers().then(res => {
        this.allUser = res.data
      })
    },
    // 数据列表
    getDataList() {
      this.listLoading = true
      knowledgeList(this.listQuery).then(res => {
        this.dataList = res.data.data
        this.total = res.data.total
        this.dataList.forEach((item) => {
          item.receive_name = []
          item.receive_id.forEach((itm) => {
            item.receive_name.push(itm.user_username)
          })
          item.receive_name = item.receive_name.toString()
        })
        this.listLoading = false
      })
    },
    //  获取属于那种类型
    getList() {
      listKnowledgeCate(this.listQuery).then(res => {
        this.list = res.data.data
      })
    },
    // 新增图文弹窗
    handletw() {
      this.dynamicTags = []

      this.nullData()
      this.getDataList()
      this.twStatus = 'create'
      this.show_tw = true
      this.tw_obj.type = 3
    },
    // 新增视频号弹窗显示
    handlesph() {
      this.dynamicTags = []

      this.nullData()
      this.sphStatus = 'create'
      this.show_sph = true
      this.sph_obj.type = 1
    },
    // 新增外部视频弹窗显示
    handlewbsp() {
      this.dynamicTags = []

      this.nullData()
      this.show_wbsp = true
      this.wbsp_obj.type = 2
      this.wbspStatus = 'create'
    }
  }
}
</script>


<style scoped>
.department_color:hover {
  color: rgb(64, 158, 255);
  cursor: pointer;
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.label_name {
  margin-top: 1vw;
}

.close {
  /* width: 30px;
  height: 30px;
  background: red; */
  position: absolute;
  top: 52%;
  right: 19%;
  border-radius: 50%;
}

::v-deep #tab-data-label {
  padding-left: 20px;
}
</style>
