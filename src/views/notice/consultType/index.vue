<template>
  <div class="app-container">
    <el-card class="box-card">
      <div style="display:flex; width: 20%;  margin-bottom: 10px; ">
        <el-input style="margin-right: 20px;" v-model="listQuery.name" @input="changeType"
          placeholder="请输入搜索的名称"></el-input>
        <el-button class="filter-item" type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
      </div>
      <div>
        <el-table :key="keys" v-loading="listLoading" :data="list" border fit highlight-current-row
          style="width: 100%;">
          <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="50" />
          <el-table-column label="分类名称" align="center">
            <template slot-scope="{row}">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分类描述" align="center">
            <template slot-scope="{row}">
              <span>{{ row.description }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center">
            <template slot-scope="{row}">
              <span>{{ row.created_at }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" width="200" align="center" label="操作">
            <template slot-scope="row">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(row)">编辑</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-dialog :title="titleAddEdit" :visible.sync="dialogVisible" width="70%">

      <div>
        <div class="type_ti"><span class="type_title">分类名称：</span> <el-input class="type_input" type="text"
            v-model="form.name" /></div>
        <div class="type_ti"><span class="type_title">分类描述：</span> <el-input class="type_input" type="textarea"
            v-model="form.description" /></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dSubmitForm">
          取消
        </el-button>
        <el-button type="primary" @click="submitForm">
          提交
        </el-button>
      </div>
    </el-dialog>
    <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size" @pagination="getList" />
  </div>
</template>

<script>
import { listKnowledgeCate, delKnowledgeCate, editKnowledgeCate, addKnowledgeCate } from '@/api/know'

import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'Dept',
  components: { Pagination },

  data() {
    return {
      keys: 1,
      total: 0,
      list: [],
      listQuery: {
        page: 1,
        page_size: 10
      },
      titleAddEdit: '',
      dialogVisible: false,
      form: {
      },
      listLoading: false,
    }
  },
  created() {
    this.getList()
  },
  methods: {
    dSubmitForm() {
      this.dialogVisible = false

    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    // 实时搜索咨询类型
    changeType(e) {
      this.listQuery.name = e
      this.getList()
    },
    /** 查询咨询分类列表 */
    getList() {
      this.listLoading = true
      listKnowledgeCate(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      })
    },
    /** 新增按钮操作 */
    handleAdd() {

      this.dialogVisible = true
      this.titleAddEdit = '新增咨询分类'

    },
    /** 编辑操作 */
    handleEdit(row) {
      this.dialogVisible = true
      this.titleAddEdit = '编辑咨询分类'
      this.form = row.row
    },


    /** 提交按钮 */
    submitForm: function () {
      if (this.form.name == '' || this.form.description == '') {
        this.$notify({
          message: '请规范信息',
          type: 'error'
        })
      } else {
        if (this.titleAddEdit === '编辑咨询分类') {
          editKnowledgeCate(this.form).then(response => {
            if (response.meta.status === 200) {
              this.dialogVisible = false

              this.$notify({
                message: '编辑成功',
                type: 'success'
              })

              this.getList()
              this.form.name = ''
              this.form.description = ''
            } else {
              this.msgError(response.msg)
            }
          })
        } else {
          addKnowledgeCate(this.form).then(response => {
            if (response.meta.status === 200) {
              this.dialogVisible = false
              this.form.name = ''
              this.form.description = ''
              this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        }
      }

    },
    /** 删除按钮操作 */
    handleDelete(e) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delKnowledgeCate({ id: e.row.id }).then(response => {
          if (response.meta.status === 200) {
            this.getList()
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {
        this.$notify({
          title: 'info',
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    },
  }
}
</script>

<style>
.type_input {}

.type_title {
  font-size: 24px;
  width: 200px;
}

.type_ti {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
}
</style>