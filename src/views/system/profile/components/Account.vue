<template>
  <el-form>
    <el-form-item label="用户名称">
      <el-input disabled v-model.trim="user.name" />
    </el-form-item>
    <el-form-item label="登录账号">
      <el-input disabled v-model.trim="user.phone" />
    </el-form-item>
    <el-form-item label="登录密码">
      <el-input v-model.trim="user.password" placeholder="请输入登录密码" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">修改</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { changePassword } from '@/api/system/sys'
import { removeRole, removeToken } from '@/utils/auth'

export default {
  props: {
    user: {
      type: Object,
      default: () => {
        return {
          name: '',
          phone: '',
          password: ''
        }
      }
    }
  },
  methods: {
    submit() {
      changePassword({ 'admin_phone': this.user.phone, 'admin_password': this.user.password }).then(response => {
        if (response.meta.status === 200) {
          this.$message({
            message: '修改成功',
            type: 'success',
            duration: 10 * 1000
          })
          removeToken()
          removeRole()
          this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        }
      })
    }
  }
}
</script>
