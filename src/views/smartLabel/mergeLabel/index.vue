<template>
  <div class="mains">
    <div class="all_label">
      <el-select v-model="value" clearable placeholder="请选择标签类型" @change="change_select">
        <el-option v-for="item in options" :key="item.id" :label="item.category_name" :value="item.id" />
      </el-select>
      <div class="rece">
        <div class="count">
          <div v-for="(item, index) in data" :key="index" class="center">
            <el-button type="primary" size="mini" class="label" @click="once_label(item)">{{ item.label_name }}
            </el-button>
          </div>
        </div>
        <div>
          <div v-if="type == 4" class="count_right">
            <el-table
              :data="work_reception"
              style="width: 60vw; margin-bottom: 20px;  "
              row-key="id"
              border
              fit
              highlight-current-row
              default-expand-all
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column label="任务名称" align="left">
                <template slot-scope="{row}">
                  <span>{{ row.task_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属项目" width="200px" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.project_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="发布人" width="80px" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.user_id }}</span>
                </template>
              </el-table-column>
              <el-table-column label="执行人" width="80px" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.receive_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="任务开始时间" width="135px" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.start_time }}</span>
                </template>
              </el-table-column>
              <el-table-column label="任务结束时间" width="135px" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.end_time }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="type == 1" class="count_right">
            <el-table :data="project_reception" border fit highlight-current-row style="width: 65vw">
              <el-table-column label="公司名称" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.company_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="项目名称" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.pro_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="项目管理者" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.pro_managers }}</span>
                </template>
              </el-table-column>
              <el-table-column label="项目开始时间" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.pro_start_time }}</span>
                </template>
              </el-table-column>
              <el-table-column label="项目结束时间" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.pro_end_time }}</span>
                </template>
              </el-table-column>
              <el-table-column label="项目状态" class-name="status-col">
                <template slot-scope="{row}">
                  <el-tag :type="row.status">
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="type == 3" class="count_right" />
          <div v-if="type == 2" class="count_right" />

        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 引入标签类型、每个标签对应的数据、按类型进行搜索对应标签的api接口
import { getCategoryList, categoryLabelList, getLabelData } from '@/api/smartLabel'

export default {
  data() {
    return {
      options: [],
      value: '',
      data: [],
      type: 0,
      work_reception: [],
      project_reception: [],
      check: [],
      finance: []
    }
  },
  created() {
    this.label_type()
    this.change_selects()
  },
  methods: {
    // 获取每一个标签对应数据
    once_label(item) {
      var obj = {
        category: item.id,
        label_name: item.label_name
      }
      // console.log(obj, '提交的')
      getLabelData(obj).then(response => {
        if (item.category.id === 1) {
          this.type = 1
          this.project_reception = response.data
        } else if (item.category.id === 4) {
          this.type = 4
          this.work_reception = response.data
        } else if (item.category.id === 2) {
          this.type = 2
          this.check = response.data
        } else if (item.category.id === 3) {
          this.type = 3
          this.finance = response.data
        }
      })
    },

    // 获取标签类型
    label_type() {
      getCategoryList().then(response => {
        this.options = response.data
      })
    },

    // 搜索框值改变进行搜索
    change_select(e) {
      if (!e) {
        e = ''
      }
      var obj = {
        category: e
      }
      categoryLabelList(obj).then(response => {
        this.data = response.data
      })
    },
    // 初始显示所有标签
    change_selects() {
      var obj = {
        category: ''
      }
      categoryLabelList(obj).then(response => {
        this.data = response.data
      })
    }
  }
}
</script>

<style  scoped>
.rece {
  display: flex;
}

.label {
  margin: 2px;
  display: inline !important;
}

.mains {
  padding: 3vw;
}

.count_right {
  flex: 1;
  padding: 1.5vw 3vw;
}

.count {
  width: 25vw;
  display: flex;
  flex-wrap: wrap;
  margin-top: 1.7vw;
}
</style>
