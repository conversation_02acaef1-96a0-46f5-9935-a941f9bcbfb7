<template>
  <el-tabs v-model="activeName">
    <el-tab-pane label="数据标签" name="data-label">
      <div class="header" style="padding-bottom: 20px; padding-left: 20px">
        <el-date-picker
          v-model="listQuery.searchTime"
          unlink-panels
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="daterange"
          clearable
          filterable
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleFilter"
        />
        <el-input v-model="listQuery.label_name" clearable placeholder="按名称搜索" style="width: 230px; padding-left: 20px" @change="handleFilter" />
        <el-button style="margin-left: 20px" @click="handleFilter">搜索</el-button>
        <el-button @click="handleCreate()">新增</el-button>
      </div>

      <el-tabs :tab-position="tabPosition" @tab-click="handleClickLabel">
        <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.category_name">
          <div class="surface">
            <el-table
              :key="tableKey"
              v-loading="listLoading"
              :data="dataList"
              border
              fit
              highlight-current-row
              style="width: 100%;"
            >
              <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="80" />
              <el-table-column label="标签类型" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.category }}</span>
                </template>
              </el-table-column>
              <el-table-column label="标签名称" align="left">
                <template slot-scope="{row}">
                  <span>{{ row.label_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="标签规则" align="left">
                <template slot-scope="{row}">
                  <span>{{ row.label_role }}</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.created_at }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('table.actions')"
                align="center"
                width="200px"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{row}">
                  <el-button type="text" size="mini" @click="handleUpdate(row)">
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="listQuery.page"
              :limit.sync="listQuery.page_size"
              @pagination="getList"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-tab-pane>
    <!-- 新增编辑弹框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="80px"
        style="width: 420px; margin-left:50px;"
      >
        <el-form-item label="标签名称" prop="label_name">
          <el-input v-model="temp.label_name" style="width: 205px"  placeholder="请输入标签名称"  />
        </el-form-item>
        <el-form-item label="标签规则" prop="label_role">
          <el-input v-model="temp.label_role" type="textarea" :rows="2" style="width: 205px" placeholder="请输入内容" />
        </el-form-item>
        <!-- <el-form-item label="标签等级" prop="label_level">
          <el-input v-model="temp.label_level" style="width: 205px" />
        </el-form-item> -->
        <el-form-item label="标签颜色" prop="label_level">
          <el-input v-model="temp.label_color" style="width: 205px"  placeholder="请选择标签颜色" />
        </el-form-item>
        <el-form-item label="标签分类" prop="category">
          <el-select v-model="temp.category" filterable placeholder="请选择">
            <el-option
              v-for="item in options1"
              :key="item.category_name"
              :label="item.category_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
    <el-tab-pane label="人员标签" name="user-label">
      <indexLabel />
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import { getCategoryList, labelList, upLabel, addLabel } from '@/api/smartLabel'
import Pagination from '@/components/Pagination'
import indexLabel from './indexLabel.vue'
import { isNumeric } from 'echarts/lib/util/number'
// import { parse } from 'path-to-regexp'
export default {
  components: {
    indexLabel,
    Pagination
  },
  data() {
    return {
      activeName: 'user-label',
      tabPosition: 'left',
      list: [],
      total: 0,
      value4: [],
      listLoading: false,
      listQuery: {
        page: 1,
        page_size: 10,
        category: 1,
        is_belong_peo: 1,
        searchTime: ''
      },
      dataList: [],
      dialogVisible: false,
      textMap: {
        create: '新增标签',
        update: '编辑标签'
      },
      rules: {
        label_name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
        label_role: [{ required: true, message: '请输入标签规则', trigger: 'blur' }],
        // label_level: [{ required: true, message: '请输入标签等级', trigger: 'blur' }],
        label_color: [{ required: true, message: '请输入标签颜色', trigger: 'blur' }],
        category: [{ required: true, message: '请选择标签分类', trigger: 'change' }]
      },
      temp: {
        label_name: '',
        label_role: '',
        // label_level: '',
        label_color: '',
        category_name: '',
        category_id: '',
        category: ''
      },
      dialogStatus: '',
      options1: [],
      options: [],
      show_pop: false,
      datas: [],
      value: [],
      right_Arr: [],
      type_id: [],
      show_from: true,
      input_name: '',
      textarea: '',
      search_data: [],
      tableKey: 0
    }
  },
  created() {
    this.getList()
    this.label_type()
    this.getCategoryList()
  },
  methods: {
    resetTemp() {
      this.temp = {
        label_name: '',
        label_role: '',
        label_level: '',
        label_color: '',
        category_name: '',
        category_id: '',
        category: ''
      }
    },
    handleClickLabel(tab, event) {
      this.listQuery.category = parseInt(tab.index) + 1
      this.getList()
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    getList() {
      if (this.listQuery.searchTime) {
        this.listQuery.created_at = this.listQuery.searchTime[0] + '——' + this.listQuery.searchTime[1]
      }
      getCategoryList().then(res => {
        this.list = res.data
      })
      this.listLoading = true
      labelList(this.listQuery).then(response => {
        this.dataList = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 500)
      })
      this.listQuery.created_at = ''
    },
    // 时间搜索
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取标签类型
    label_type() {
      getCategoryList().then(response => {
        this.options = response.data
      })
    },
    getCategoryList() {
      getCategoryList({ category: this.temp.category }).then(response => {
        this.options1 = response.data
      })
    },
    // 新增标签
    handleCreate() {
      this.getCategoryList()
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, handleMerge() {
      this.show_pop = true
    },
    createData() {
      this.listLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addLabel(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
    },
    // 编辑
    handleUpdate(row) {
      this.dialogVisible = true
      this.getCategoryList()
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.temp.category = row.category
      this.temp.category_id = row.category.id
      this.dialogStatus = 'update'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!isNumeric(this.temp.category)) {
            this.temp.category = this.temp.category_id
          }
          upLabel(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
    }
  }

}
</script>

<style scoped>
::v-deep #tab-data-label{
  padding-left:20px ;
}
</style>

