<template>
  <div>
    <div class="header" style="padding-bottom: 20px; padding-left: 20px">
      <el-date-picker v-model="listQuery.searchTime" unlink-panels value-format="yyyy-MM-dd" format="yyyy-MM-dd"
        type="daterange" clearable filterable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
        @change="handleFilter" />
      <el-input v-model="listQuery.label_name" clearable placeholder="按名称搜索" style="width: 230px; padding-left: 20px"
        @change="handleFilter" />
      <el-button style="margin-left: 20px" @click="handleFilter">搜索</el-button>
      <el-button @click="handleCreate">新增</el-button>
      <el-button @click="handleMerge()">合成</el-button>
    </div>

    <el-tabs :tab-position="tabPosition" @tab-click="handleClickLabel">
      <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.category_name">
        <div class="surface">
          <el-table :key="tableKey" v-loading="listLoading" :data="dataList" border fit highlight-current-row
            style="width: 100%;">
            <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="80" />
            <el-table-column label="标签类型" align="center">
              <template slot-scope="{row}">
                <span>{{ row.category }}</span>
              </template>
            </el-table-column>
            <el-table-column label="标签名称" align="left">
              <template slot-scope="{row}">
                <span>{{ row.label_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="标签规则" align="left">
              <template slot-scope="{row}">
                <span>{{ row.label_role }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center">
              <template slot-scope="{row}">
                <span>{{ row.created_at }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('table.actions')" align="center" width="200px"
              class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button type="text" size="mini" @click="handleUpdate(row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
            @pagination="getList" />
        </div>
      </el-tab-pane>
      <!-- 合成弹框 -->
      <el-dialog :visible.sync="show_pop" width="55vw" height="80vh">
        <div class="main">
          <div style="text-align: center">
            <el-transfer v-model="value4" style="text-align:  left; display: inline-block;  " filterable
              :left-default-checked="[]" :right-default-checked="[]" :titles="['已有标签', '选中标签']"
              :button-texts="['到左边', '到右边']" :format="{
                noChecked: '${total}',
                hasChecked: '${checked}/${total}'
              }" :data="datas" @change="handleChange">
              <span slot-scope="{ option }"> {{ option.label }}</span>
              <!-- <el-button slot="right-footer" @click="compund" size="small">合成</el-button> -->
            </el-transfer>
          </div>
          <div v-show="show_from" class="right">
            <el-table :data="right_Arr" border fit highlight-current-row style="width: 50vw;  margin-top: 2vw; ">
              <el-table-column label="标签名称" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.label_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="标签内容" align="center">
                <template slot-scope="{row}">
                  <span>{{ row.label_role }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="label_name">标签名称</div>
            <el-input v-model="input_name" class="label_name" placeholder="请输入标签名称" />
            <div class="label_name">标签规则</div>
            <el-input v-model="textarea" class="label_name" type="textarea" placeholder="请输入内容" show-word-limit />
            <div class="label_name">标签类型</div>
            <el-select v-model="value" class="label_name" clearable placeholder="请选择标签类型" @change="change_select">
              <el-option v-for="item in options" :key="item.id" :label="item.category_name" :value="item.id" />
            </el-select>
            <div class="label_name">标签等级</div>
            <el-select v-model="trigger_level" class="label_name" clearable placeholder="请选择标签等级"
              @change="change_select_type">
              <el-option v-for="item in options3" :key="item.id" :label="item.trigger_level" :value="item.id" />
            </el-select>
            <div class="label_name">标签编号</div>
            <el-input v-model="label_num" oninput="value=value.replace(/[^\d]/g,'')" style="width: 205px"
              placeholder="请输入内容" />
            <div class="label_name">标签词义</div>
            <el-select v-model="word_meaning" class="label_name" clearable placeholder="请选择标词义"
              @change="change_select_label">
              <el-option v-for="item in options2" :key="item.id" :label="item.word_meaning" :value="item.id" />
            </el-select>
            <div class="label_name">播报内容</div>
            <el-input v-model="label_report" class="label_name" type="textarea" placeholder="请输入内容" show-word-limit />

            <el-button @click="submint">合成</el-button>
          </div>
        </div>
      </el-dialog>
      <!-- 新增编辑弹框 -->
      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible">
        <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="80px"
          style=" margin-left:50px;">
          <el-form-item label="标签名称" prop="label_name">
            <el-input v-model="temp.label_name" style="width: 205px" />
          </el-form-item>
          <el-form-item label="标签规则" prop="label_role">
            <el-input v-model="temp.label_role" type="textarea" :rows="2" style="width: 205px" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="标签词义" prop="word_meaning">
            <el-select v-model="temp.word_meaning" class="label_name" clearable placeholder="标签词义"
              @change="change_select1">
              <el-option v-for="item in options2" :key="item.id" :label="item.word_meaning" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="触发等级" prop="trigger_level ">
            <el-select v-model="temp.trigger_level" class="label_name" clearable placeholder="触发等级"
              @change="change_select2">
              <el-option v-for="item in options3" :key="item.id" :label="item.trigger_level" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="触发编号" prop="identity_num">
            <el-input v-model="temp.identity_num" oninput="value=value.replace(/[^\d]/g,'')" :rows="2"
              style="width: 205px" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item style="" label="语言包" prop="msg">
            <el-input type="textarea" v-model="temp.msg" disabled :rows="2" style="width: 205px;"
              placeholder="请选择语言包" />
            <el-button style="margin-left: 20px;" @click="show_msg = true">去选择语言包</el-button>

          </el-form-item>
          <el-form-item label="动作包">
            <el-input type="textarea" v-model="temp.action" disabled :rows="2" style="width: 205px; "
              placeholder="请选择动作" />
            <el-button style="margin-left: 20px;" @click="show_move = true">去选择动作包</el-button>
          </el-form-item>
          <el-form-item label="标签分类" prop="category">
            <el-select v-model="temp.category" filterable placeholder="请选择">
              <el-option v-for="item in options1" :key="item.category_name" :label="item.category_name"
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            提交
          </el-button>
        </div>
      </el-dialog>
      <!-- 动作包列表弹窗 -->
      <el-dialog title="动作包" :visible.sync="show_move">
        <div class="app-container">
          <el-table :key="tableKey" v-loading="listLoading" :data="move_list" border fit highlight-current-row
            style="width: 100%;">
            <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="50" />
            <el-table-column label="动作名称" align="center">
              <template slot-scope="{row}">
                <span>{{ row.action }}</span>
              </template>
            </el-table-column>
            <el-table-column label="动作名称" align="center">
              <template slot-scope="{row}">
                <span>{{ row.action_num }}</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center">
              <template slot-scope="{row}">
                <el-button type="success" size="mini" @click="submit_move(row)">
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination :total="move_total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
            @pagination="getmoveList" />
        </div>
      </el-dialog>
      <!-- 语言包列表弹窗 -->
      <el-dialog title="语言包" :visible.sync="show_msg">
        <div class="app-container">
          <el-table :key="tableKey" v-loading="listLoading" :data="msg_list" border fit highlight-current-row
            style="width: 100%;">
            <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="50" />
            <el-table-column label="语言包" align="center">
              <template slot-scope="{row}">
                <span>{{ row.msg }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="{row}">
                <el-button type="success" size="mini" @click="submit_msg(row)">
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination :total="msg_total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
            @pagination="getmsgList" />
        </div>
      </el-dialog>
    </el-tabs>
  </div>
</template>
<script>
import { labelMsgList, labelActionList } from '@/api/movement'
import { getCategoryList, labelList, upLabel, addLabel, categoryLabelList } from '@/api/smartLabel'
import Pagination from '@/components/Pagination'
import { isNumeric } from 'echarts/lib/util/number'
import { Row } from 'element-ui'

export default {
  components: {
    Pagination,
  },
  data() {
    return {
      msg: {},
      move: {},
      show_move: false,
      show_msg: false,
      label_report: '',
      word_meaning: '',
      label_num: '',
      trigger_level: '',
      activeName: 'user-label',
      tabPosition: 'left',
      list: [],
      msg_list: [],
      move_list: [],
      total: 0,
      msg_total: 0,
      move_total: 0,
      listLoading: false,
      dialogVisible: false,
      listQuery: {
        page: 1,
        page_size: 10,
        category: 1,
        is_belong_peo: 2,
        searchTime: ''
      },
      dataList: [],
      textMap: {
        create: '新增标签',
        update: '编辑标签'
      },
      rules: {
        label_name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
        label_role: [{ required: true, message: '请输入标签规则', trigger: 'blur' }],
        word_meaning: [{ required: true, message: '请选择标签词义', trigger: 'blur' }],
        category: [{ required: true, message: '请选择标签分类', trigger: 'change' }],
        identity_num: [{ required: true, message: '请输入触发编号', trigger: 'blur' }],
        cattrigger_levelegory: [{ required: true, message: '请选择触发等级', trigger: 'change' }],
        msg: [{ required: true, message: '请输入播放内容', trigger: 'blur' }]
      },
      temp: {
        label_name: '',
        label_role: '',
        word_meaning: '',
        category_name: '',
        category_id: '',
        category: '',
        trigger_level: '',
        trigger_level_id: '',
        identity_num: '',
        msg: ''
      },
      dialogStatus: '',
      options1: '',
      show_pop: false,
      value4: [],
      options: [],
      options3: [
        {
          id: '1',
          trigger_level: '一级'
        },
        {
          id: '2',
          trigger_level: '二级'
        },
        {
          id: '3',
          trigger_level: '三级'
        }
      ],
      options2: [
        {
          id: '1',
          word_meaning: '贬义词'
        },
        {
          id: '2',
          word_meaning: '中性词'
        },
        {
          id: '3',
          word_meaning: '褒义词'
        }
      ],
      datas: [],
      value: [],
      right_Arr: [],
      type_id: [],
      show_from: true,
      input_name: '',
      textarea: '',
      search_data: [],
      tableKey: 0,
      msg_id: ''
    }
  },
  created() {
    this.getList()
    this.label_type()
    this.getCategoryList()
    this.change_selects()
    this.getmsgList()
    this.getmoveList()
  },
  methods: {
    submit_move(e) {
      console.log(e, '动作包')
      this.show_move = false
      this.move = e
      this.temp.action = e.action
    },
    submit_msg(e) {
      console.log(e, '语言包')
      this.show_msg = false
      this.msg = e
      this.temp.msg = e.msg
    },
    // 获取语言包列表
    getmsgList() {
      labelMsgList(this.listQuery).then(response => {
        this.msg_list = response.data.data
        this.msg_total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    // 获取动作包列表
    getmoveList() {
      labelActionList(this.listQuery).then(response => {
        this.move_list = response.data.data
        this.move_total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    resetTemp() {
      this.temp = {
        label_name: '',
        label_role: '',
        word_meaning: '',
        category_name: '',
        category_id: '',
        category: '',
        trigger_level: '',
        trigger_level_id: '',
        identity_num: '',
        msg: ''
      }
    },
    handleClickLabel(tab, event) {
      this.listQuery.category = parseInt(tab.index) + 1
      this.getList()
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    getList() {
      if (this.listQuery.searchTime) {
        this.listQuery.created_at = this.listQuery.searchTime[0] + '——' + this.listQuery.searchTime[1]
      }
      getCategoryList().then(res => {
        this.list = res.data
      })
      this.listLoading = true
      labelList(this.listQuery).then(response => {
        this.dataList = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 500)
      })
      this.listQuery.created_at = ''
    },
    // 获取标签类型
    label_type() {
      getCategoryList().then(response => {
        this.options = response.data
      })
    },
    // 到左边的
    handleChange(value) {
      var right_arr = []
      this.datas.forEach((item) => {
        var res = value.some(itm => {
          return item.key === itm
        })
        if (res) {
          this.type_id.push(item.id)
          right_arr.push(item)
        }
      })
      this.right_Arr = right_arr
    },
    // 组合成新的标签
    submint() {
      var obj = {
        label_name: this.input_name,
        label_role: this.textarea,
        label_level: 1,
        category: this.e,
        merge: this.type_id.toString(),
        trigger_level: this.trigger_level,
        identity_num: this.label_num,
        word_meaning: this.word_meaning,
        msg: this.label_report,
        is_belong_peo: 2
      }
      if (obj.label_name === '' || obj.label_role === '' || obj.merge === '' || !obj.category) {
        this.$message({
          message: '填写完整数据',
          type: 'error'
        })
      } else {
        addLabel(obj).then(response => {
          this.$router.go(0)
        })
      }
    },
    change_select_label(e) {
      this.word_meaning = e
    },
    // 点击合成
    compund() {
      if (this.right_Arr.length >= 2) {
        this.show_from = true
      }
    },
    change_select_type(e) {
      this.trigger_level = e
    },
    // 选择标签类型
    change_select(e) {
      this.e = e
    },
    change_select1(e) {
      this.temp.word_meaning = e
    },
    change_select2(e) {
      this.temp.trigger_level = e
    },
    // 请求接口拿到所有标签
    change_selects() {
      var obj = {
        category: ''
      }
      categoryLabelList(obj).then(response => {
        response.data.forEach((item, index) => {
          item.key = `${index}`
          item.label = item.label_name
          this.datas.push(item)
        })
      })
    },
    getCategoryList() {
      getCategoryList({ category: this.temp.category }).then(response => {
        this.options1 = response.data
      })
    },
    // 新增标签
    handleCreate() {
      this.getCategoryList()
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, handleMerge() {
      this.show_pop = true
    },
    createData() {
      this.listLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.is_belong_peo = 2
          this.temp.msg = this.msg.id
          this.temp.action = this.move.id
          addLabel(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
    },
    // 时间搜索
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    // 编辑
    handleUpdate(row) {
      this.getCategoryList()
      this.dialogVisible = true
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.options2.forEach((item) => {
        if (item.id == row.word_meaning) {
          this.temp.word_meaning = item.word_meaning
          this.temp.word_meaning_id = row.word_meaning
        }
      })
      this.msg_id = row.msg_id
      this.temp.category = row.category
      this.temp.category_id = row.category_id
      this.temp.trigger_level = row.trigger_level_name
      this.trigger_name = row.trigger_level_name
      this.trigger = row.trigger_level_id
      this.temp.identity_num = row.identity_num
      this.dialogStatus = 'update'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!isNumeric(this.temp.category)) {
            this.temp.category = this.temp.category_id
          }
          if (this.trigger_name == this.temp.trigger_level) {
            this.temp.trigger_level = this.trigger
          }
          this.options2.forEach((item) => {
            if (item.word_meaning == this.temp.word_meaning) {
              this.temp.word_meaning = item.id
            }
          })
          if (this.msg.id) {
            this.temp.msg = this.msg.id
          } else {
            this.temp.msg = this.msg_id
          }
          if (this.move.id) {
            this.temp.action = this.move.id
          } else {
            this.temp.action = this.move.id
          }

          upLabel(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
    }
  }

}
</script>

<style scoped>

::v-deep #tab-data-label {
  padding-left: 20px;
}
</style>
