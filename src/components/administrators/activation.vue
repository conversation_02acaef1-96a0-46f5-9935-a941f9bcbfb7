<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <el-input v-model="listQuery.user_name" placeholder="姓名" clearable style="width: 150px;" class="filter-item"
        @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
        @click="handleDownload">
        导出
      </el-button>
      <!-- <el-button class="filter-item" style="" type="primary" icon="el-icon-edit" @click="handleCreate">
        新增
      </el-button> -->
    </div>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="80px"
        style="width: 420px; margin-left:50px;">
        <el-form-item label="名称" prop="admin_name">
          <el-input v-model="temp.admin_name" style="width: 205px" placeholder="请输入管理员名称" />
        </el-form-item>
        <el-form-item label="账号" prop="admin_phone">
          <el-input v-model="temp.admin_phone" style="width: 205px" placeholder="请输入管理员账号" />
        </el-form-item>
        <el-form-item label="密码" prop="admin_password">
          <el-input v-model="temp.admin_password" placeholder="请输入密码" style="width: 205px" show-password />
        </el-form-item>
        <el-form-item label="角色" prop="role_id">
          <el-select v-model="temp.role_name" filterable placeholder="请选择角色" @visible-change="getCompanyRole"
            @change="changeRoles">
            <el-option v-for="item in RoleOptions" :key="item.id" :label="item.role_name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="绑定用户" prop="bind_user">
          <el-select v-model="temp.bind_user_id" filterable placeholder="请选择" @change="changeIndex">
            <el-option v-for="item in options" :key="item.id" :label="item.user_username" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="辅助角色" prop="auxiliary_role">
          <el-select v-model="temp.auxiliary_role" multiple placeholder="请选择" @change="changeIndex1"
            @visible-change="getCompanyRole">
            <el-option v-for="item in RoleOptions" :key="item.id" :label="item.role_name" :value="item.id" />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row
      style="width: 100%;">
      <el-table-column label="序号" type="index" :index="indexAdd" sortable="custom" align="center" width="80">
        <!-- <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template> -->
      </el-table-column>
      <el-table-column label="管理员名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.admin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.company_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色名称" align="center">
        <template slot-scope="{row}">
          <span>{{ row.role_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center">
        <template slot-scope="{row}">
          <span>{{ row.admin_phone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定小程序账号" align="center">
        <template slot-scope="{row}">
          <span>{{ row.bind_user.user_username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="辅助角色" align="center">
        <template slot-scope="{row}">
          <span>{{ row.auxiliary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="{row}">
          <span>{{ row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.actions')" align="center" width="200px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            {{ $t('table.edit') }}
          </el-button>
          <el-button v-if="row.status === '正常'" size="mini" type="danger" @click="handleDisable(row, $index)">
            禁用
          </el-button>
          <el-button v-if="row.status === '禁用'" size="mini" type="success" @click="handleTrigger(row, $index)">
            激活
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  companyAdminList,
  adminAdd,
  getCompanyUsers,
  getCompanyRole,
  adminEdit,
  adminDisable
} from '@/api/system/admin'

import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

const calendarTypeOptions = [
  { key: 'CN', display_name: 'China' },
  { key: 'US', display_name: 'USA' },
  { key: 'JP', display_name: 'Japan' },
  { key: 'EU', display_name: 'Eurozone' }
]
const calendarTypeKeyValue = calendarTypeOptions.reduce((acc, cur) => {
  acc[cur.key] = cur.display_name
  return acc
}, {})

export default {
  name: 'ComplexTable',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    },
    typeFilter(type) {
      return calendarTypeKeyValue[type]
    }
  },
  props: {
    lists: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },

  data() {
    return {
      tableKey: 0,
      list: [],
      isShow: false,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        status: 2
      },
      listQueryAdd: {
        company_id: ''
      },
      listData: {
        page: 1,
        page_size: 20
      },
      options: [],
      RoleOptions: [],
      importanceOptions: [],
      statusOptions: ['published', 'draft', 'deleted'],
      showReviewer: false,
      temp: {
        company_id: '',
        admin_name: '',
        bind_user: '',
        bind_user_id: '',
        admin_phone: '',
        role_name: '',
        role_id: '',
        admin_password: '',
        auxiliary_role: '',
        auxiliary: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑管理员',
        create: '新增管理员'
      },
      dialogPvVisible: false,
      pvData: [],
      arrs: [],
      rules: {
        admin_name: [{ required: true, message: '请输入管理员名称', trigger: 'change' }],
        admin_phone: [{ required: true, message: '请输入管理员账号', trigger: 'change' }],
        admin_password: [{ required: true, message: '请输入管理员密码', trigger: 'change' }],
        bind_user: [{ required: true, message: '请选择绑定用户', trigger: 'change' }],
        role_id: [{ required: true, message: '请选择角色', trigger: 'change' }]
      },
      downloadLoading: false,
      numId: 0,
      explodeData : []
    }
  },
  watch: {
    lists: {
      deep: true,
      immediate: true,
      handler() {
        this.list = this.lists
      }
    }
  },
  created() {
    this.getList()
    this.getCompanyRole()
    this.getCompany()
  },
  methods: {
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    changeIndex(user_id) {
      this.temp.bind_user = user_id
    },
    changeIndex1(auxiliary_role) {
      this.temp.auxiliary_role = auxiliary_role
      console.log(this.temp.auxiliary_role)
    },
    changeRoles(role_id) {
      this.temp.role_id = role_id
    },
    open() {
      this.popshow = true
    },
    closePop() {
      this.popshow = false
    },
    getList() {
      this.listLoading = true
      companyAdminList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleModifyStatus(row, status) {
      this.$message({
        message: '修改成功',
        type: 'success'
      })
      row.status = status
    },
    resetTemp() {
      this.temp = {
        admin_name: '',
        admin_phone: '',
        admin_password: '',
        bind_user: '',
        bind_user_id: '',
        role_name: '',
        role_id: '',
        auxiliary_role: '',
        auxiliary: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.temp.auxiliary_role = this.temp.auxiliary_role.toString()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          adminAdd(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      console.log(row)
      this.resetTemp()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.temp = Object.assign({}, row)
      this.temp.bind_user_id = row.bind_user_id
      this.temp.auxiliary_role = row.auxiliary_role.split(',')
      this.temp.auxiliary_role.map(v => {
        return this.arrs.push(Number(v))
      })
      this.temp.auxiliary_role = this.arrs
      console.log(this.arrs)
      console.log(this.temp.auxiliary_role)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (typeof (this.temp.bind_user) !== 'number') {
            this.temp.bind_user = this.temp.bind_user_id
          }
          this.temp.auxiliary_role = this.temp.auxiliary_role.join(',')
          adminEdit(this.temp).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
      this.getList()
    },
    handleDisable(row) {
      adminDisable({ id: row.id, status: 2 }).then(response => {
        row.status = '禁用'
      })
      this.getList()
    },
    handleTrigger(row) {
      adminDisable({ id: row.id, status: 1 }).then(response => {
        row.status = '正常'
      })
      this.getList()
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['序号', '管理员名称', '公司名称', '账号', '绑定小程序账号', '辅助角色', '创建时间', '状态']
        const filterVal = ['admin_name', 'company_id', 'admin_phone', 'bind_user', 'auxiliary', 'created_at', 'status']
        this.listQuery.page_size = 1000
        await companyAdminList(this.listQuery).then(response => {
          this.explodeData = response.data.data
          this.listQuery.page_size = 10
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
        const data = this.formatJson(filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '管理员台账(禁用)'
        })
        this.downloadLoading = false
      })
    },
    formatJson(filterVal) {
      return this.explodeData.map(v => filterVal.map(j => {
        if (j === 'bind_user') {
          return v['bind_user']['user_username']
        }
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    getCompany() {
      getCompanyUsers().then(response => {
        this.options = response.data
      })
    },
    getCompanyRole() {
      getCompanyRole().then(response => {
        this.RoleOptions = response.data
      })
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 10px;
}
</style>
