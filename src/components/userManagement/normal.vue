<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <el-date-picker v-model="queryParams.date" @change="handleFilter" type="daterange" start-placeholder="开始日期"
                      end-placeholder="结束日期" placeholder="选择入职日期"
      >
      </el-date-picker>
      <el-input v-model="queryParams.user_username" placeholder="姓名" clearable style="width: 150px;"
                class="filter-item"
                @input="handleFilter"
      />
      <el-input v-model="queryParams.user_tel" placeholder="手机号" clearable style="width: 150px; margin-left: 10px;"
                class="filter-item"
                @input="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
                 @click="handleDownload"
      >
        导出
      </el-button>
      <el-button class="filter-item" style="" type="primary" icon="el-icon-edit" @click="handleCreate">
        新增
      </el-button>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-download" @click="downloadTemplate">
        下载模板
      </el-button>
      <el-button @click="openImportDialog" v-waves class="filter-item" type="primary">
        <svg-icon icon-class="guide"/>
        导入
      </el-button>
    </div>
    <div v-if="importDialogVisible" style="  position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 99;">
      <div class="pop-bg" style="position: absolute;top: 0;right: 0;bottom: 0;left: 0;background: rgba(0, 0, 0, 0.7);"/>
      <div class="pop-text"
           style="position: absolute;width: 600px;height: 500px;top: 50%;left: 50%;padding: 20px;transform: translate(-50%, -50%);background: #fff;z-index: 9;box-sizing: border-box;"
      >
        <h2 class="user-permission"
            style="margin-top:1px;color:#333;margin-bottom: 30px;color: #666;font-size: 16px;font-weight: bold;text-align:center;"
        >
          用户导入</h2>
        <p class="close"
           style="position: absolute;top: 0;right: 25px;color: #a6a6a6;font-size: 25px;cursor: pointer;margin-top: 13px;"
           @click="closeImportDialog"
        >×</p>

        <el-upload style="text-align:center;" class="upload-demo" drag action="fakeaction"
                   :http-request="uploadSectionFile" multiple
        >
          <i class="el-icon-upload"/>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">只能上传excel表格，且不超过1000kb</div>
        </el-upload>
      </div>
    </div>
    <!-- 添加公司用户 -->
    <el-dialog
      :visible.sync="userDialogVisible"
      :title="dialogTitleMap[userDialogStatus]"
      :modal-append-to-body="false"
      class="responsive-dialog"
    >
      <div class="dialog-form-wrap">
        <el-form ref="dataForm" :inline="false" :rules="rules" :model="formData" :disabled="disabled"
                 label-position="left" label-width="80px"
        >
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12">
              <el-form-item label="姓名" prop="user_username">
                <el-input v-model="formData.user_username" placeholder="请输入你的姓名"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="年龄" prop="user_age">
                <el-input v-model="formData.user_age" placeholder="请输入你的年龄"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12">
              <el-form-item label="手机号" prop="user_tel">
                <el-input v-model="formData.user_tel" placeholder="请输入手机号码"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="邮箱" prop="user_email">
                <el-input v-model="formData.user_email" placeholder="请输入你的邮箱"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12">
              <el-form-item label="部门" prop="department_id">
                <treeselect v-model="formData.department_id" :options="deptOptions" :normalizer="normalizer"
                            :show-count="true" placeholder="请选择部门"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="角色">
                <el-select v-model="formData.company_role_id" filterable placeholder="请选择角色"
                           @visible-change="fetchRoleOptions" @change="changeIndex"
                >
                  <el-option v-for="item in roleOptions" :key="item.id" :label="item.role_name" :value="item.id"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12">
              <el-form-item label="民族" prop="user_nation">
                <el-input v-model="formData.user_nation" placeholder="请输入你的民族"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="岗位">
                <el-select v-model="formData.user_position" filterable placeholder="请选择岗位"
                           @visible-change="fetchPositionOptions" @change="changeIndex2"
                >
                  <el-option v-for="item in positionOptions" :key="item.id" :label="item.position" :value="item.id"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12">
              <el-form-item label="户籍地址">
                <el-input v-model="formData.census_register" placeholder="请输入你的户籍地址"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="居住地址">
                <el-input v-model="formData.residential_address" placeholder="输入你的居住地址"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12">
              <el-form-item label="入职时间" prop="inductiontimes">
                <el-date-picker v-model="formData.inductiontimes" type="date" placeholder="选择入职时间"
                                style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="转正时间" prop="positive_time">
                <el-date-picker v-model="formData.positive_time" type="date" placeholder="选择转正时间"
                                style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="disabled" type="primary" @click="handleCancel">返回</el-button>
        <el-button v-if="!disabled" @click="userDialogVisible = false">取消</el-button>
        <el-button v-if="!disabled" type="primary" @click="userDialogStatus === 'create' ? createData() : updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
    <el-table :key="tableKey" v-loading="listLoading" :data="userList" border fit highlight-current-row
              style="width: 100%;"
    >
      <el-table-column label="序号" prop="id" type="index" :index="indexAdd" sortable="custom" align="center"
                       width="80"
      >
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_tel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_age }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center">
        <template slot-scope="{row}">
          <span>{{ row.department_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center">
        <template slot-scope="{row}">
          <span>{{ row.company_role_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="岗位" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_position }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入职时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.inductiontimes }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转正时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.positive_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.actions')" align="center" width="300px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="danger" @click="handleReset(row, $index)">
            重置密码
          </el-button>
          <el-button v-if="row.state === '正常'" size="mini" type="warning" @click="handleDisable(row, $index)">
            禁用
          </el-button>
          <el-button v-if="row.state === '禁用'" size="mini" type="success" @click="handleTrigger(row, $index)">
            激活
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.page_size"
                @pagination="getList"
    />
  </div>
</template>

<script>
import {
  companyPositionList,
  companyUserList,
  userDisabled,
  companyUserUp,
  getCompanyDept,
  getCompanyRole,
  resetUserPassword,
  companyUserAdd,
  importUser
} from '@/api/system/admin'

import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'ComplexTable',
  components: { Pagination, Treeselect },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      importDialogVisible: false,
      userDialogVisible: false,
      userDialogStatus: '',
      dialogTitleMap: {
        update: '编辑用户',
        create: '新增用户',
        detail: '查看详情'
      },
      userList: [],
      deptOptions: [],
      roleOptions: [],
      positionOptions: [],
      distanceOptions: [
        { id: '1', label: '居住距离近' },
        { id: '2', label: '居住距离适中' },
        { id: '3', label: '居住距离较远' }
      ],
      total: 0,
      listLoading: true,
      queryParams: {
        page: 1,
        page_size: 10,
        state: 1,
        date: [],
        user_username: '',
        user_tel: ''
      },
      formData: {
        user_id: '',
        company_role_id_s: '',
        user_position_s: '',
        department_id_s: '',
        avatar_url: '',
        user_email: '',
        company_id: '',
        company_role_id: '',
        department_id: '',
        level: '',
        ada_role: '',
        census_register: '',
        inductiontimes: '',
        positive_time: '',
        center_distance: ''
      },
      disabled: false,
      tableKey: 0,
      rules: {
        user_username: [{ required: true, message: '请输入您的名字', trigger: 'change' }],
        user_tel: [{ required: true, message: '请输入您的手机号', trigger: 'blur' }],
        user_age: [{ required: true, message: '请输入您的年龄', trigger: 'change' }],
        user_nation: [{ required: true, message: '请输入你的民族', trigger: 'change' }],
        department_id: [{ required: true, message: '请选择您的部门', trigger: 'change' }],
        positive_time: [{ required: true, message: '转正时间是必填', trigger: 'change' }],
        inductiontimes: [{ required: true, message: '入职时间是必填', trigger: 'change' }],
        user_email: [{ required: true, message: '邮箱是必填选项，用于日报推送', trigger: 'change' }]
      },
      downloadLoading: false,
      numId: 0,
      dialogWidth: '1200px'
    }
  },

  watch: {
    userTel(val) {
      this.queryParams.user_tel = val
      this.handleFilter()
    },
    lists: {
      deep: true,
      immediate: true,
      handler() {
        this.userList = this.lists
      }
    }
  },
  created() {
    this.getList()
    this.fetchDeptOptions()
    this.fetchPositionOptions()
    this.fetchRoleOptions()
  },
  mounted() {
    this.setDialogWidth()
    window.addEventListener('resize', this.setDialogWidth)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setDialogWidth)
  },
  methods: {
    setDialogWidth() {
      this.dialogWidth = window.innerWidth < 900 ? '95vw' : '800px'
    },
    openImportDialog() {
      this.importDialogVisible = true
    },
    closeImportDialog() {
      this.importDialogVisible = false
    },
    indexAdd(index) {
      const page = this.queryParams.page
      const pageSize = this.queryParams.page_size
      return index + 1 + (page - 1) * pageSize
    },
    normalizer(node) {
      if (node.child && !node.child.length) {
        delete node.child
      }
      return {
        id: node.id,
        label: node.department_name,
        children: node.child
      }
    },
    changeIndex(position_id) {
      this.formData.company_role_id = position_id
    },
    changeIndex2(position_id) {
      this.formData.user_position = position_id
    },
    handleFilter() {
      this.queryParams.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      companyUserList(this.queryParams).then((response) => {
        this.userList = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    fetchDeptOptions() {
      getCompanyDept().then(response => {
        this.deptOptions = []
        const dept = { id: 0, department_name: '主类目', child: [] }
        dept.child = response.data
        this.deptOptions.push(dept)
      })
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    handleCancel() {
      this.userDialogVisible = false
    },
    handleCreate() {
      this.disabled = false
      this.resetFormData()
      this.fetchDeptOptions()
      this.userDialogStatus = 'create'
      this.userDialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    resetFormData() {
      this.formData = {
        user_id: '',
        company_role_id_s: '',
        user_position_s: '',
        department_id_s: '',
        avatar_url: '',
        user_email: '',
        company_id: '',
        company_role_id: '',
        department_id: '',
        level: '',
        ada_role: '',
        census_register: '',
        inductiontimes: '',
        positive_time: '',
        center_distance: ''
      }
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          companyUserAdd(this.formData).then(() => {
            this.userDialogVisible = false
            this.$notify({
              title: '成功',
              message: '添加成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.disabled = false
      this.resetFormData()
      this.formData = Object.assign({}, row)
      this.formData.department_id = row.department_id_s
      this.fetchDeptOptions()
      this.userDialogStatus = 'update'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.userDialogVisible = true
    },
    updateData() {
      this.userDialogVisible = false
      companyUserUp(this.formData).then(() => {
        const index = this.userList.findIndex(v => v.id === this.formData.id)
        this.userList.splice(index, 1, this.formData)
        this.$notify({
          title: '成功',
          message: '编辑成功',
          type: 'success',
          duration: 2000
        })
        this.getList()
      })
    },
    handleDisable(row) {
      userDisabled({ id: row.id, status: 2 }).then(() => {
        row.state = '禁用'
        this.getList()
      })
    },
    handleTrigger(row) {
      userDisabled({ id: row.id, status: 1 }).then(() => {
        row.state = '正常'
        this.getList()
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['序号', '姓名', '手机号', '年龄', '民族', '部门', '邮箱', '户籍地址', '角色', '岗位', '入职时间', '转正时间', '合同时长', '居住距离', '状态']
        const filterVal = ['user_username', 'user_tel', 'user_age', 'user_nation', 'department_id', 'user_email', 'census_register', 'company_role_id', 'user_position', 'inductiontimes', 'positive_time', 'contract_time', 'residential_address', 'state']
        let explode_data = []
        this.queryParams.page_size = 100
        await companyUserList(this.queryParams).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data.data
            this.queryParams.page_size = 10
          }
        })
        const data = this.formatJson(explode_data, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '用户管理台账'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => v[j])
      )
    },
    fetchPositionOptions() {
      companyPositionList({ page: 1, page_size: 100 }).then((res) => {
        this.positionOptions = res.data.data
      })
    },
    fetchRoleOptions() {
      getCompanyRole().then(res => {
        this.roleOptions = res.data
      })
    },
    handleReset(row) {
      this.$confirm('是否重置此用户密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetUserPassword({ id: row.id }).then(() => {
          this.$message({
            type: 'success',
            message: '重置成功'
          })
        })
      })
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      form.append('excel_file', file)
      importUser(form)
        .then(res => {
          if (res.meta.status === 200) {
            this.importDialogVisible = false
            this.getList()
          }
        })
    },
    downloadTemplate() {
      this.flag('user')
    }
  }
}
</script>

