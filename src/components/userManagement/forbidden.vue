<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <el-date-picker v-model="listQuery.date" @change="handleFilter" type="date" placeholder="选择离职日期"></el-date-picker>
      <el-input v-model="listQuery.user_username" placeholder="姓名" clearable style="width: 150px;" class="filter-item"
                @input="handleFilter" />
      <el-input v-model="listQuery.user_tel" placeholder="手机号" clearable style="width: 150px; margin-left: 10px;" class="filter-item"
                @input="handleFilter" />

        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
        <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
          @click="handleDownload">
          导出
        </el-button>
    </div>
    <!-- 添加公司用户 -->
    <el-dialog v-show="dialogFormVisible" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :inline="true" :rules="rules" :model="temp" :disabled="disabled" label-position="left"
        label-width="90px" style="width: 900px; margin-left:50px;">
        <div style="display: block;">
          <el-form-item label="姓名" prop="user_username">
            <el-input v-model="temp.user_username" placeholder="请输入你的姓名" style="width: 205px" />
          </el-form-item>
          <el-form-item label="年龄" prop="user_age">
            <el-input v-model="temp.user_age" style="width: 205px" placeholder="请输入你的年龄" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="temp.user_email" style="width: 205px" placeholder="请输入你的邮箱" />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="temp.company_role_id" filterable placeholder="请选择角色" @visible-change="getCompanyRole"
              @change="changeIndex">
              <el-option v-for="item in role" :key="item.id" :label="item.role_name" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="用户等级">
            <el-select v-model="temp.level" filterable placeholder="用户等级" >
              <el-option
                  v-for="item in userLevel"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
          </el-form-item>


          <el-form-item label="户籍地址">
            <el-input v-model="temp.census_register" style="width: 205px" placeholder="请输入你的户籍地址" />
          </el-form-item>
          <el-form-item label="入职时间" prop="inductiontimes">
            <div style="display: inline-block;">
              <el-date-picker v-model="temp.inductiontimes" style="width: 205px" type="date" placeholder="选择入职时间" />
            </div>
          </el-form-item>
          <el-form-item label-width="4.1vw" prop="contract_time" label="合同时长">
            <el-input v-model="temp.contract_time" placeholder="请输入合同时长(年)" style="width: 205px" />
          </el-form-item>
        </div>

        <div style="display: block;">
          <el-form-item label="手机号" prop="user_tel">
            <el-input v-model="temp.user_tel" style="width: 205px" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="民族" prop="user_nation">
            <el-input v-model="temp.user_nation" style="width: 205px" placeholder="请输入你的民族" />
          </el-form-item>
          <el-form-item label="部门" prop="department_id">
            <treeselect v-model="temp.department_id" :options="deptOptions" :normalizer="normalizer" :show-count="true"
              placeholder="请选择部门" />
          </el-form-item>
          <el-form-item label="岗位">
            <el-select v-model="temp.user_position" filterable placeholder="请选择岗位" @visible-change="companyPositionList"
              @change="changeIndex2">
              <el-option v-for="item in options" :key="item.id" :label="item.position" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="居住地址">
            <el-input v-model="temp.residential_address" style="width: 205px" placeholder="输入你的居住地址" />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="temp.user_card" style="width: 205px" placeholder="请输入你的身份证号" />
          </el-form-item>
          <el-form-item label="转正时间" prop="positive_time">
            <div style="display: inline-block;">
              <el-date-picker v-model="temp.positive_time" type="date" placeholder="选择转正时间" />
            </div>
          </el-form-item>
          <el-form-item label="居住距离">
            <el-select v-model="temp.center_distance" filterable placeholder="请选择距离位置" @visible-change="distanceList"
              @change="changeIndex3">
              <el-option v-for="item in distance" :key="item.id" :label="item.center_distance" :value="item.id" />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="disabled" type="primary" @click="handleCancel">
          返回
        </el-button>
        <el-button v-if="!disabled" @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button v-if="!disabled" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row
      style="width: 100%;">
      <el-table-column label="序号" prop="id" type="index" :index="indexAdd" sortable="custom" align="center" width="80">
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="{row}">
          <span>{{  row.user_username  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center">
        <template slot-scope="{row}">
          <span>{{  row.user_tel  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center">
        <template slot-scope="{row}">
          <span>{{  row.user_age  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center">
        <template slot-scope="{row}">
          <span>{{  row.department_id  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center">
        <template slot-scope="{row}">
          <span>{{  row.company_role_id  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="岗位" align="center">
        <template slot-scope="{row}">
          <span>{{  row.user_position  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证号" align="center">
        <template slot-scope="{row}">
          <span>{{  row.user_card  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入职时间" align="center">
        <template slot-scope="{row}">
          <span>{{  row.inductiontimes  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="转正时间" align="center">
        <template slot-scope="{row}">
          <span>{{  row.positive_time  }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.actions')" align="center" width="300px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="danger" @click="handleReset(row, $index)">
            重置密码
          </el-button>
          <el-button v-if="row.state === '正常'" size="mini" type="warning" @click="handleDisable(row, $index)">
            禁用
          </el-button>
          <el-button v-if="row.state === '禁用'" size="mini" type="success" @click="handleTrigger(row, $index)">
            激活
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  companyPositionList,
  companyUserList,
  userDisabled,
  companyUserUp,
  getCompanyDept,
  getCompanyRole,
  resetUserPassword,
  companyUserAdd,
  importUser
} from '@/api/system/admin'

import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { isString } from '@/utils/validate'

export default {
  name: 'ComplexTable',
  components: { Pagination, Treeselect },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  props: {
    lists: {
      type: Array,
      default: []
    },
    userTel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      popshow: false,
      data_value: new Date(),
      dialogPasswordVisible: false,
      tableKey: 0,
      list: [],
      deptOptions: [],
      userDisabled: false,
      disabled: false,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        state: 2,
        date: [],
        user_username: '',
        user_tel: ''
      },
      listQueryAdd: {
        company_id: ''
      },
      form: {
        department_name: undefined,
        pid: undefined
      },
      listData: {
        pages: 1,
        page_size: 20
      },
      role: [],
      department: [],
      options: [],
      importanceOptions: [],
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      statusOptions: ['published', 'draft', 'deleted'],
      showReviewer: false,
      temp: {
        user_id: '',
        company_role_id_s: '',
        user_position_s: '',
        department_id_s: '',
        avatar_url: '',
        user_email: '',
        company_id: '',
        company_role_id: '',
        department_id: '',
        level: '',
        ada_role: '',
        census_register: '',
        contract_time: '', // 合同时长
        inductiontimes: '', // 入职时间
        positive_time: '', // 转正时间
        center_distance: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑用户',
        create: '新增用户',
        detail: '查看详情'
      },
      dialogPvVisible: false,
      pvData: [],
      distance: [],
      rules: {
        user_username: [{ required: true, message: '请输入您的名字', trigger: 'change' }],
        user_tel: [{ required: true, message: '请输入您的手机号', trigger: 'blur' }],
        user_age: [{ required: true, message: '请输入您的年龄', trigger: 'change' }],
        user_nation: [{ required: true, message: '请输入您的民族', trigger: 'change' }],
        department_id: [{ required: true, message: '请选择您的部门', trigger: 'change' }],
        contract_time: [{ required: true, message: '合同时长是必填', trigger: 'change' }],
        positive_time: [{ required: true, message: '转正时间是必填', trigger: 'change' }],
        inductiontimes: [{ required: true, message: '入职时间是必填', trigger: 'change' }]
      },
      downloadLoading: false,
      numId: 0,
      userLevel : [
        {
          value: 1,
          label: '总经理',
        },
        {
          value: 2,
          label: '部门主管'
        },
        {
          value: 3,
          label: '普通员工'
        }
      ]
    }
  },

  watch: {
    userTel(val) {
      this.listQuery.user_tel = val;
      this.handleFilter();
    },
    lists: {
      deep: true,
      immediate: true,
      handler() {
        this.list = this.lists
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.child && !node.child.length) {
        delete node.child
      }
      return {
        id: node.id,
        label: node.department_name,
        children: node.child
      }
    },
    changeIndex(position_id) {
      this.temp.company_role_id = position_id
    },
    changeIndex2(position_id) {
      this.temp.user_position = position_id
    },
    distanceList() {
      this.distance = [
        {
          id: '1',
          center_distance: '居住距离近'
        },
        {
          id: '2',
          center_distance: '居住距离适中'
        },
        {
          id: '3',
          center_distance: '居住距离较远'
        }
      ]
    },
    changeIndex3(position_id) {
      this.temp.center_distance = position_id
    },
    open() {
      this.popshow = true
    },
    closePop() {
      this.popshow = false
    },
    getList() {
      this.listLoading = true
      companyUserList(this.listQuery).then((response) => {
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    /** 查询部门下拉树结构 */
    getTreeselect(e) {
      getCompanyDept().then(response => {
        this.deptOptions = []
        const dept = { id: 0, department_name: '主类目', child: [] }
        dept.child = response.data
        this.deptOptions.push(dept)
      })
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    },
    handleFilter() {
      this.listQuery.search_time = this.formatDate(this.listQuery.date[0]) + '——' + this.formatDate(this.listQuery.date[1])
      if (this.listQuery.search_time == '——') {
        this.listQuery.search_time = ''
      }
      this.listQuery.page = 1
      this.getList()
    },
    handleModifyStatus(row, status) {
      this.$message({
        message: '操作Success',
        type: 'success'
      })
      row.status = status
    },
    resetTemp() {
      this.temp = {}
    },
    // 取消
    handleCancel() {
      this.dialogFormVisible = false
    },
    handleCreate() {
      this.disabled = false
      this.resetTemp()
      this.getTreeselect('add')
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.contract_time = Number(this.temp.contract_time)
          companyUserAdd(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '添加成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.disabled = false
      this.resetTemp()

      this.temp = Object.assign({}, row)
      this.temp.department_id = row.department_id_s
      this.getTreeselect('update')
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      if (row.center_distance == 2) {
        this.temp.center_distance = '居住距离适中'
      }
      if (row.center_distance == 1) {
        this.temp.center_distance = '居住距离近'
      }
      if (row.center_distance == 3) {
        this.temp.center_distance = '居住距离较远'
      }
      if (row.center_distance == '') {
        this.temp.center_distance = ''
      }
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        this.temp.company_role_id = isString(this.temp.company_role_id) ? this.temp.company_role_id_s : this.temp.company_role_id
        this.temp.user_position = isString(this.temp.user_position) ? this.temp.user_position_s : this.temp.user_position
        this.temp.department_id = isString(this.temp.department_id) ? this.temp.department_id_s : this.temp.department_id
        this.temp.contract_time = Number(this.temp.contract_time)
        if (valid) {
          companyUserUp(this.temp).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '编辑成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
      this.getList()
    },
    handleDisable(row) {
      userDisabled({ id: row.id, status: 2 }).then(response => {
        row.state = '禁用'
      })
      this.getList()
    },
    handleTrigger(row) {
      userDisabled({ id: row.id, status: 1 }).then(response => {
        row.state = '正常'
      })
      this.getList()
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async (excel) => {
        const tHeader = ['序号', '姓名', '手机号', '年龄', '部门', '角色', '岗位', '入职时间', '身份证号', '转正时间', '状态']
        const filterVal = ['user_username', 'user_tel', 'user_age', 'department_id', 'company_role_id', 'user_position', 'inductiontimes', 'user_card', 'positive_time', 'state']

        let explode_data = []
        this.listQuery.page_size = 100
        await companyUserList(this.listQuery).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data.data
            this.listQuery.page_size = 10
          }
        })

        const data = this.formatJson(explode_data, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '用户管理台账(禁用)'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    },
    companyPositionList() {
      companyPositionList({ page: 1, page_size: 100 }).then((res) => {
        this.options = res.data.data
      })
    },
    getCompanyRole() {
      getCompanyRole().then(res => {
        this.role = res.data
      })
    },
    handleReset(row) {
      this.$confirm('是否重置此用户密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetUserPassword({ id: row.id }).then(response => {
          this.$message({
            type: 'success',
            message: '重置成功'
          })
        })
      }).catch(() => {
        resetUserPassword({ id: row.id }).then(response => {
          this.$message({
            type: 'info',
            message: '已取消重置'
          })
        })
      })
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('excel_file', file)
      importUser(form)
        .then(res => {
          if (res.meta.status === 200) {
            this.popshow = false
            this.getList()
          } else {
            // xxx
          }
        })
        .catch(() => {
          // xxx
        })
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 10px;
}

.vue-treeselect {
  width: 200px;
}
</style>
