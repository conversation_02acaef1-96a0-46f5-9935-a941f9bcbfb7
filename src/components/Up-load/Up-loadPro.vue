<template>
   <div v-if="popshow" style="  position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 99;" >
        <div class="pop-bg" style="position: absolute;top: 0;right: 0;bottom: 0;left: 0;background: rgba(0, 0, 0, 0.7);"></div>
        <div class="pop-text" style="position: absolute;width: 600px;height: 500px;top: 50%;left: 50%;padding: 20px;transform: translate(-50%, -50%);background: #fff;z-index: 9;box-sizing: border-box;">
          <h2 class="user-permission" style="margin-top:1px;color:#333;margin-bottom: 30px;color: #666;font-size: 16px;font-weight: bold;text-align:center;">文件导入</h2>
          <p @click="closePop" class="close" style="position: absolute;top: 0;right: 25px;color: #a6a6a6;font-size: 25px;cursor: pointer;margin-top: 13px;">×</p>

          <el-upload
            style="text-align:center;"
            class="upload-demo"
            drag
            action="fakeaction"
            :http-request="uploadSectionFile"
            multiple>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传excel表格，且不超过1000kb</div>
          </el-upload>
        </div>
      </div>
</template>

<script>
export default {
  name: 'UploadPro',
  data() {
    return {
      popshow: false,

    }
  },
  mounted() {
   
  },
  activated() {
    
  },
  destroyed() {
   
  },
  methods: {
    closePop(){
        this.popshow = false;
    },
    uploadSectionFile(params) {
        let file = params.file
        let form = new FormData();
      // 文件对象
      form.append("excel_file", file);
      imageUpload(form)
        .then(res => {
          // console.log(res.meta,"------------------")
        //自行处理各种情况
          const code = res && parseInt(res.code, 10);
          if (res.meta.status === 200) {
            this.popshow = false
            this.getList()
          } else {
            // xxx
          }
        })
        .catch(() => {
          // xxx
        });
     } 
  }
}
</script>
