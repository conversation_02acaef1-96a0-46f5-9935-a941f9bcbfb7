<template>
    <div class="app-container">
        <h3 class="filter-container">
            新建规则
        </h3>
        <el-divider></el-divider>
        <div class="main">
            <div class="time">
                工作时长：
            </div>
            <div class="up_down">
                <div>
                    <div class="left">
                        <div class="times">上班时间</div>
                        <div>
                            <el-time-select v-model="obj.work_time" :picker-options="{
                              start: '07:30',
                              step: '00:15',
                              end: '11:30'
                            }" placeholder="开始时间">
                            </el-time-select>
                        </div>
                    </div>
                    <div class="left">
                        <div class="times">下班时间</div>
                        <el-time-select v-model="obj.off_work_time" :picker-options="{
                          start: '17:00',
                          step: '00:15',
                          end: '24:59'
                        }" placeholder="结束时间">
                        </el-time-select>
                    </div>
                </div>
                <div style="margin-left:3vw;">
                    <div class="right">
                        <div style=" width: 300px;  margin-bottom:5vh;">最晚到超过 <input class="late"
                                v-model="obj.work_time_limit" />
                            分钟记为迟到</div>
                        <div style="width: 300px; ">最早可提前<input class="late" type="number"
                                v-model="obj.work_time_start" /> 分钟进行打卡</div>
                    </div>
                    <div class="right">
                        <div style="width: 300px; margin-bottom:5vh;">最早超过 <input class="late"
                                v-model="obj.off_work_time_limit" /> 分钟进行早退
                        </div>
                        <div style="width: 300px; ">最晚可延后 <input class="late" type="number"
                                v-model="obj.off_work_time_end" />分钟进行打卡</div>
                    </div>
                </div>
            </div>
            <div style="display: flex; align-items: center; margin: 10px 0 ; font-size: 18px; font-weight: 900;  ">打卡区域：
                <el-input v-model="obj.clock_location" type="textarea" style="width: 500px" />
            </div>
            <div style="display: flex; align-items: center; margin: 10px 0 ; font-size: 18px; font-weight: 900;  ">
                <div>考勤范围： </div><input class="late" type="number" v-model="obj.clock_scope" />
                <div>米</div>
            </div>
            <div style="margin: 10px 0 ;">
                <span style="font-size:18px; font-weight:900; ">中途休息 </span> : &nbsp;
                <el-time-select placeholder="起始时间" v-model="obj.lunch_break_start" :picker-options="{
                  start: '10:30',
                  step: '00:15',
                  end: '14:30'
                }">
                </el-time-select> 至
                <el-time-select placeholder="结束时间" v-model="obj.lunch_break_end" :picker-options="{
                  start: '11:30',
                  step: '00:15',
                  end: '15:30',
                }">
                </el-time-select>
            </div>
            <div style="display: flex; align-items: center; ">
                <div style="margin: 10px 0; font-size: 18px; font-weight: 900; margin-right: 15px;  ">
                    晚走次日晚到 :
                </div>
                <el-switch v-model="obj.late_switch" active-color="#13ce66" inactive-color="#ff4949">
                </el-switch>
            </div>
            <div class="role">
                <div v-if="list.length != 0">
                    <div v-for="(item,index) in list" style="height:5vh;  line-height:5vh; " :key="index">
                        <span style=" display: inline-block; width:4vw;">规则{{index + 1}}:</span> 下班晚走 <input
                            class="late" type="number" v-model="item.work_time" /> 小时, 次日可晚到
                        <input class="late" type="number" v-model="item.late_time" />小时，并且不算迟到。<el-button type="danger"
                            icon="el-icon-delete" @click="del(item)" size="mini" circle></el-button>
                    </div>
                </div>
                <el-button type="primary" class="addrole" @click="addrole">
                    新增规则
                </el-button>
            </div>
            <div style="font-size: 18px; margin-top: 2vw; font-weight: 900; ">弹性打卡时间为 <input class="late" type="number"
                    v-model="obj.flexible_time" /> 分钟</div>
            <div style="font-size: 18px; margin-top: 2vw; font-weight: 900; ">迟到 <input class="late" type="number"
                    v-model="obj.severely_late" /> 分钟记为严重迟到</div>
        </div>
        <el-button type="primary" class="submit_btm" size="medium " @click=" createData ">
            提交
        </el-button>
    </div>
</template>
  
<script>
import { getAttRole, setAttRole, delAttRole } from '@/api/workAttendance'

export default {
    name: 'SetLeaveDay',
    data() {
        return {
            list: [],
            start_time: '',
            end_time: '',
            lateness: '',
            value1: '',
            obj: {}
        }
    },
    created() {
        this.getList()
    },
    methods: {
        addrole() {
            this.list.push({
                work_time: '',
                late_time: '',
                date: new Date().getTime()
            })
        },
        del(e) {
            if (e.id) {
                delAttRole({ id: e.id }).then(res => {
                    if (res.meta.status == 200) {
                        this.getList()
                        this.$notify({
                            title: 'Success',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        })
                    }
                })
            } else {
                const arr = []
                this.list.forEach((item, index) => {
                    if (item.date != e.date) {
                        arr.push(item)
                    }

                })
                this.list = arr
                console.log(this.list)
            }

        },
        getList() {
            this.listLoading = true
            getAttRole().then(response => {
                this.obj = response.data
                if (this.obj.late_switch == 1) {
                    this.obj.late_switch = true
                }
                if (this.obj.late_switch == 2) {
                    this.obj.late_switch = false
                }
                this.list = response.data.role
                setTimeout(() => {
                    this.listLoading = false
                }, 1 * 100)
            })
        },
        createData() {
            this.dialogFormVisible = true
            if (this.obj.late_switch == true) {
                this.obj.late_switch = 1
            }
            if (this.obj.late_switch == false) {
                this.obj.late_switch = 2
            }
            const arr = []
            this.obj.role.forEach((item) => {
                if (item.late_time != '' && item.work_time != '') {
                    arr.push(item)
                }
            })
            this.obj.role = arr
            setAttRole(this.obj).then((response) => {
                if (response.meta.status === 200) {
                    this.dialogFormVisible = false
                    this.$notify({
                        title: 'Success',
                        message: '添加成功',
                        type: 'success',
                        duration: 2000
                    })
                    this.getList()
                }

            })
        },

    }
}

</script>
<style scoped>
.role {
    width: 40vw;
    height: auto;
    border-radius: 10px;
    background: #e9e9e999;
    padding: 10px 2vw;
    margin: 10px;
}

.submit_btm {
    margin-top: 2vw;
    margin-left: 18vw;
}

.left {
    width: 70%;
    padding: 4vh;
}

.right {
    padding-top: 4vh;
    margin-bottom: 1vh;
}

.late {
    width: 55px;
    outline: none;
    border-radius: 5px;
}

.time {
    margin: 12px 0;
    font-size: 18px;
    font-weight: 900;
}

.times {
    font-size: 1vw;
    margin-bottom: 10px;
}

.up_down {
    width: 40vw;
    height: 35vh;
    /* margin: 0 auto; */
    background: #e9e9e999;
    display: flex;
    border-radius: 10px;
}
</style>