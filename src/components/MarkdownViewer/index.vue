<template>
  <div :id="id" />
</template>

<script>
import 'tui-editor/dist/tui-editor-contents.css'
import Viewer from 'tui-editor/dist/tui-editor-Viewer'

export default {
  name: '<PERSON><PERSON>Viewer',
  props: {
    value: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default() {
        return 'markdown-viewer-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      }
    }
  },
  data() {
    return {
      viewer: null
    }
  },
  watch: {
    value(newVal) {
      if (this.viewer && newVal) {
        this.viewer.setValue(newVal)
      }
    }
  },
  mounted() {
    this.initViewer()
  },
  beforeDestroy() {
    this.destroyViewer()
  },
  methods: {
    initViewer() {
      this.viewer = new Viewer({
        el: document.getElementById(this.id),
        initialValue: this.value
      })
    },
    destroyViewer() {
      if (this.viewer) {
        this.viewer.remove()
        this.viewer = null
      }
    }
  }
}
</script>
