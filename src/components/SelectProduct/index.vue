<template>
  <div>
    <!-- 触发按钮 -->
    <slot name="trigger" :showPicker="showProjectPicker">
      <el-button @click="showProjectPicker">选择项目</el-button>
    </slot>

    <!-- 项目选择弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      custom-class="project-select-dialog"
    >
      <!-- 操作按钮区域 -->
      <div class="dialog-header-actions">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="confirmSelect" size="small">确定</el-button>
      </div>

      <div class="project-picker">
        <el-row :gutter="16" style="padding: 16px;">
          <!-- 左栏：项目类别 -->
          <el-col :span="7">
            <div class="category-panel">
              <div class="panel-header">
                <i class="el-icon-folder-opened" style="margin-right: 8px;"></i>
                项目类别
              </div>
              <div class="category-list">
                <div
                  v-for="category in proTypeList"
                  :key="category.id"
                  :class="['category-item', { active: selectedCategoryId === category.id }]"
                  @click="selectCategory(category)"
                >
                  <i class="el-icon-collection-tag" style="margin-right: 8px;"></i>
                  {{ category.name }}
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右栏：项目列表 -->
          <el-col :span="17">
            <div class="project-panel">
              <div class="panel-header">
                <div class="header-title">
                  <i class="el-icon-s-grid" style="margin-right: 8px;"></i>
                  项目列表
                  <span class="project-count">({{ totalProjects }})</span>
                </div>
                <el-row :gutter="12" style="margin-top: 12px;">
                  <el-col :span="14">
                    <el-input
                      v-model="searchForm.pro_name"
                      placeholder="搜索项目名称..."
                      size="small"
                      clearable
                      @input="handleSearch"
                      prefix-icon="el-icon-search"
                    />
                  </el-col>
                  <el-col :span="10">
                    <el-select
                      v-model="searchForm.pro_managers"
                      placeholder="项目经理"
                      size="small"
                      clearable
                      @change="handleSearch"
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="manager in projectManagers"
                        :key="manager.id"
                        :label="manager.user_username"
                        :value="manager.id"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </div>

              <div class="project-list" v-loading="loading">
                <div v-if="filteredProjects.length === 0 && !loading" class="empty-text">
                  暂无项目数据
                </div>
                <el-checkbox-group v-model="selectedProjects" v-if="multiple">
                  <div
                    v-for="project in filteredProjects"
                    :key="project.id"
                    class="project-item"
                  >
                    <el-checkbox :label="project.id">
                      <div class="project-info">
                        <div class="project-name">{{ project.pro_name }}</div>
                        <div class="project-meta">

                          项目经理：{{ project.pro_managers }} | 项目类型：{{ project.pro_type }}| 项目状态：{{ project.status }}
                        </div>
                      </div>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>

                <div v-else>
                  <div
                    v-for="project in filteredProjects"
                    :key="project.id"
                    :class="['project-item', { selected: selectedProject === project.id }]"
                    @click="selectProject(project)"
                  >
                    <div class="project-info">
                      <div class="project-name">{{ project.pro_name }}</div>
                      <div class="project-meta">
                        项目经理：{{ project.pro_managers }} | 项目类型：{{ project.pro_type }} | 项目状态：{{ project.status }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              :current-page="pagination.page"
              :page-sizes="[5, 10, 20, 50]"
              :page-size="pagination.pageSize"
              :total="totalProjects"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </el-row>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getproList, ProCategory, getCompanyUsers } from '@/api/porject/project'

export default {
  name: 'SelectProduct',
  props: {
    title: {
      type: String,
      default: '选择项目'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Object, Array],
      default: () => null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      proTypeList: [],
      projectList: [],
      projectManagers: [],
      selectedCategoryId: null,
      selectedProject: null,
      selectedProjects: [],
      searchForm: {
        pro_name: '',
        pro_managers: ''
      },
      pagination: {
        page: 1,
        pageSize: 5,
        total: 0
      }
    }
  },
  computed: {
    allFilteredProjects() {
      // 所有筛选都由后端处理，直接返回项目列表
      return this.projectList
    },

    filteredProjects() {
      const projects = this.allFilteredProjects
      // 分页处理
      const start = (this.pagination.page - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      return projects.slice(start, end)
    },

    totalProjects() {
      return this.allFilteredProjects.length
    }
  },
  methods: {
    showProjectPicker() {
      this.dialogVisible = true
      this.init()
    },

    async init() {
      this.loading = true
      try {
        await Promise.all([
          this.getProjectTypes(),
          this.getProjects(),
          this.getProjectManagers()
        ])
        this.initSelectedValues()
      } catch (error) {
        console.error('初始化项目选择器失败:', error)
      } finally {
        this.loading = false
      }
    },

    initSelectedValues() {
      if (this.multiple && Array.isArray(this.value)) {
        this.selectedProjects = this.value.map(p => p.id)
      } else if (!this.multiple && this.value) {
        this.selectedProject = this.value.id
      }
    },

    async getProjectTypes() {
      try {
        const response = await ProCategory()
        this.proTypeList = response.data || []
      } catch (error) {
        console.error('获取项目类型失败:', error)
        this.proTypeList = []
      }
    },

    async getProjects() {
      try {
        const params = {
          page: 1,
          page_size: 10000,
          pid: 0
        }

        // 如果选择了项目类型，添加 pro_type 参数
        if (this.selectedCategoryId) {
          params.pro_type = this.selectedCategoryId
        }

        // 如果有搜索条件，添加搜索参数
        if (this.searchForm.pro_name) {
          params.pro_name = this.searchForm.pro_name
        }

        if (this.searchForm.pro_managers) {
          params.pro_managers = this.searchForm.pro_managers
        }

        const response = await getproList(params)
        this.projectList = response.data.data || []
        this.pagination.page = 1
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.projectList = []
      }
    },

    async getProjectManagers() {
      try {
        const response = await getCompanyUsers()
        this.projectManagers = response.data || []
      } catch (error) {
        console.error('获取项目经理失败:', error)
        this.projectManagers = []
      }
    },

    selectCategory(category) {
      this.selectedCategoryId = this.selectedCategoryId === category.id ? null : category.id
      // 重新请求项目列表
      this.getProjects()
    },

    selectProject(project) {
      if (!this.multiple) {
        this.selectedProject = project.id
      }
    },

    handleSearch() {
      // 重新请求项目列表
      this.pagination.page = 1
      this.getProjects()
    },

    handlePageChange(page) {
      this.pagination.page = page
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.page = 1
    },

    confirmSelect() {
      if (this.multiple) {
        const selectedProjectData = this.allFilteredProjects.filter(p =>
          this.selectedProjects.includes(p.id)
        )
        this.$emit('input', selectedProjectData)
        this.$emit('select', selectedProjectData)
      } else {
        const selectedProjectData = this.allFilteredProjects.find(p => p.id === this.selectedProject)
        this.$emit('input', selectedProjectData)
        this.$emit('select', selectedProjectData)
      }
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
/* 对话框样式 */
::v-deep .project-select-dialog {
  border-radius: 12px;
  overflow: hidden;
}

::v-deep .project-select-dialog .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px 16px;
  margin: 0;
}

::v-deep .project-select-dialog .el-dialog__title {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

::v-deep .project-select-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

::v-deep .project-select-dialog .el-dialog__body {
  padding: 0;
}

.dialog-header-actions {
  position: absolute;
  top: 18px;
  right: 60px;
  z-index: 10;
}

.dialog-header-actions .el-button {
  margin-left: 8px;
  border-radius: 20px;
  padding: 8px 20px;
  font-weight: 500;
}

.dialog-header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.dialog-header-actions .el-button--primary:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dialog-header-actions .el-button:not(.el-button--primary) {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.dialog-header-actions .el-button:not(.el-button--primary):hover {
  background: rgba(255, 255, 255, 0.1);
}

.project-picker {
  background: #f8fafc;
}

.category-panel, .project-panel {
  height: 100%;
  border: none;
  border-radius: 0;
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.category-panel {
  margin-right: 10px;
}

.project-panel {
  margin-left: 10px;
}

.panel-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  font-size: 14px;
  color: #2c3e50;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
}

.project-count {
  margin-left: 8px;
  font-size: 12px;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.category-list {
  height: calc(100% - 60px);
  overflow: hidden;
  padding: 8px 0;
}

.category-list::-webkit-scrollbar {
  display: none;
}

.category-list {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.category-item {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #606266;
  position: relative;
}

.category-item:hover {
  background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
  color: #1890ff;
  transform: translateX(2px);
}

.category-item.active {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
  border-radius: 0 2px 2px 0;
}

.project-list {
  height: calc(100% - 150px);
  overflow-y: auto;
  padding: 12px;
}

.project-item {
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.project-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.project-item.selected {
  border-color: #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.project-info {
  display: flex;
  flex-direction: column;
}

.project-name {
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 15px;
  line-height: 1.4;
}

.project-meta {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.5;
}

.pagination-container {
  padding:  15px;
  display: flex;
  justify-content: center;
}

.empty-text {
  text-align: center;
  color: #8c8c8c;
  padding: 40px 20px;
  font-size: 14px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64"><circle cx="32" cy="32" r="30" fill="%23f0f0f0"/><text x="32" y="38" text-anchor="middle" font-size="20" fill="%23ccc">📁</text></svg>') no-repeat center top;
  background-size: 48px 48px;
  padding-top: 80px;
}



</style>
