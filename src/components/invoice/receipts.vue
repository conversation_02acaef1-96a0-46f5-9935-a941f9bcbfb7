<template>
  <div>
    <div style="display: flex; flex-wrap: wrap; padding: 15px 0">
      <!-- 搜索 -->
      <!-- <el-select v-model="listQuery.userId" placeholder="申请人" clearable multiple filterable style="width: 200px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, inx) in importanceOptions4" :key="inx" :label="item.user_username" :value="item.id" />
      </el-select> -->

      <el-date-picker v-model="listQuery.search_time" style="margin: 0 10px" type="daterange" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期" @change="handleFilter" />

      <el-select v-model="listQuery.pro_id" placeholder="项目名称" clearable filterable
        style="width: 190px;margin-right: 20px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in importanceOptions3" :key="i" :label="item.pro_name" :value="item.id" />
      </el-select>

      <!-- <el-select v-model="listQuery.customer_id" placeholder="按客户名称搜索" clearable filterable class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in options" :key="i" :label="item.customer_name" :value="item.id" />
      </el-select> -->
      <div @click="dialogVisibles = true">
        <el-input v-model="listQuery.customer_name" placeholder="按客户名称搜索"></el-input>
      </div>
      <el-dialog title="客户信息表" width="70%" :visible.sync="dialogVisibles">
        <el-table v-loading="loading" ref="multipleTable" @selection-change="handleSelectionChange" :data="tableDatass"
          style="width: 100%;margin: auto" border>
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column prop="date" label="客户名称">
            <template slot-scope="{row}">
              <span>{{  row.customer_name  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="客户等级">
            <template slot-scope="{row}">
              <span>{{  row.customer_level  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="联系人">
            <template slot-scope="{row}">
              <span>{{  row.contact  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="联系电话" width="120px">
            <template slot-scope="{row}">
              <span>{{  row.phone  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" :show-overflow-tooltip='true' label="主营类目" width="160px">
            <template slot-scope="{row}">
              <span>{{  row.categories  }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div style="display:flex;justify-content: flex-end; margin-top: 2vh; ">
          <el-button @click="choise">选择</el-button>
          <el-button @click="toggleSelection()">取消选择</el-button>
        </div>
        <pagination v-show="totalss > 0" :total="totalss" :page.sync="listQuerys.page"
          :limit.sync="listQuerys.page_size" @pagination="getListss" />
      </el-dialog>
      <el-button type="primary" style="margin-left: 10px" @click="handleFilter">搜索</el-button>
      <el-button type="primary" @click="handleCreate()">新增</el-button>
      <el-button type="primary" @click="handleDownload">导出</el-button>
    </div>
    <div>
      <!-- 列表 -->
      <el-table v-loading="loading" :data="tableData" style="width: 100%;margin: auto" border>
        <el-table-column label="序号" type="index" sortable="custom" align="center" width="80" />

        <el-table-column prop="name" label="项目编号">
          <template slot-scope="{row}">
            <span>{{  row.item_num  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="项目名称">
          <template slot-scope="{row}">
            <span>{{  row.pro_name  }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="发票代码">
          <template slot-scope="{row}">
            <span>{{  row.invoice_code  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="发票号码">
          <template slot-scope="{row}">
            <span>{{  row.invoice_num  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="开票时间">
          <template slot-scope="{row}">
            <span>{{  row.date  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="发票类型">
          <template slot-scope="{row}">
            <span>
              <span v-if="row.invoice_type == 1">销项发票</span>
              <span v-if="row.invoice_type == 2">进项发票 </span>
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="name" :show-overflow-tooltip='true' label="开票内容">
          <template slot-scope="{row}">
            <span>{{  row.invoice_text  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="销货方名称">
          <template slot-scope="{row}">
            <span>{{  row.customer_name  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="不含税金额">
          <template slot-scope="{row}">
            <span>{{  row.tax_money  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="税率">
          <template slot-scope="{row}">
            <span>{{  row.tax_rate  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="税额">
          <template slot-scope="{row}">
            <span>{{  row.tax_paid  }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="发票金额">
          <template slot-scope="{row}">
            <span>{{  row.invoice_amount  }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('table.actions')" align="center" width="200px"
          class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
        @pagination="getList" />
    </div>
    <el-dialog title="合同详情" :visible.sync="show_contract">
      <el-select v-model="listQuerys.pro_id" placeholder="按项目名搜索" clearable filterable
        style="width: 290px;margin-bottom:1vw;" class="filter-item" @change="getLists">
        <el-option v-for="(item, i) in importanceOptions3" :key="i" :label="item.pro_name" :value="item.id" />
      </el-select>
      <el-table v-loading="loading" :data="tableDatas" style="width: 100%;margin: auto" border>
        <el-table-column label="序号" type="index" sortable="custom" align="center" width="80" />
        <el-table-column prop="name" label="合同编号">
          <template slot-scope="{row}">
            <span>{{  row.contract_num  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="项目名称">
          <template slot-scope="{row}">
            <span>{{  row.pro_name  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="客户名称">
          <template slot-scope="{row}">
            <span>{{  row.customer_name  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="开户行">
          <template slot-scope="{row}">
            <span>{{  row.bank_name  }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="银行账号">
          <template slot-scope="{row}">
            <span>{{  row.bank_account  }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('table.actions')" align="center" width="200px"
          class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="submit(row)">
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="totals > 0" :total="totals" :page.sync="listQuerys.page" :limit.sync="listQuerys.page_size"
        @pagination="getLists" />
    </el-dialog>
    <!-- 新增删除 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" :inline="true" size="medium" label-width="150px">

        <el-form-item label="申请日期" prop="date">
          <el-date-picker v-model="temp.date" type="date" style="width: 190px" placeholder="选择日期" />
        </el-form-item>
        <el-form-item label="关联合同" prop="date">
          <div @click="contract_terms">
            <el-input v-model="temp.incomeid" disabled placeholder="请选择合同"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="temp.pro_id" style="width: 190px" placeholder="请输入项目名称" disabled />
        </el-form-item>
        <el-form-item label="客户名称" prop="customer_id">
          <el-select v-model="temp.customer_id" placeholder="请选择客户" clearable filterable style="width: 190px"
            class="filter-item">
            <el-option v-for="(item, i) in options" :key="i" :label="item.customer_name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户电话">
          <el-input v-model="temp.phone" style="width: 190px" placeholder="请输入客户电话" />
        </el-form-item>
        <el-form-item label="客户地址">
          <el-input v-model="temp.address" style="width: 190px" placeholder="请输入客户地址" />
        </el-form-item>

        <el-form-item label="客户税号">
          <el-input v-model="temp.social_code" style="width: 190px" placeholder="请输入客户税号" disabled />
        </el-form-item>
        <el-form-item label="发票抬头" prop="invoice_title">
          <el-input v-model="temp.invoice_title" placeholder="请输入发票抬头" />
        </el-form-item>

        <el-form-item label="发票代码" prop="invoice_code">
          <el-input v-model="temp.invoice_code" placeholder="请输入发票代码" />
        </el-form-item>

        <el-form-item label="发票号码" prop="invoice_num">
          <el-input v-model="temp.invoice_num" type="number" placeholder="请输入发票号码" />
        </el-form-item>
        <el-form-item label="开户行">
          <el-input v-model="temp.bank_name" style="width: 190px" placeholder="请输入开户行" disabled />
        </el-form-item>
        <el-form-item label="客户银行账号">
          <el-input v-model="temp.bank_account" style="width: 190px" placeholder="请输入客户银行账号" disabled />
        </el-form-item>

        <el-form-item label="开票金额" prop="invoice_amount">
          <el-input v-model="temp.invoice_amount" type="number" style="width: 190px" placeholder="请输入开票金额"
            @change="handleShow()" />
        </el-form-item>

        <el-form-item label="税率" prop="tax_rate">
          <el-select v-model="temp.tax_rate" placeholder="请输入税率%" clearable filterable style="width: 190px"
            class="filter-item" @visible-change="handleShow()">
            <el-option v-for="(item, i) in rate" :key="i" :label="item.text" :value="item.id" />
          </el-select>
        </el-form-item>


        <el-form-item label="	税额" prop="tax_paid">
          <el-input v-model="temp.tax_paid" type="number" disabled placeholder="请输入税额" />
        </el-form-item>
        <el-form-item label="不含税金额" prop="tax_money">
          <el-input v-model="temp.tax_money" type="number" disabled placeholder="请输入不含税金额" />
        </el-form-item>



        <el-form-item label="开票内容" prop="invoice_text">
          <el-input v-model="temp.invoice_text" type="textarea" :rows="2" placeholder="请输入开票内容" style="width: 30vw" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getALLPro } from '@/api/porject/project'
import { getAllUserInfo } from '@/api/user'
import { CustomerList } from '@/api/customer'
import { contractList } from '@/api/contract'

import { invoiceList, invoiceAdd, invoiceEdit } from '@/api/finance'
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  data() {
    return {
      proName:'',
      importanceOptions3: [], // 项目名称
      options: [], // 按客户搜
      importanceOptions4: [], // 申请人
      listQuery: {
        page: 1,
        page_size: 10,
        userId: [],
        date: '',
        pro_id: '',
        customer_id: '',
        search_time: '',
        invoice_type: '2'
      },
      listQuerys: {
        page: 1,
        page_size: 10,
        type: 2
      },
      dialogStatus: '',
      rules: {
        // user_id: [{ required: true, message: '请选择申请人', trigger: 'change' }],
        date: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
        customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
        pro_id: [{ required: true, message: '请选择项目', trigger: 'change' }],
        invoice_text: [{ required: true, message: '请输入开票内容', trigger: 'blur' }],
        invoice_amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        tax_rate: [{ required: true, message: '请输入税率', trigger: 'blur' }],
        invoice_title: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }]
      },
      tableData: [],
      tableDatas: [],
      tableDatass: [],
      textMap: {
        create: '新增销项发票',
        update: '编辑销项发票'
      },
      loading: false,
      total: 0,
      temp: {
        department: 1,
        date: '',
        customer_id: '',
        customer_id: '',
        customer_ids: '',
        invoice_type: '2',
        invoice_text: '',
        invoice_amount: '',
        tax_rate: '',
        invoice_title: '',
        id: '',
        tax_paid: '',
        tax_money: '',
        invoice_num: '',
        invoice_code: '',
        social_code: ''
      },
      dialogVisible: false,
      dialogVisibles: false,
      totals: 0,
      show_contract: false,
      totalss: 0,
      rate: [
        {
          id: 1,
          text: '1%'
        }, {
          id: 2,
          text: '3%'
        }, {
          id: 3,
          text: '6%'
        }, {
          id: 4,
          text: '13%'
        }
      ],
      multipleSelection: [],
      invoice: [
        {
          id: 1,
          text: '销项发票'
        }, {
          id: 2,
          text: '进项发票'
        }
      ]
    }
  },
  created() {
    this.getList()
    this.handleCustomerList()
    this.getLists()
    this.getListss()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    choise() {
      const arr = []
      const arr_name = []
      if (this.multipleSelection != [] || this.multipleSelection.length != 0) {
        this.multipleSelection.forEach((item) => {
          arr.push(item.id)
          arr_name.push(item.customer_name)
        })
      }
      this.listQuery.customer_id = arr.toString()
      this.listQuery.customer_name = arr_name.toString()
      this.handleFilter()
      this.dialogVisibles = false
    },
    // 取消选择
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
      this.listQuery.customer_id = arr.toString()
      this.listQuery.customer_name = arr_name.toString()
      this.handleFilter()
      this.dialogVisibles = false
    },
    submits(e) {
      this.dialogVisibles = false
      this.listQuery.customer_id = e.id
      this.listQuery.customer_name = e.customer_name
      this.handleFilter()
    },
    getListss() {
      this.loading = true
      CustomerList(this.listQuerys).then(res => {
        this.tableDatass = res.data.data
        this.totalss = res.data.total
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    toPoint(percent) {
      var str = percent.replace("%", "");
      str = str / 100;
      return str;
    },
    submit(e) {
      this.temp.incomeid = e.contract_num       // 合同编号
      this.temp.pro_id = e.pro_name             // 项目名称
      this.temp.pro_ids = e.pro_id
      this.temp.contract_id = e.id              // 合同id
      this.temp.customer_id = e.customer_name   // 客户名称
      this.temp.customer_ids = e.customer_id    // 客户id
      this.temp.address = e.address             // 地址
      this.temp.bank_account = e.bank_account   // 客户银行账号
      this.temp.phone = e.phone                   //客户电话
      this.temp.bank_name = e.bank_name         //开户行
      this.temp.invoice_amount = e.money
      this.temp.social_code = e.social_code  //社会统一信用代码
      this.rate.forEach((item) => {
        if (item.id == e.tax_rate) {
          this.temp.tax_rate = item.text
          this.temp.tax_paid = this.temp.invoice_amount * this.toPoint(this.temp.tax_rate)
          this.temp.tax_money = this.temp.invoice_amount - this.temp.tax_paid
        }
      })
      this.show_contract = false                // 取消合同详情弹窗
    },
    getLists() {
      this.loading = true
      contractList(this.listQuerys).then(response => {
        this.tableDatas = response.data.data
        this.totals = response.data.total
        this.loading = false
      })
    },
    contract_terms() {
      this.show_contract = true
    },

    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async (excel) => {
        const tHeader = [
          '序号',
          '项目编号',
          '项目名称',
          '项目类型',
          '发票代码',
          '发票号码',
          '开票时间',
          '开票类型',
          '开票内容',
          '购买方名称',
          '不含税金额',
          '税率',
          '税额',
          '开票金额'
        ]
        const filterVal = [
          'item_num',
          'pro_name',
          'pro_type',
          'invoice_code',
          'invoice_num',
          'date',
          'invoice_type',
          'invoice_text',
          'customer_name',
          'tax_money',
          'tax_rate',
          'tax_paid',
          'invoice_amount'
        ]

        let explode_data = []
        this.listQuery.page_size = 1000
        await invoiceList(this.listQuery).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data
            this.listQuery.page_size = 10
          }
        })
        const data = this.formatJson(explode_data.data, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '销项发票台账'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          if (v.invoice_type == 1) {
            v.invoice_type = '销项发票'
          }
          if (v.invoice_type == 2) {
            v.invoice_type = '进项发票'
          }
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    },
    getList() {
      this.loading = true
      invoiceList(this.listQuery).then(response => {
        this.tableData = response.data.data

        this.tableData.forEach((item_rate) => {
          if (item_rate.tax_rate == 1) {
            item_rate.tax_rate = '1%'
          }
          if (item_rate.tax_rate == 2) {
            item_rate.tax_rate = '3%'
          }
          if (item_rate.tax_rate == 3) {
            item_rate.tax_rate = '6%'
          }
          if (item_rate.tax_rate == 4) {
            item_rate.tax_rate = '13%'
          }


        })
        this.total = response.data.total
      })
      if (this.listQuery.search_time) {
        this.listQuery.date = this.formatDate(this.listQuery.search_time[0]) + '——' + this.formatDate(this.listQuery.search_time[1])
      }
      if (this.listQuery.userId !== '') {
        this.listQuery.user_id = this.listQuery.userId.join(',')
      }
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    // 搜索
    handleFilter() {
      this.listLoading = true
      if (this.listQuery.search_time == null) {
        this.listQuery.date = ''
      }
      this.listQuery.page = 1
      this.getList()
      setTimeout(() => {
        this.listLoading = false
      }, 500)
    },
    resetTemp() {
      this.temp = {
        department: '1',
        date: '',
        customer_id: '',
        invoice_type: '2',
        invoice_text: '',
        invoice_amount: '',
        tax_rate: '',
        invoice_title: '',
        id: '',
        tax_paid: '',
        tax_money: '',
        invoice_num: '',
        invoice_code: ''
      }
    },
    handleShow() {
      this.calculation()
    },
    calculation() {
      if (this.temp.invoice_amount !== '' && this.temp.tax_rate !== '') {
        this.temp.tax_paid = this.temp.invoice_amount * (this.temp.tax_rate * 0.01)
        this.temp.tax_money = this.temp.invoice_amount - this.temp.tax_paid
      }
    },
    // 新增
    handleCreate() {
      this.dialogVisible = true
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.date = this.formatDate(new Date())
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, handleMerge() {
      this.show_pop = true
    },
    createData() {
      this.listLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.rate.forEach((item) => {
            if (item.text = this.temp.tax_rate) {
              this.temp.tax_rate = item.id
            }
          })
          invoiceAdd(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
      this.getList()
    },
    // 编辑
    handleUpdate(row) {

      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.temp.id = row.id
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.id = row.id
      this.temp.department = 1
      this.temp.incomeid = row.contract_num
      this.temp.customer_id = row.customer_name
      this.temp.pro_id = row.pro_name
      this.temp.customer_ids = row.contract_id
      this.temp.invoice_code = row.invoice_code
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.rate.forEach((item) => {
            if (item.text = this.temp.tax_rate) {
              this.temp.tax_rate = item.id
            }
          })
          this.temp.customer_id = this.temp.customer_ids
          invoiceEdit(this.temp).then(response => {
            this.dialogVisible = false
            this.getList()
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleCustomerList() {
      this.loading = true
      // 获取项目名称
      getALLPro().then(response => {
        this.importanceOptions3 = response.data
      })// 获取申请人
      getAllUserInfo().then((response) => {
        this.importanceOptions4 = response.data
      }) // 客户列表
      CustomerList({ page: 1, page_size: 10, status: 1 }).then(res => {
        this.options = res.data.data
        this.loading = false
      })
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
