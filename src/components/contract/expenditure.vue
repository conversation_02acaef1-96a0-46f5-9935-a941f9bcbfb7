<template>
  <div>
    <div style="display:flex; flex-wrap: wrap; align-items: center; ">
      <div style="display: inline-block;margin-right: 5px;">
        <el-date-picker v-model="listQuery.searchTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="daterange"
          clearable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleFilter" />
      </div>
      <el-select v-model="listQuery.pro_id" placeholder="按项目名搜索" clearable filterable
        style="width: 190px;margin-right: 20px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in importanceOptions3" :key="i" :label="item.pro_name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.amount" placeholder="按开票完成情况搜索" clearable filterable
        style="width: 190px;margin-right: 20px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in amount" :key="i" :label="item.flag_is" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.balance" placeholder="按是否完成收款搜索" clearable filterable
        style="width: 190px;margin-right: 20px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in balance" :key="i" :label="item.flag_is" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.is_receive" placeholder="按是否收到合同搜索" clearable filterable
        style="width: 190px;margin-right: 20px" class="filter-item" @change="handleFilter">
        <el-option v-for="(item, i) in flag" :key="i" :label="item.flag_is" :value="item.id" />
      </el-select>
      <div @click="customerName" style="width: 190px;  ">
        <el-input v-model="listQuery.customer_name"   placeholder="点击选择客户名称" />
      </div>
      <el-button type="primary"  style="margin:10px 10px" @click="handleFilter">
        搜索
      </el-button>
      <el-button type="primary" style="margin:10px 10px" @click="handleCreate()">新增</el-button>
      <el-button :loading="loading"  style="margin:10px 10px" class="filter-item" type="primary" icon="el-icon-download" @click="handleDownload">
        导出
      </el-button>
    </div>
    <div>
      <el-table v-loading="loading" :data="tableData" style="text-align: center" border>
        <el-table-column prop="name" label="项目编号" width="160px">
          <template slot-scope="{row}">
            <span>{{  row.pro_number  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="项目名称">
          <template slot-scope="{row}">
            <span>{{  row.pro_name  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="项目类型">
          <template slot-scope="{row}">
            <span>{{  row.pro_type  }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column prop="name" label="所属公司">
          <template slot-scope="{row}">
            <span>{{ row.company }}</span>
          </template>
        </el-table-column> -->

        <el-table-column prop="name" label="合同编号">
          <template slot-scope="{row}">
            <span>{{  row.contract_num  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="客户名称">
          <template slot-scope="{row}">
            <span>{{  row.customer_name  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="签订日期">
          <template slot-scope="{row}">
            <span>{{  row.signing_date  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="是否收到合同">
          <template slot-scope="{row}">
            <span v-if="row.is_receive == 1">未收到</span>
            <span v-if="row.is_receive == 2">收到</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" :show-overflow-tooltip='true' label="合同摘要">
          <template slot-scope="{row}">
            <span>{{  row.content  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="合同总额">
          <template slot-scope="{row}">
            <span>{{  row.money  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="已收金额">
          <template slot-scope="{row}">
            <span>{{  row.paid  }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="未收金额">
          <template slot-scope="{row}">
            <span>{{  row.unpaid  }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="开票金额">
          <template slot-scope="{row}">
            <span>{{  row.invoice_amount  }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" :show-overflow-tooltip='true' label="备注">
          <template slot-scope="{row}">
            <span>{{  row.remark  }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('table.actions')" align="center" width="200px"
          class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
            <el-button type="primary" size="mini" @click="derive(row)">
              导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
        @pagination="getList" />
      <el-dialog title="供应商信息表" width="70%" :visible.sync="dialogVisibles">
        <el-input v-model="lists.customer_name" clearable style="width: 200px;margin-bottom: 20px"
          placeholder="按供应商名称搜索" @keyup.enter.native="getLists" />
        <el-table v-loading="loading" :data="tableDatas"  ref="multipleTable" @selection-change="handleSelectionChange"  style="width: 100%;margin: auto" border>
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column prop="date" label="客户名称">
            <template slot-scope="{row}">
              <span>{{  row.customer_name  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="客户等级">
            <template slot-scope="{row}">
              <span>{{  row.customer_level  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="联系人">
            <template slot-scope="{row}">
              <span>{{  row.contact  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="联系电话" width="120px">
            <template slot-scope="{row}">
              <span>{{  row.phone  }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" :show-overflow-tooltip='true' label="主营类目" width="160px">
            <template slot-scope="{row}">
              <span>{{  row.categories  }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column :label="$t('table.actions')" align="center" width="200px"
            class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <el-button type="success" size="mini" @click="submit(row)">
                选择
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <div style="display:flex;justify-content: flex-end; margin-top: 2vh; ">
          <el-button @click="choise">选择</el-button>
          <el-button @click="toggleSelection()">取消选择</el-button>
        </div>
        <pagination v-show="totals > 0" :total="totals" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
          @pagination="getLists" />
      </el-dialog>
      <!-- 新增编辑列表 -->
      <el-dialog :title="textMap[dialogStatus]" width="70%" :visible.sync="dialogVisible">
        <el-tabs :tab-position="tabPosition">
          <el-tab-pane label="合同摘要">
            <el-form ref="dataForm" :rules="rules" :model="temp" :inline="true" size="medium" label-width="150px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="客户名称">
                    <!-- <el-select @change="customer" v-model="temp.customer_id" placeholder="公司名称" clearable filterable
                      style="width: 190px" class="filter-item">
                      <el-option v-for="(item, i) in options" :key="i" :label="item.customer_name" :value="item.id" />
                    </el-select> -->
                    <div @click="customerNames" style="width: 190px; ">
                      <el-input v-model="temp.customer_name" disabled placeholder="点击选择客户名称" />
                    </div>
                  </el-form-item>

                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同税率" prop="tax_rate">
                    <el-select v-model="temp.tax_rate" placeholder="请输入税率%" clearable filterable style="width: 190px"
                      class="filter-item">
                      <el-option v-for="(item, i) in rate" :key="i" :label="item.text" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="12">
                  <el-form-item label="所属公司" prop="money">
                    <el-input v-model="temp.company" disabled type="number" placeholder="所属公司" />
                  </el-form-item>
                </el-col> -->
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="关联项目" prop="pro_id">
                    <el-select v-model="temp.pro_id" @change="getPro" placeholder="关联项目" clearable filterable
                      style="width: 190px" class="filter-item">
                      <el-option v-for="(item, i) in importanceOptions3" :key="i" :label="item.pro_name"
                        :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="签订日期" prop="signing_date">
                    <el-date-picker v-model="temp.signing_date" type="date" style="width: 190px" placeholder="选择日期" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="合同总额" prop="money">
                    <el-input v-model="temp.money" type="number" placeholder="请输入合同总额" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="付款方式" prop="payment_method">
                    <el-select v-model="temp.payment_method" placeholder="请输入付款方式" clearable filterable
                      style="width: 190px" class="filter-item">
                      <el-option v-for="(item, i) in payment" :key="i" :label="item.pay_name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="开始时间" :gutter="10" prop="start_time">
                    <el-date-picker v-model="temp.start_time" type="date" placeholder="开始日期" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="结束时间" :gutter="10" prop="end_time">
                    <el-date-picker v-model="temp.end_time" type="date" placeholder="结束日期" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="是否收到合同" prop="is_receive">
                    <el-radio-group v-model="temp.is_receive">
                      <el-radio label="2">收到</el-radio>
                      <el-radio label="1">未收到</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否含税" prop="is_tax">
                    <el-radio-group v-model="temp.is_tax">
                      <el-radio label="2">含税</el-radio>
                      <el-radio label="1">不含税</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="合同摘要" prop="content">
                    <el-input v-model="temp.content" type="textarea" rows="3" style="width: 600px"
                      placeholder="请输入合同摘要" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="附件" prop="attachment">
                    <el-upload ref="upload" class="upload-demo" drag action="fakeaction"
                      :http-request="uploadSectionFile" multiple>
                      <i class="el-icon-upload" />
                      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="temp.remark" type="textarea" rows="2" style="width: 600px" placeholder="请输入备注" />
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-tab-pane>
          <el-dialog :visible.sync="visible" z-index="100">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
          <el-tab-pane class="wrap" label="付款方式">
            <div style="width: 100%;border: 1px solid #ccc;  margin: auto; height: 500px">
              <div style="width: 100%; height: 50px; text-align: center;line-height: 50px">付款方式</div>
              <div style="display: flex">
                <span class="text-title">期数</span>
                <span class="text-title">收款日期</span>
                <span class="text-title">收款金额</span>
                <span class="text-title">比例</span>
                <span class="text-title">状态</span>
                <span class="text-title">操作</span>
              </div>
              <div>
                <el-form ref="dynamicValidateForm" :model="dynamicValidateForm" class="demo-dynamic">
                  <el-form-item v-for="(domain, index) in dynamicValidateForm.domains" :key="domain.key"
                    :prop="'domains.' + index + '.value'">
                    <div style="display: flex">
                      <div class="input">
                        <el-input disabled v-model="domain.period" />
                      </div>
                      <div class="input">
                        <el-date-picker v-model="domain.date" style="width: 150px;" type="date" placeholder="选择日期" />
                      </div>
                      <div class="input" @click="one_count(domain)">
                        <el-input @input="count_money" v-model="domain.money" />
                      </div>
                      <div class="input">
                        <el-input disabled v-model="domain.proportion" />
                      </div>
                      <div class="input">
                        <div v-if="domain.status == 0">待收回</div>
                        <div v-if="domain.status == 1">已收回</div>
                      </div>
                      <div class="input" style="text-align: center; line-height: 50px;">
                        <el-button v-if="index !== 0 && domain.status == 0" type="danger" size="mini"
                          icon="el-icon-delete" @click.prevent="removeDomain(domain)" />
                        <el-button v-if="domain.status == 0 && show_back == 1" type="primary" size="mini"
                          @click.prevent="take(domain)">收回
                        </el-button>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item style="margin: 10px">
                    <el-button @click="addDomain">添加</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            提交
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { CustomerList } from '@/api/customer'
import { contractList, contractAdd, contractEdit, paymentList, contractCheck } from '@/api/contract'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import { getALLPro } from '@/api/porject/project'
import { fileUpload } from '@/api/system/sys'

export default {
  components: { Pagination },
  data() {
    return {
      dialogVisibles: false,
      tableDatas: [],
      totals: 0,
      flag: [
        {
          id: 2,
          flag_is: '收到'
        }, {
          id: 1,
          flag_is: '未收到'
        }
      ],
      amount: [
        {
          id: 0,
          flag_is: '未开票'
        }, {
          id: 1,
          flag_is: '已开票'
        }
      ],
      balance: [{
        id: 0,
        flag_is: '已完成收款'
      }, {
        id: 1,
        flag_is: '未完成收款'
      }],
      tableData: [],
      importanceOptions3: [],
      listQuery: {
        page: 1,
        page_size: 10,
        pro_id: '',
        customer_name: '',
        pro_number: '',
        type: 2,
        customer_id: '',
        searchTime: [],
        amount: '',
        balance: ''
      },
      payment: [],
      value1: '',
      value: '',
      loading: false,
      tabPosition: 'left',
      textMap: {
        create: '新增合同',
        update: '编辑合同'
      },
      rules: {
        customer_id: [{ required: true, message: '请输入客户名称', trigger: 'change' }],
        pro_id: [{ required: true, message: '请输入关联项目', trigger: 'change' }],
        signing_date: [{ required: true, message: '请选择签订日期', trigger: 'change' }],
        invoice_amount: [{ required: true, message: '请选择开票金额', trigger: 'blur' }],
        is_tax: [{ required: true, message: 'is_tax', trigger: 'blur' }],
        money: [{ required: true, message: '请输入合同总额', trigger: 'blur' }],
        paid: [{ required: true, message: '已收款金额', trigger: 'blur' }],
        payment_method: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
        start_time: [{ required: true, message: '请选择时间', trigger: 'change' }],
        end_time: [{ required: true, message: '请选择时间', trigger: 'change' }],
        tax_rate: [{ required: true, message: '税率', trigger: 'change' }]
      },
      total: 0,
      dialogStatus: '',
      show_pop: false,
      dynamicValidateForm: {
        domains: [{
          period: '',
          date: '',
          money: '',
          proportion: '',
          status: 0
        }]
      },
      lists: {
        page: 1,
        page_size: 10,
        type: 2
      },
      temp: {
        signing_date: '',
        is_receive: '1',
        content: '',
        money: '',
        paid: '',
        unpaid: '',
        invoice_amount: '',
        remark: '',
        company: '',
        pro_id: '',
        pro_ids: '',
        start_time: '',
        end_time: '',
        tax_rate: '',
        is_tax: '1',
        customer_id: '',
        payment_method: '',
        attachment: '',
        period: '',
        proportion: '',
        list: [],
        importanceOptions3: [],
        id: '',
        type: 2,
        customer_name: ''

      },
      options: [],
      labelPosition: 'left',
      dialogImageUrl: '',
      visible: false,
      dialogVisible: false,
      disabled: false,
      rate: [
        {
          id: 1,
          text: '1%'
        }, {
          id: 2,
          text: '3%'
        }, {
          id: 3,
          text: '6%'
        }, {
          id: 4,
          text: '13%'
        }
      ],
      num_period: 1,
      show_back: 0

    }
  },
  created() {
    this.handleCustomerList()
    this.getList()
    this.getLists()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    choise() {
      const arr = []
      const arr_name = []
      if (this.multipleSelection != [] || this.multipleSelection.length != 0) {
        this.multipleSelection.forEach((item) => {
          arr.push(item.id)
          arr_name.push(item.customer_name)
        })
      }
      this.listQuery.customer_id = arr.toString()
      this.listQuery.customer_name = arr_name.toString()
      this.handleFilter()
      this.dialogVisibles = false
    },
    // 取消选择
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
      this.listQuery.customer_id = ''
      this.listQuery.customer_name = ''
      this.handleFilter()
      this.dialogVisibles = false
    },


    getLists() {
      this.loading = true
      CustomerList(this.lists).then(res => {
        this.tableDatas = res.data.data
        this.totals = res.data.total
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    getPro(e) {
      this.temp.pro_ids = e
    },
    // 选择当前客户
    submit(e) {
      if (this.search_add == 1) {
        this.temp.customer_name = e.customer_name
        this.temp.customer_id = e.id
        this.customer(e.id)
        this.dialogVisibles = false
      }
      if (this.search_add == 2) {
        this.listQuery.customer_id = e.id
        this.listQuery.customer_name = e.customer_name
        this.handleFilter()
        this.dialogVisibles = false
      }

    },
    // 点击客户名称
    customerName() {
      this.dialogVisibles = true
      this.search_add = 2
    },
    customerNames() {
      this.dialogVisibles = true
      this.search_add = 1
    },
    take(e) {
      contractCheck({ id: e.id }).then(res => {
        if (res.meta.status == 200) {
          this.dialogVisible = false
          this.tableData = []
          this.getList()
        }
      })
    },
    one_count(e) {
      this.itemId = e.period
    },
    count_money(e) {
      this.dynamicValidateForm.domains.forEach((item) => {
        if (item.period == this.itemId) {
          item.proportion = e / this.temp.money
          item.proportion = item.proportion.toFixed(2);

        }
      })
    },
    customer(e) {
      getALLPro({ customer_id: e }).then(res => {
        if (res.data.length != 0 || res.data != []) {
          this.temp.pro_id = res.data[0].pro_name
          this.temp.pro_ids = res.data[0].id
        }
      })
    },
    //导出单条
    derive(e) {
      this.rate.forEach((item) => {
        if (item.id == e.tax_rate) {
          this.taxRate = item.text
        }
      })
      const oneHeader = ['新增支出合同', '', '', '']
      const twoHeader = ['供应商名称', e.customer_name, '', '']
      const fiveHeader = ['编号', e.customer_name, '', '']
      const sevenHeader = ['申请人', e.user_id, '申请部门', e.department_id]
      const fourHeader = ['关联项目', e.pro_name, '', '']
      const eightHeader = ['开始时间', e.start_time, '结束时间', e.end_time]
      const nineHeader = ['合同总额', e.money, '税率', this.taxRate]
      const tenHeaders = ['', '', '', '']
      const sixHeader = ['付款方式', '', '', '']
      const tenHeader = ['期数', '收款日期', '收款金额', '比例']
      const merges = ['A1:D1', 'B2:D2', 'B3:D3', 'B5:D5', 'A8:D8', 'A9:D9']
      const filterVal = ['period', 'date', 'money', 'proportion']
      import('@/vendor/Export2Excel').then(async excel => {
        const multiHeader = [oneHeader, twoHeader, fiveHeader, sevenHeader, fourHeader, eightHeader, nineHeader, tenHeaders, sixHeader, tenHeader]
        const header = []
        const data = this.formatJson(e.list, filterVal)
        excel.export_json_to_excel({
          multiHeader,
          header,
          data,
          filename: '支出合同台账',
          merges
        })
      })
    },
    // 列表展示信息
    getList() {
      this.loading = true
      contractList(this.listQuery).then(res => {
        this.tableData = res.data.data
        this.total = res.data.total
        this.loading = false
      })
    },
    // 重置
    resetTemp() {
      this.temp = {
        signing_date: this.formatDate(new Date()),
        is_receive: '1',
        content: '',
        is_tax: '1',
        money: '',
        paid: '',
        unpaid: '',
        invoice_amount: '',
        attachment: '',
        remark: '',
        start_time: '',
        end_time: '',
        type: 2,
        pro_id: '',
        payment_method: '',
        list: []
      }
      this.dynamicValidateForm = {
        domains: [{
          period: this.num_period,
          date: '',
          money: '',
          proportion: '',
          status: 0
        }]
      }
    },
    // 新增
    handleCreate() {
      this.show_back = 0,
        this.num_period = 1
      this.dialogVisible = true
      this.dialogStatus = 'create'
      this.resetTemp()
      // this.$refs.upload.clearFiles()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleMerge() {
      this.show_pop = true
    },
    createData() {
      let counst = 0.00
      for (var i = 0; i < this.dynamicValidateForm.domains.length; i++) {
        counst += Number(this.dynamicValidateForm.domains[i].money)
      }
      if (counst > this.temp.money) {
        this.$notify({
          title: 'error',
          message: '核对金额',
          type: 'error',
          duration: 2000
        })
      } else {
        this.listLoading = true
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            if (this.temp.pro_ids != '') {
              this.temp.pro_id = this.temp.pro_ids
            }
            this.dont = true
            this.dynamicValidateForm.domains.forEach((item) => {
              if (item.date == '' || item.money == '' || item.proportion == '') {
                this.dont = false
              }
            })
            if (this.dont == true) {
              this.temp.list = this.dynamicValidateForm.domains
            }
            if (this.dont == true) {
              this.temp.list = []
            }
            contractAdd(this.temp).then(response => {
              this.dialogVisible = false
              this.$notify({
                title: 'Success',
                message: '提交成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
              setTimeout(() => {
                this.listLoading = false
              }, 500)
            })
          }
        })
        this.getList()
      }
    },
    // 编辑
    handleUpdate(row) {
      this.show_back = 1
      this.num_period = row.list.length
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.dynamicValidateForm.domains = []
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.dynamicValidateForm.domains = row.list
      this.temp.is_receive = row.is_receive.toString()
      this.temp.is_tax = row.is_tax.toString()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.id = row.id
      this.temp.payment_method = row.payment_method.id
    },
    updateData() {
      let counst = 0.00
      for (var i = 0; i < this.dynamicValidateForm.domains.length; i++) {
        counst += Number(this.dynamicValidateForm.domains[i].money)
      }
      if (counst > this.temp.money) {
        this.$notify({
          title: 'error',
          message: '核对金额',
          type: 'error',
          duration: 2000
        })
      } else {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            contractEdit(this.temp).then(response => {
              this.dialogVisible = false
              this.getList()
              this.$notify({
                title: 'Success',
                message: '提交成功',
                type: 'success',
                duration: 2000
              })
            })
          }
        })
      }
    },
    // 搜索
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.getList()
      setTimeout(() => {
        this.listLoading = false
        this.listQuery.customer_name = ''
        this.listQuery.customer_id = ''
      }, 500)
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form)
        .then(res => {
          this.temp.attachment = res.data.url + ','
        })
    },
    // 导出
    handleDownload() {
      this.loading = true
      import('@/vendor/Export2Excel').then(async (excel) => {
        const tHeader = ['序号', '项目编号', '项目名称', '项目类型', '合同编号', '客户名称', '签订日期', '是否收到合同', '合同摘要', '合同总额', '已收金额', '未收金额', '开票金额', '备注']
        const filterVal = ['pro_number', 'pro_name', 'pro_type', 'contract_num', 'customer_name', 'signing_date', 'is_receive', 'content', 'money', 'paid', 'unpaid', 'invoice_amount', 'remark']

        let explode_data = []
        this.listQuery.page_size = 100
        await contractList(this.listQuery).then(res => {
          if (res.meta.status === 200) {
            explode_data = res.data.data
            this.listQuery.page_size = 10
          }
        })

        const data = this.formatJson(explode_data, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '合同支出管理'
        })
        setTimeout(() => {
          this.loading = false
        }, 100)
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          if (v.is_receive == 1) {
            v.is_receive = '未收到'
          }
          if (v.is_receive == 2) {
            v.is_receive = '收到'
          }
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 调用客户名字接口
    handleCustomerList() {
      this.loading = true
      CustomerList(this.lists).then(res => {
        this.options = res.data.data
      })
      getALLPro().then(response => {
        this.importanceOptions3 = response.data
      })
      paymentList().then(response => {
        this.payment = response.data
      })
    },
    removeDomain(item) {
      const index = this.dynamicValidateForm.domains.indexOf(item)
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1)
      }
    },
    addDomain() {
      this.show_back = 0
      this.dynamicValidateForm.domains.push({
        period: ++this.num_period,
        date: '',
        money: '',
        proportion: '',
        key: Date.now(),
        status: 0
      })
    },
    formatDate(value) {
      if (value == null) {
        return ''
      } else {
        const date = new Date(value)
        const y = date.getFullYear()// 年
        let MM = date.getMonth() + 1 // 月
        MM = MM < 10 ? ('0' + MM) : MM
        let d = date.getDate() // 日
        d = d < 10 ? ('0' + d) : d
        return y + '-' + MM + '-' + d
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-title {
  display: block;
  width: 20%;
  border: 1px solid #ccc;
  height: 50px;
  text-align: center;
  line-height: 50px;
}

.input {
  width: 20%;
  height: 50px;
  border: 1px solid #ccc;

  ::v-deep .el-input__inner {
    border: 0;
    display: block;
    height: 48px;
  }
}

.wrap {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
</style>
