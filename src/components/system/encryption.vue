<template>
  <div>
    <el-form
      ref="ruleForm"
      v-loading="loading"
      :model="temp"
      :rules="rules"
      label-width="110px"
      class="demo-ruleForm"
    >
      <el-form-item label="启用文件加密" prop="status">
        <el-switch v-model="status" />
      </el-form-item>
      <el-form-item label="文件类型限制" prop="type">
        <el-input v-model="temp.type" type="textarea" style="width: 500px" />
      </el-form-item>
      <el-form-item label="文件加密方式" prop="encryption_way">
        <el-radio v-model="temp.encryption_way" :label="1">部分加密</el-radio>
        <el-radio v-model="temp.encryption_way" :label="2">完整加密</el-radio>
      </el-form-item>
      <el-form-item label="文件大小限制" prop="file_size">
        <el-input v-model="temp.file_size" style="width: 80px" /> KB
      </el-form-item>
      <el-form-item label="文件加密算法" prop="encryption_des">
        <!-- <el-checkbox v-for="city in list.encryption_des" :key="city" :label="city">{{ city }}</el-checkbox> -->
        <el-radio v-model="temp.encryption_des" label="1">AES-256-CBS</el-radio>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="handleSubmit">提交</el-button>
  </div>
</template>

<script>
import { encryptionList, encryptionUp } from '@/api/encryption'
export default {
  data() {
    return {
      list: [],
      status: false,
      loading: false,
      temp: {
        id: '',
        status: '',
        type: '',
        encryption_way: '',
        encryption_des: '',
        file_size: ''
      },
      rules: {
        status: [{ required: true, message: '请启用文件加密', trigger: 'change' }],
        file_size: [{ required: true, message: '请输入文件大小限制', trigger: 'change' }],
        encryption_way: [{ required: true, message: '请选择文件加密方式', trigger: 'blur' }],
        type: [{ required: true, message: '请输入类型', trigger: 'change' }],
        encryption_des: [{ required: true, message: '请选择加密算法', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      encryptionList().then(res => {
        this.list = res.data
        this.temp = Object.assign({}, res.data)
        this.temp.id = res.data.id
        this.list.encryption_des = 3
        if (this.temp.status === 1) {
          this.status = true
        } else {
          this.status = false
        }
      })
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    handleSubmit() {
      this.temp.type = this.temp.type.toString()
      encryptionUp(this.temp).then(res => {})
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
