<template>
  <div class="app-container" style="">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="140px"
      class="demo-ruleForm"
    >
      <el-form-item label="报销审核人">
        <el-select
          v-model="ruleForm.expense_examiner"
          multiple
          filterable
          placeholder="请选择报销审核人"
        >
          <el-option
            v-for="item in options_check"
            :key="item.id"
            :label="item.user_username"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="补卡审核人">
        <el-select
          v-model="ruleForm.reviewer_id"
          multiple
          filterable
          placeholder="请选择补卡审核人"
        >
          <el-option
            v-for="item in options_card"
            :key="item.id"
            :label="item.user_username"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="请假审核人">
        <el-select
          v-model="ruleForm.leave_examiner"
          multiple
          filterable
          placeholder="请选择请假审核人"
        >
          <el-option
            v-for="item in options_leave"
            :key="item.id"
            :label="item.user_username"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
     
      
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >提交
        </el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { attendanceSysInfo, attendanceSet } from '@/api/workAttendance'
import { getCompanyUsers } from '@/api/project'

export default {
  name: 'SetUp',
  data() {
    return {
      value1: '',
      value2: '',
      options_check: [], // 报销列表
      options_card: [], // 补卡列表
      options_leave: [], // 请假列表
      ruleForm: {
        clock_intime: undefined,
        clock_offtime: undefined,
        clock_intimes: undefined,
        clock_offtimes: undefined,
        clock_location: undefined,
        expense_examiner: [],
        reviewer_id: [],
        leave_examiner: []
      },
      temp: {
        clock_intime: undefined,
        clock_offtime: '',
        clock_intimes: '',
        clock_offtimes: '',
        clock_location: '',
        expense_examiner: [],
        reviewer_id: [],
        leave_examiner: []
      },
      rules: {
        clock_intime: [
          {
            required: true,
            message: '请选择日期',
            trigger: 'change'
          }
        ],
        clock_offtime: [
          {
            required: true,
            message: '请选择时间',
            trigger: 'change'
          }
        ],
        clock_intimes: [
          {
            required: true,
            message: '请选择日期',
            trigger: 'change'
          }
        ],
        clock_offtimes: [
          {
            required: true,
            message: '请选择时间',
            trigger: 'change'
          }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getCompanyUsers()
  },
  methods: {
    getList() {
      attendanceSysInfo().then((response) => {
        this.ruleForm.clock_intime = response.data[0].clock_intime
        this.ruleForm.clock_offtime = response.data[0].clock_offtime
        this.ruleForm.clock_intimes = response.data[0].clock_intimes
        this.ruleForm.clock_offtimes = response.data[0].clock_offtimes
        this.ruleForm.clock_location = response.data[0].clock_location

        const data = this.getValueOfUser(response.data[0])
        this.ruleForm.expense_examiner = data[0]
        this.ruleForm.reviewer_id = data[1]
        this.ruleForm.leave_examiner = data[2]
      })
    },
    getCompanyUsers() {
      getCompanyUsers().then(response => {
        this.options_check = response.data
        this.options_card = response.data
        this.options_leave = response.data
      })
    },
    submitForm(ruleForm) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.ruleForm.expense_examiner = this.ruleForm.expense_examiner.toString()
          this.ruleForm.leave_examiner = this.ruleForm.leave_examiner.toString()
          this.ruleForm.reviewer_id = this.ruleForm.reviewer_id.toString()

          attendanceSet(this.ruleForm).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    getValueOfUser(data) {
      const expense_examiner_id = []
      const reviewer_id_id = []
      const leleave_examiner_id = []

      for (let i = 0; i < data.expense_examiner.length; i++) {
        expense_examiner_id.push(data.expense_examiner[i].id)
      }

      for (let i = 0; i < data.reviewer_id.length; i++) {
        reviewer_id_id.push(data.reviewer_id[i].id)
      }

      for (let i = 0; i < data.leave_examiner.length; i++) {
        leleave_examiner_id.push(data.leave_examiner[i].id)
      }

      return [expense_examiner_id, reviewer_id_id, leleave_examiner_id]
    }
  }
}
</script>
<style scoped>
.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 0px;
}
</style>
