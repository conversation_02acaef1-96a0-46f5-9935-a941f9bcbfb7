<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <el-select v-model="listQuery.category_id" placeholder="申请类型" clearable style="width: 150px; margin: 0"
                 class="filter-item" @change="handleFilter"
      >
        <el-option v-for="(item, idx) in importanceOptions1" :key="idx" :label="item.expense_type" :value="item.id"/>
      </el-select>
      <el-select v-model="listQuery.pro_id" placeholder="项目名称" filterable clearable style="width: 200px"
                 class="filter-item" @change="handleFilter"
      >
        <el-option v-for="(item, iex) in importanceOptions3" :key="iex" :label="item.pro_name" :value="item.id"/>
      </el-select>
      <el-select v-model="listQuery.userId" placeholder="申请人" clearable multiple filterable style="width: 200px"
                 class="filter-item" @change="handleFilter"
      >
        <el-option v-for="(item, inx) in importanceOptions4" :key="inx" :label="item.user_username" :value="item.id"/>
      </el-select>

      <el-select v-model="listQuery.finance_check" placeholder="部门审核" clearable style="width: 150px"
                 class="filter-item"
                 @change="handleFilter"
      >
        <el-option v-for="(item, ind) in importanceOptions2" :key="ind" :label="item.status" :value="item.id"/>
      </el-select>

      <el-select v-model="listQuery.status" placeholder="财务审核" clearable style="width: 150px" class="filter-item"
                 @change="handleFilter"
      >
        <el-option v-for="(item, ind) in importanceOptions2" :key="ind" :label="item.status" :value="item.id"/>
      </el-select>


      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
                 @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <el-table :key="tableKey" ref="multipleTable" v-loading="listLoading" :data="list" border fit highlight-current-row
              style="width: 100%" @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" align="center" type="index" :index="indexAdd" width="50"/>
      <el-table-column label="申请人" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.user_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>

      <el-table-column label="项目名称" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.pro_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="付款时间" width="180px" align="center">
        <template slot-scope="{ row }">
          <p>开始时间：{{ row.start_time }}</p>
          <p>结束时间：{{ row.end_time }}</p>
        </template>
      </el-table-column>

      <el-table-column label="费用类型" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.category_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.money }}</span>
        </template>
      </el-table-column>
      <el-table-column label="付款事由" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.reason }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收款人信息" width="180px" align="left">
        <template slot-scope="{ row }">
          <p>收款人名称：{{ row.pay_name }}</p>
          <p>收款人开账号：{{ row.bank_accounts }}</p>
          <p>收款人开户行：{{ row.bank_name }}</p>
        </template>
      </el-table-column>
      <el-table-column label="申请附件" width="200px" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.cost_photo.length" class="prove">
            <div v-for="(v, k) in row.cost_photo" :key="k" class="voucher">
              <el-image style="width: 50px; height: 50px" :src="v" :scroll-container="scrollContainer"
                        :preview-src-list="row.cost_photo" @click="big_image(v)"
              />
            </div>
          </div>
          <div v-if="row.cost_proof.length > 0" style="display: flex;justify-content: space-around;">
            <div v-for="(file_item, filedex) in row.cost_proof" :key="filedex">
              <i class="el-icon-document-remove"/> <a :href="file_item" title="点击预览打印"
                                                      target="_blank"
            >报销凭证</a>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="付款凭证" width="200px" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.pay_evidence.length > 0" style="display: flex;justify-content: space-around;">
            <div v-for="(file_item, filedex) in row.pay_evidence" :key="filedex">
              <i class="el-icon-document-remove"/>
              <a :href="file_item" title="点击预览打印" target="_blank">报销凭证</a>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="部门审核人" align="center" width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.expense_examiner }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门审核状态" align="center" width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.finance_check }}</span>
        </template>
      </el-table-column>
      <el-table-column label="财务审核人" align="center" width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.finance_check_user }}</span>
        </template>
      </el-table-column>
      <el-table-column label="财务审核状态" align="center" width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.finance_check_status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经理审核人" align="center" width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.manager_check }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经理审核状态" align="center" width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.manager_status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驳回原因" align="center">
        <template slot-scope="{ row }">
          <span style="
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            "
          >{{ row.pass_reason }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="status-col" width="200px">
        <template slot-scope="{ row }">
          <div>
            <el-button type="primary" size="mini" @click="derive(row)">
              导出
            </el-button>
            <el-button type="primary" size="mini" @click="showEvidence(row)"
                       :disabled="row.manager_status !== '已通过'"
            >
              上传付款凭证
            </el-button>
          </div>
          <div style="margin-top: 4px;">
            <el-button
              type="primary"
              size="mini"
              v-if="row.manager_check === ''"
              :disabled="row.finance_check_status === '待审核' && row.finance_check !== '已通过'"
              @click="checkInformal(row)"
            >
              财务通过
            </el-button>

            <el-button
              type="danger"
              size="mini"
              v-if="row.manager_check === ''"
              :disabled="row.finance_check_status === '待审核' && row.finance_check !== '已通过'"
              @click="handleUpdate(row)"
            >
              财务驳回
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
                @pagination="getList"
    />

    <!--驳回-->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="90px"
               style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="驳回原因" prop="pass_reason">
          <el-input v-model="temp.pass_reason" style="margin-top: 15px" type="textarea" :rows="2"
                    placeholder="请输入驳回意见"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消</el-button>
        <el-button type="primary" @click="updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
    <!--上传凭证-->

    <el-dialog
      title="上传付款凭证"
      :visible.sync="evidenceVisible"
      @close="fileList = []"
    >
      <el-upload
        class="upload-demo"
        :http-request="uploadSectionFile"
        :on-success="handleUploadSuccess"
        :on-remove="handleRemove"
        :file-list="fileList"
        :limit="5"
        multiple
        action=""
        list-type="text"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="evidenceVisible = false">取消</el-button>
        <el-button type="primary" @click="addEvidence()">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { costApplyList, costApplyAudit, paymentVoucher } from '@/api/finance'
import { getALLPro, searchCompany } from '@/api/porject/project'
import { getAllUserInfo } from '@/api/user'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import { fileUpload } from '@/api/system/sys'

export default {
  name: 'ComplexTable',
  components: { Pagination },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      scrollContainer: HTMLCollection,
      srcList: [' '],
      tableKey: 0,
      list: [],
      multipleSelection: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        pro_id: '',
        finance_check: '',
        expense_examiner: '',
        category_id: '',
        userId: []
      },
      searchTime: null,
      listData: {
        page: 1,
        page_size: 20
      },
      importanceOptions: [],
      importanceOptions1: [],
      importanceOptions2: [
        {
          status: '待审核',
          id: '1'
        },
        {
          status: '已通过',
          id: '2'
        },
        {
          status: '拒绝',
          id: '3'
        }
      ],
      importanceOptions3: [],
      importanceOptions4: [],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        type: '',
        reason: '',
        status: 'published',
        pass_reason: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        examine: '审核驳回'
      },
      dialogPvVisible: false,
      pvData: [],
      rules: {
        type: [
          { required: true, message: 'type is required', trigger: 'change' }
        ],
        timestamp: [
          {
            type: 'date',
            required: true,
            message: 'timestamp is required',
            trigger: 'change'
          }
        ],
        pass_reason: [
          { required: true, message: '请输入驳回原因', trigger: 'change' }
        ]
      },
      downloadLoading: false,
      numId: 0,
      evidenceVisible: false,
      fileList: [],
      currentRow: null,
    }
  },
  created() {
    this.getList()
    this.searchList()
  },
  methods: {
    derive(e) {
      const oneHeader = ['业务付款申请', '', '', '']
      const twoHeader = ['申请人', e.user_name, '申请部门', e.dept_name]
      const threeHeader = ['项目名称', e.pro_name, '费用类型', e.category_name]
      const fourHeader = ['付款时间', e.start_time, '最晚付款时间', e.end_time]
      const fiveHeader = ['付款金额', e.money, '付款事由', e.reason]
      const sixHeader = ['收款人名称', e.pay_name, '', '']
      const sevenHeader = ['收款人账号', e.bank_accounts, '', '']
      const eightHeader = ['收款人开户行', e.bank_name, '', '']
      const tenHeader = ['部门审核人', e.expense_examiner, '部门审核状态', e.finance_check]
      const eleven = ['财务审核人', e.finance_check_user, '财务审核状态', e.finance_check_status]
      const twelve = ['经理审核人', e.manager_check, '经理审核状态', e.manager_status]
      const thirteen = ['驳回原因', e.pass_reason, '', '']
      const merges = ['A1:D1', 'B6:D6', 'B7:D7', 'B8:D8', 'B12:D12']
      const columnWidths = 100
      import('@/vendor/Export2Excel').then(async excel => {
        const multiHeader = [oneHeader, twoHeader, threeHeader, fourHeader, fiveHeader, sixHeader, sevenHeader, eightHeader,
          tenHeader, eleven, twelve, thirteen]
        const header = []
        const data = []
        excel.export_json_to_excel({
          multiHeader,
          header,
          data,
          filename: '业务付款申请',
          merges,
          columnWidths
        })
      })
    },
    showEvidence(row) {
      this.currentRow = row;
      this.fileList = [];
      this.$nextTick(() => {
        if (row.pay_evidence && row.pay_evidence.length) {
          this.fileList = row.pay_evidence.map((url, index) => ({
            name: `已上传文件${index + 1}`,
            url: url,
            status: 'success'
          }));
        }
        this.evidenceVisible = true;
      });
    },

    handleUploadSuccess(response, file, fileList) {

      if(response.meta.status === 200) {
        this.$message.success('文件上传成功');
        // 更新文件列表
        this.fileList = fileList.map(file => {
          if(file.response) {
            return {
              name: file.name,
              url: file.response.data.url,
              status: 'success'
            }
          }
          return file;
        });
      } else {
        this.$message.error('文件上传失败');
        const index = fileList.indexOf(file);
        fileList.splice(index, 1);
      }
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    addEvidence() {
      if (!this.fileList.length) {
        this.$message.warning("请上传至少一个付款凭证！");
        return;
      }

      // 获取所有文件的URL
      const uploadedUrls = this.fileList.map(file => file.url).filter(url => url);

      // 构造请求参数
      const params = {
        id: this.currentRow.id,
        pay_evidence: uploadedUrls.join(',')
      };


      // 调用API保存付款凭证
      paymentVoucher(params).then(response => {
        if (response.meta.status === 200) {
          this.$message.success("凭证上传成功！");
          this.evidenceVisible = false;
          this.getList(); // 刷新列表
        } else {
          this.$message.error(response.meta.msg || "凭证上传失败！");
        }
      }).catch(() => {
        this.$message.error("凭证上传失败！");
      });
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      form.append('file', file)
      form.append('type', 2)
      fileUpload(form)
        .then(res => {
          params.onSuccess(res, file)
        })
        .catch(() => {
          params.onError()
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = []
      for (var i = 0; i < val.length; i++) {
        this.multipleSelection.push(val[i].id)
      }
      return this.multipleSelection
    },
    getSortClass: function(key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    big_image(item) {
      this.srcList[0] = item
      this.$nextTick(() => {
        const wrapper = document.getElementsByClassName(
          'el-image-viewer__actions__inner'
        )
        const downImg = document.createElement('i')
        downImg.setAttribute('class', 'el-icon-download')
        wrapper[0].appendChild(downImg)
        if (wrapper.length > 0) {
          this.wrapperElem = wrapper[0]
          this.cusClickHandler()
        }
      })
    },
    cusClickHandler() {
      this.wrapperElem.addEventListener('click', this.hideCusBtn)
    },
    hideCusBtn(e) {
      const className = e.target.className
      if (className === 'el-icon-download') {
        const imgUrl = document.getElementsByClassName(
          'el-image-viewer__canvas'
        )[0].children[0].src
        this.downloadImage(imgUrl)
      }
    },
    downloadImage(imgUrl) {
      imgUrl = imgUrl.substring(imgUrl.lastIndexOf('/'))
      imgUrl = imgUrl.substring(1, imgUrl.length)
      window.location.href = process.env.VUE_APP_BASE_API + '/admin/downloadedImg?file=' + imgUrl
    },
    getList() {
      if (this.searchTime) {
        this.listQuery.pro_end_time =
          this.searchTime[0] + '——' + this.searchTime[1]
      }
      if (this.listQuery.userId !== '') {
        this.listQuery.user_id = this.listQuery.userId.join(',')
      }

      this.listLoading = true
      costApplyList(this.listQuery).then((response) => {
        response.data.data.forEach((item) => {
          if (item.cost_photo === '') {
            item.cost_photo = []
          } else {
            item.cost_photo = item.cost_photo.split(',')
          }

          if (item.cost_proof === '') {
            item.cost_proof = []
          } else {
            item.cost_proof = item.cost_proof.split(',')
          }

          if (item.pay_evidence === '') {
            item.pay_evidence = []
          } else {
            item.pay_evidence = item.pay_evidence.split(',')
          }

        })
        this.list = response.data.data
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      })
    },
    searchList() {
      searchCompany(this.listData).then((response) => {
        this.importanceOptions = response.data.data
      })
      getALLPro().then(response => {
        this.importanceOptions3 = response.data
      })
      getAllUserInfo().then((response) => {
        this.importanceOptions4 = response.data
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      if (this.searchTime == null) {
        this.listQuery.pro_end_time = ''
      }
    },
    checkInformal(row) {
      this.$confirm('是否确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        costApplyAudit({ id: row.id, status: 2 }).then(response => {
          this.$message({
            type: 'success',
            message: '审核通过!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消审核'
        })
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        status: 'published',
        type: ''
      }
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.timestamp = new Date(this.temp.timestamp)
      this.dialogStatus = 'examine'
      this.dialogFormVisible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const obj = { id: this.temp.id, status: 3, pass_reason: this.temp.pass_reason }
          if (this.temp.reason !== '') {
            costApplyAudit(obj).then((response) => {
              if (response.meta.status === 200) {
                this.$notify({
                  title: 'Success',
                  message: '驳回成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.getList()
              }
            })
          }
        }
      })
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = [
          '序号',
          '申请人',
          '申请部门',
          '项目名称',
          '付款日期',
          '付款截至日期',
          '费用类型',
          '费用金额',
          '事由',
          '收款人名称',
          '收款人账号',
          '收款人开户行',
          '添加附件',
          '部门审核人',
          '部门审核状态',
          '财务审核人',
          '财务审核状态',
          '经理审核人',
          '经理审核状态',
          '驳回原因'
        ]
        const filterVal = [
          'expense_examiner',
          'dept_name',
          'pro_name',
          'start_time',
          'end_time',
          'category_name',
          'money',
          'reason',
          'pay_name',
          'bank_accounts',
          'bank_name',
          'cost_proof',
          'expense_examiner',
          'finance_check',
          'finance_check_user',
          'finance_check_status',
          'manager_check',
          'manager_status',
          'pass_reason'
        ]

        let explode_data = []
        this.listQuery.page_size = 1000
        await costApplyList(this.listQuery).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data
            this.listQuery.page_size = 10
          }
        })
        const data = this.formatJson(explode_data.data, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '付款申请台账'
        })
        this.downloadLoading = false
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    }
  }
}
</script>
<style scoped>
.el-image-viewer__actions__divider {
  width: 3vw;
  height: 3vw;
}

.filter-container .filter-item {
  margin-bottom: 0px;
  margin-left: 10px;
}

.prove {
  display: flex;
  flex-wrap: wrap;
  margin: 10px;
}

.voucher {
  margin-right: 5px;
}
</style>
