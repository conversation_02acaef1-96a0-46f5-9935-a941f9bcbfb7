<template>
  <div class="app-container">
    <div class="filter-container" style="position: relative">
      <div style="display: inline-block">
        <el-date-picker v-model="searchTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="daterange"
                        clearable range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        @change="handleFilter"/>
      </div>
      <el-select v-model="listQuery.project_id" placeholder="项目名称" filterable clearable style="width: 200px"
                 class="filter-item" @change="handleFilter">
        <el-option v-for="(item, iex) in importanceOptions3" :key="iex" :label="item.pro_name" :value="item.id"/>
      </el-select>
      <el-select v-model="receiveId" placeholder="申请人" clearable multiple filterable style="width: 200px"
                 class="filter-item" @change="handleFilter">
        <el-option v-for="(item, inx) in importanceOptions4" :key="inx" :label="item.user_username"
                   :value="item.id"/>
      </el-select>
      <el-select v-model="listQuery.state" placeholder="部门审核" clearable style="width: 150px" class="filter-item"
                 @change="handleFilter">
        <el-option v-for="(item, ind) in importanceOptions2" :key="ind" :label="item.status" :value="item.id"/>
      </el-select>
      <el-select v-model="listQuery.finance_check" placeholder="财务审核" clearable style="width: 150px"
                 class="filter-item" @change="handleFilter">
        <el-option v-for="(item, ind) in importanceOptions2" :key="ind" :label="item.status" :value="item.id"/>
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download"
                 @click="handleDownload">
        导出
      </el-button>
    </div>
    <el-table v-loading="loading" :data="skuListInfo" :span-method="objectSpanMethod" border>
      <el-table-column width="60px" prop="id" label="序号">
      </el-table-column>
      <el-table-column width="115px" prop="project" label="项目名称">
      </el-table-column>
      <el-table-column width="94px" prop="created_at" label="申请时间">
      </el-table-column>
      <el-table-column width="66px" prop="create_by" label="申请人">
      </el-table-column>
      <el-table-column prop="start_time" label="开始时间">
      </el-table-column>
      <el-table-column prop="end_time" label="结束时间">
      </el-table-column>
      <el-table-column prop="expense_type" label="报销类型">
      </el-table-column>
      <el-table-column :show-overflow-tooltip='true' prop="expense_detail" label="报销明细">
      </el-table-column>
      <el-table-column prop="expense_money" label="金额">
      </el-table-column>
      <el-table-column prop="money" label="总金额">
      </el-table-column>
      <el-table-column width="190px" label="附件">
        <template slot-scope="{ row }">
          <div v-if="row.expense_photo.length > 0" style="display:flex; flex-wrap:wrap; ">
            <div v-for="(item, index) in row.expense_photo" :key="index">
              <el-image :src="item" style="width: 50px; height: 50px; margin: 2px; "
                        scroll-container="scrollContainer" :preview-src-list="row.expense_photo"
                        @click="big_image(item)" alt=""/>
            </div>
          </div>
          <div v-if="row.expense_proof.length > 0" style="display: flex;justify-content: space-around;">
            <div v-for="(file_item, filedex) in row.expense_proof" :key="filedex">
              <i class="el-icon-document-remove"/> <a :href="file_item" title="点击预览打印"
                                                      target="_blank">报销凭证</a>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="expense_examiner" label="部门审核人">
      </el-table-column>
      <el-table-column prop="state" label="部门审核">
      </el-table-column>
      <el-table-column prop="finance_check" label="财务审核">
      </el-table-column>
      <el-table-column prop="remarks" label="驳回原因">
      </el-table-column>

      <el-table-column width="207px" label="操作">
        <template slot-scope="{ row }">
          <div>
            <div style="margin-bottom: 5px;" v-if="role == 2 ">
              <el-button :disabled="!(row.state === '待审核')" type="primary" size="mini"
                         @click="departformal(row)">
                部门通过
              </el-button>
              <el-button :disabled="!(row.state === '待审核')" size="mini" type="danger"
                         @click="departUpdate(row)">
                部门驳回
              </el-button>
            </div>
            <div v-if="role == 5 ">
              <el-button :disabled="!(row.state === '已通过' && row.finance_check === '待审核')" type="primary"
                         size="mini" @click="checkInformal(row)">
                财务通过
              </el-button>
              <el-button :disabled="!(row.state === '已通过' && row.finance_check === '待审核')" size="mini"
                         type="danger" @click="handleUpdate(row)">
                财务驳回
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.page_size"
                @pagination="getList"/>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="70px"
               style="width: 400px; margin-left: 50px">
        <el-form-item label="驳回原因">
          <el-input v-model="temp.reason" props="reason" style="margin-top: 15px" type="textarea"
                    placeholder="请输入驳回意见"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消</el-button>
        <el-button type="primary" @click="updateData()">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getALLPro, searchCompany} from "@/api/porject/project";
import {getAllUserInfo} from "@/api/user";
import {getReimbursementType, getReimbursementList, checkInformal} from "@/api/finance";
import Pagination from '@/components/Pagination'

export default {
  components: {Pagination},
  data() {
    return {
      role: localStorage.getItem('role'),
      header_list: [],
      receiveId: [],
      scrollContainer: HTMLCollection,
      srcList: [' '],
      tableKey: 0,
      list: [],
      multipleSelection: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        expense_type: undefined,
        company_id: undefined,
        project_id: undefined,
        create_by: undefined,
        state: undefined,
        pro_end_time: undefined,
        finance_check: undefined,
        sort: undefined
      },
      searchTime: null,
      listData: {
        page: 1,
        page_size: 20
      },
      show_pop: false,
      tables: [],
      importanceOptions: [],
      importanceOptions1: [],
      importanceOptions2: [
        {
          status: '待审核',
          id: '1'
        },
        {
          status: '已通过',
          id: '2'
        },
        {
          status: '拒绝',
          id: '3'
        }
      ],
      importanceOptions3: [],
      importanceOptions4: [],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        type: '',
        reason: '',
        status: 'published'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        examine: '审核驳回'
      },
      dialogPvVisible: false,
      pvData: [],
      rules: {
        type: [
          {required: true, message: 'type is required', trigger: 'change'}
        ],
        timestamp: [
          {
            type: 'date',
            required: true,
            message: 'timestamp is required',
            trigger: 'change'
          }
        ],
        reason: [
          {required: true, message: '请添加驳回原因', trigger: 'blur'}
        ]
      },
      downloadLoading: false,
      numId: 0,
      skuListInfo: [],
      typeNameArr: [],
      typeNamePos: 0,
      storeArr: [],
      storePos: 0,
      feeArr: [],
      feePos: 0,
      moneyArr: [],
      moneyPos: 0,
      departmentAll: [],
      departmentPos: 0,
      department_All: [],
      department_Pos: 0,
      stateArr: [],
      statePos: 0,
      remarksArr: [],
      remarksPos: 0,
      operateArr: [],
      operatePos: 0,
      pdf_imgArr: [],
      pdf_imgPos: 0,
      createArr: [],
      createPos: 0,
      financeArr: [],
      financePos: 0,
      loading: false
    }
  },
  created() {
    this.searchList()
    this.getList()
  },
  methods: {
    departformal(e) {
      this.$confirm('是否确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        checkInformal({id: e.expense_id, state: 2, type: 'dept'}).then(response => {
        })
        this.$message({
          type: 'success',
          message: '审核通过!'
        })
        this.getList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消审核'
        })
      })
    },
    departUpdate(e) {
      console.log(e)
      this.temp = Object.assign({}, e)
      this.dialogStatus = 'examine'
      this.dialogFormVisible = true
      this.typeName = 'dept'
    },
    big_image(item) {
      this.srcList[0] = item
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(async (excel) => {
        const tHeader = [
          '序号',
          '项目名称',
          '申请时间',
          '报销人',
          '开始时间',
          '结束时间',
          '报销明细',
          '报销类型',
          '报销金额',
          '总金额',
          '驳回原因',
          '部门审核',
          '财务审核'
        ]
        const filterVal = [
          'id',
          'project',
          'created_at',
          'create_by',
          'start_time',
          'end_time',
          'expense_detail',
          'expense_type',
          'expense_money',
          'money',
          'remarks',
          'state',
          'finance_check'
        ]

        const explode_data = []
        this.listQuery.page_size = 10000
        await getReimbursementList(this.listQuery).then((response) => {

          if (response.meta.status === 200) {

            response.data.data.forEach((item, index) => {
              item.id = index + 1
              this.listQuery.page_size = 10

              // 空列表
              if (item.list.length === 0) {
                explode_data.push(item)

              } else {
                // 处理数据格式
                item.start_time = item.list[0].start_time.slice(0, 10)
                item.end_time = item.list[0].end_time.slice(0, 10)
                item.expense_detail = item.list[0].expense_detail
                item.expense_type = item.list[0].expense_type
                item.expense_photo = !item.expense_photo ? [] : item.expense_photo.split(',')
                item.expense_proof = !item.expense_proof ? [] : item.expense_proof.split(',')

                // 将子表数据提出到父集合
                item.list.forEach((itm) => {
                  itm.end_time = item.end_time.slice(0, 10)
                  itm.start_time = item.start_time.slice(0, 10)
                  itm.created_at = itm.created_at.slice(0, 10)

                  const id = item.id
                  item = Object.assign({}, item, itm)
                  item.id = id
                  explode_data.push(item)
                })
              }


              this.listQuery.page_size = 10
            })
          }
        })

        const data = this.formatJson(explode_data, filterVal)
        // 导出添加序号
        // data.forEach((v, k) => {
        //   this.numId = k + 1
        //   v.forEach((kv, kk) => {
        //     if (kk === 0) {
        //       v.unshift(this.numId)
        //     }
        //   })
        // })

        // 合并单元格
        const merges = this.mergesCells(explode_data)
        // 导出数据
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '报销台账',
          merges
        })
        this.downloadLoading = false
      })
    },
    // 处理转化数据
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    // 处理转化数据
    mergesCells(explode_data) {
      this.merage()
      const arr = [];
      this.typeNameArr.forEach((v, k) => {
        // 第一行数据
        if (k === 0) {
          v = v + 1
          arr.push(v)
        } else {
          const index = arr.length - 1
          if (v != 0) {
            arr.push(arr[index] + v)
          }
        }
      })

      const start = []
      const end = []
      const AZ = ['A', 'B', 'C', 'D', 'J', 'K', 'L', 'M']
      AZ.forEach((az_item) => {
        arr.forEach((item) => {
          item = az_item + item
          end.push(item)
        })
      })
      arr.forEach((item) => {
        start.push(item)
      })
      const custer_arr = []
      for (var i = 0; i < start.length; i++) {
        if (i == 0) {
          custer_arr.push(2)
        } else {
          custer_arr.push(start[i - 1] + 1)
        }
      }
      const allArr = []
      AZ.forEach((az_item) => {
        custer_arr.forEach((item) => {
          item = az_item + item
          allArr.push(item)
        })
      })
      const finish = []
      allArr.forEach((item, index) => {
        end.forEach((itm, idx) => {
          if (itm != item && index == idx) {
            var data = item + ':' + itm
            finish.push(data)
          }
        })
      })
      return finish


    },
    // 审核
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const obj = {id: this.temp.expense_id, state: 3, type: this.typeName, remarks: this.temp.reason}
          if (this.temp.reason !== '') {
            checkInformal(obj).then((response) => {
              if (response.meta.status === 200) {
                this.$notify({
                  title: 'Success',
                  message: '驳回成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.getList()
              }
            })
          }
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.timestamp = new Date(this.temp.timestamp)
      this.dialogStatus = 'examine'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.typeName = 'finance'
    },
    checkInformal(row) {
      this.$confirm('是否确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        checkInformal({id: row.expense_id, state: 2, type: 'finance'}).then(response => {
        })
        this.$message({
          type: 'success',
          message: '审核通过!'
        })
        this.getList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消审核'
        })
      })
    },
    // 获取报销接口数据
    getList() {
      this.loading = true

      if (this.searchTime) {
        this.listQuery.pro_end_time =
          this.searchTime[0] + '——' + this.searchTime[1]
      }
      if (this.receiveId) {
        this.listQuery.create_by = this.receiveId.join(',')
      }
      getReimbursementList(this.listQuery).then((response) => {
        if (response.data.total == 0) {
          this.skuListInfo = []
        } else {
          response.data.data.forEach((item, index) => {
            if (this.listQuery.page == 1) {
              item.id = index + 1
            }
            if (this.listQuery.page != 1) {
              item.id = (this.listQuery.page * this.listQuery.page_size) - 9 + index
            }
            if (item.list.length != 0) {
              item.start_time = item.list[0].start_time.substring(0, 11)
              item.end_time = item.list[0].end_time.substring(0, 11)
              item.expense_detail = item.list[0].expense_detail
              item.expense_type = item.list[0].expense_type
            }
            if (item.expense_photo === '') {
              item.expense_photo = []
            } else {
              item.expense_photo = item.expense_photo.split(',')
            }
            if (item.expense_proof === '') {
              item.expense_proof = []
            } else {
              item.expense_proof = item.expense_proof.split(',')
            }
            this.header_list.push(item.list)
          })
          this.list = response.data.data
          this.total = response.data.total
          var arr = []
          this.list.forEach((item) => {
            if (item.list.length == 0) {
              arr.push(item)
            } else {
              item.list.forEach((itm) => {
                const aa = item.id
                item = Object.assign({}, item, itm);
                item.id = aa
                item.start_time = item.start_time.substring(0, 10)
                item.end_time = item.end_time.substring(0, 10)
                item.created_at = item.created_at.slice(0, 10)
                arr.push(item)
              })
            }
          })
          if (this.listQuery.page <= response.data.pages) {
            this.skuListInfo = arr
            this.merage()
          }
        }
        setTimeout(() => {
          this.listLoading = false
          this.loading = false
        }, 100)
      })
    },
    merageInit() {
      this.typeNameArr = []
      this.typeNamePos = 0
      this.storeArr = []
      this.storePos = 0
      this.feeArr = []
      this.feePos = 0
      this.moneyArr = []
      this.moneyPos = 0
      this.departmentAll = []
      this.departmentPos = 0
      this.department_All = []
      this.department_Pos = 0
      this.stateArr = [],
        this.statePos = 0,
        this.remarksArr = [],
        this.remarksPos = 0,
        this.operateArr = [],
        this.operatePos = 0,
        this.pdf_imgArr = [],
        this.pdf_imgPos = 0,
        this.createArr = [],
        this.createPos = 0,
        this.financeArr = [],
        this.financePos = 0
    },
    merage() {
      this.merageInit()
      for (let i = 0; i < this.skuListInfo.length; i += 1) {
        if (i === 0) {
          // 第一行必须存在
          this.typeNameArr.push(1)
          this.typeNamePos = 0
          this.storeArr.push(1)
          this.storePos = 0
          this.feeArr.push(1)
          this.feePos = 0
          this.moneyArr.push(1)
          this.moneyPos = 0
          this.departmentAll.push(1)
          this.departmentPos = 0
          this.department_All.push(1)
          this.department_Pos = 0
          this.stateArr.push(1)
          this.statePos = 0,
            this.remarksArr.push(1)
          this.remarksPos = 0,
            this.operateArr.push(1)
          this.operatePos = 0
          this.pdf_imgArr.push(1)
          this.pdf_imgPos = 0
          this.createArr.push(1)
          this.createPos = 0
          this.financeArr.push(1),
            this.financePos = 0
        } else {
          // 判断当前元素与上一个元素是否相同,eg：this.typeNamePos 是 this.typeNameArr序号
          // 第一列
          if (this.skuListInfo[i].id === this.skuListInfo[i - 1].id) {
            this.typeNameArr[this.typeNamePos] += 1
            this.typeNameArr.push(0)
          } else {
            this.typeNameArr.push(1)
            this.typeNamePos = i
          }

          // 第二列
          if (this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.storeArr[this.storePos] += 1
            this.storeArr.push(0)
          } else {
            this.storeArr.push(1)
            this.storePos = i
          }
          // 第三列
          if (this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.createArr[this.createPos] += 1
            this.createArr.push(0)
          } else {
            this.createArr.push(1)
            this.createPos = i
          }
          //第四列
          if (this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.feeArr[this.feePos] += 1
            this.feeArr.push(0)
          } else {
            this.feeArr.push(1)
            this.feePos = i
          }
          // 第8列
          // if (this.skuListInfo[i].money === this.skuListInfo[i - 1].money && this.skuListInfo[i].create_by
          //   === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].peoject
          //   === this.skuListInfo[i - 1].peoject && this.skuListInfo[i].id ===
          //   this.skuListInfo[i - 1].id) {
          //   this.moneyArr[this.moneyPos] += 1
          //   this.moneyArr.push(0)
          // } else {
          //   this.moneyArr.push(1)
          //   this.moneyPos = i
          // }
          // 第9列
          if (this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.departmentAll[this.departmentPos] += 1
            this.departmentAll.push(0)
          } else {
            this.departmentAll.push(1)
            this.departmentPos = i
          }
          // 第10列
          if (this.skuListInfo[i].finance_check === this.skuListInfo[i - 1].finance_check &&
            this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.department_All[this.department_Pos] += 1
            this.department_All.push(0)
          } else {
            this.department_All.push(1)
            this.department_Pos = i
          }
          // 第11列
          if (this.skuListInfo[i].state === this.skuListInfo[i - 1].state &&
            this.skuListInfo[i].finance_check === this.skuListInfo[i - 1].finance_check &&
            this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.stateArr[this.statePos] += 1
            this.stateArr.push(0)
          } else {
            this.stateArr.push(1)
            this.statePos = i
          }
          // 第12列
          if (this.skuListInfo[i].remarks === this.skuListInfo[i - 1].remarks &&
            this.skuListInfo[i].state === this.skuListInfo[i - 1].state &&
            this.skuListInfo[i].finance_check === this.skuListInfo[i - 1].finance_check &&
            this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.remarksArr[this.remarksPos] += 1
            this.remarksArr.push(0)
          } else {
            this.remarksArr.push(1)
            this.remarksPos = i
          }
          // 第13列
          if (this.skuListInfo[i].id === this.skuListInfo[i - 1].id &&
            this.skuListInfo[i].remarks === this.skuListInfo[i - 1].remarks &&
            this.skuListInfo[i].state === this.skuListInfo[i - 1].state &&
            this.skuListInfo[i].finance_check === this.skuListInfo[i - 1].finance_check &&
            this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.operateArr[this.operatePos] += 1
            this.operateArr.push(0)
          } else {
            this.operateArr.push(1)
            this.operatePos = i
          }
          // 第14列
          if (this.skuListInfo[i].expense_proof === this.skuListInfo[i - 1].expense_proof &&
            this.skuListInfo[i].id === this.skuListInfo[i - 1].id &&
            this.skuListInfo[i].remarks === this.skuListInfo[i - 1].remarks &&
            this.skuListInfo[i].state === this.skuListInfo[i - 1].state &&
            this.skuListInfo[i].finance_check === this.skuListInfo[i - 1].finance_check &&
            this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.pdf_imgArr[this.pdf_imgPos] += 1
            this.pdf_imgArr.push(0)
          } else {
            this.pdf_imgArr.push(1)
            this.pdf_imgPos = i
          }
          if (this.skuListInfo[i].expense_proof === this.skuListInfo[i - 1].expense_proof &&
            this.skuListInfo[i].id === this.skuListInfo[i - 1].id &&
            this.skuListInfo[i].remarks === this.skuListInfo[i - 1].remarks &&
            this.skuListInfo[i].state === this.skuListInfo[i - 1].state &&
            this.skuListInfo[i].finance_check === this.skuListInfo[i - 1].finance_check &&
            this.skuListInfo[i].expense_examiner === this.skuListInfo[i - 1].expense_examiner &&
            this.skuListInfo[i].create_by === this.skuListInfo[i - 1].create_by && this.skuListInfo[i].created_at === this.skuListInfo[i - 1].created_at && this.skuListInfo[i].project === this.skuListInfo[i - 1].project && this.skuListInfo[i].id ===
            this.skuListInfo[i - 1].id) {
            this.financeArr[this.financePos] += 1
            this.financeArr.push(0)
          } else {
            this.financeArr.push(1)
            this.financePos = i
          }
        }
      }
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) {
        // 第一列的合并方法
        const row1 = this.typeNameArr[rowIndex]
        const col1 = row1 > 0 ? 1 : 0; // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row1,
          colspan: col1
        };
      } else if (columnIndex === 1) {
        // 第二列的合并方法
        const row2 = this.storeArr[rowIndex]
        const col2 = row2 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row2,
          colspan: col2
        };
      } else if (columnIndex === 2) {
        // 第8列的合并方法
        const row8 = this.createArr[rowIndex]
        const col8 = row8 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row8,
          colspan: col8
        }
      } else if (columnIndex === 3) {
        // 第三列的合并方法
        const row3 = this.feeArr[rowIndex]
        const col3 = row3 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row3,
          colspan: col3
        }
      } else if (columnIndex === 9) {
        // 第9列的合并方法
        const row9 = this.departmentAll[rowIndex]
        const col9 = row9 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row9,
          colspan: col9
        }
      } else if (columnIndex === 10) {
        // 第10列的合并方法
        const row10 = this.department_All[rowIndex]
        const col10 = row10 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row10,
          colspan: col10
        }
      } else if (columnIndex === 11) {
        // 第11列的合并方法
        const row11 = this.stateArr[rowIndex]
        const col11 = row11 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row11,
          colspan: col11
        }
      } else if (columnIndex === 12) {
        // 第12列的合并方法
        const row12 = this.remarksArr[rowIndex]
        const col12 = row12 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row12,
          colspan: col12
        }
      } else if (columnIndex === 13) {
        // 第13列的合并方法
        const row13 = this.operateArr[rowIndex]
        const col13 = row13 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需s要取消
        return {
          rowspan: row13,
          colspan: col13
        }
      } else if (columnIndex === 14) {
        // 第14列的合并方法
        const row14 = this.pdf_imgArr[rowIndex]
        const col14 = row14 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row14,
          colspan: col14
        }
      } else if (columnIndex === 15) {
        // 第14列的合并方法
        const row14 = this.financeArr[rowIndex]
        const col14 = row14 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row14,
          colspan: col14
        }
      }

    },
    searchList() {
      searchCompany(this.listData).then((response) => {
        this.importanceOptions = response.data.data
      })
      getALLPro().then(response => {
        this.importanceOptions3 = response.data
      })
      getAllUserInfo().then((response) => {
        this.importanceOptions4 = response.data
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      if (this.searchTime == null) {
        this.listQuery.pro_end_time = ''
      }
    }
  }
}
</script>
<style scoped>
.many_money {
  color: crimson;
}

.filter-container .filter-item {
  margin-bottom: 0;
  margin-left: 10px;
}

.prove {
  display: flex;
  flex-wrap: wrap;
  margin: 10px;
}

.voucher {
  margin-right: 5px;
}
</style>
