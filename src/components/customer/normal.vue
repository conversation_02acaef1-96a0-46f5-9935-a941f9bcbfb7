<template>
  <div>
    <div>
      <el-input v-model="listQuery.customer_name" clearable style="width: 200px;margin-right: 20px" placeholder="按名称搜索" @keyup.enter.native="handleFilter" />
      <el-button type="primary" @click="handleFilter">
        搜索
      </el-button>
      <el-button  @click="upload"  class="filter-item" type="primary" icon="el-icon-download">
         下载模板
      </el-button>
      <el-button   :loading="downloadLoading" class="filter-item" type="primary" @click="open">
        <svg-icon icon-class="guide" />
        导入
      </el-button>
      <el-button type="primary" style="margin:10px" @click="handleCreate()">新增</el-button>
      <el-button :loading="loading" class="filter-item" type="primary" icon="el-icon-download" @click="handleDownload">
        导出
      </el-button>
    </div>
    <div v-if="popshow" style="  position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 99;">
      <div class="pop-bg"
        style="position: absolute;top: 0;right: 0;bottom: 0;left: 0;background: rgba(0, 0, 0, 0.7);" />
      <div class="pop-text"
        style="position: absolute;width: 600px;height: 500px;top: 50%;left: 50%;padding: 20px;transform: translate(-50%, -50%);background: #fff;z-index: 9;box-sizing: border-box;">
        <h2 class="user-permission"
          style="margin-top:1px;color:#333;margin-bottom: 30px;color: #666;font-size: 16px;font-weight: bold;text-align:center;">
          用户导入</h2>
        <p class="close"
          style="position: absolute;top: 0;right: 25px;color: #a6a6a6;font-size: 25px;cursor: pointer;margin-top: 13px;"
          @click="closePop">×</p>

        <el-upload style="text-align:center;" class="upload-demo" drag action="fakeaction"
          :http-request="uploadSectionFile" multiple>
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">只能上传excel表格，且不超过1000kb</div>
        </el-upload>
      </div>
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%;margin: auto"
        border
      >
        <el-table-column
          label="序号"
          type="index"
          :index="indexAdd"
          sortable="custom"
          align="center"
          width="80"
        />
        <el-table-column prop="date" label="客户名称">
          <template slot-scope="{row}">
            <span>{{ row.customer_name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="客户等级">
          <template slot-scope="{row}">
            <span>{{ row.customer_level }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="回款周期">
          <template slot-scope="{row}">
            <span>{{ row.payment_period }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="联系人">
          <template slot-scope="{row}">
            <span>{{ row.contact }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="联系电话" width="120px">
          <template slot-scope="{row}">
            <span>{{ row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="公司邮箱" width="160px">
          <template slot-scope="{row}">
            <span>{{ row.email }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name"   :show-overflow-tooltip='true'  label="主营类目">
          <template slot-scope="{row}">
            <span>{{ row.categories }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="经营地址">
          <template slot-scope="{row}">
            <span>{{ row.address }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="社会信用代码" width="140px">
          <template slot-scope="{row}">
            <span>{{ row.social_code }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="注册电话" width="140px">
          <template slot-scope="{row}">
            <span>{{ row.register_tel }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="注册地址">
          <template slot-scope="{row}">
            <span>{{ row.register_add }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="开户行名称">
          <template slot-scope="{row}">
            <span>{{ row.bank_name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="银行账号">
          <template slot-scope="{row}">
            <span>{{ row.bank_account }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('table.actions')"
          align="center"
          width="200px"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
            <!--            <el-button type="danger" size="mini" @click="handleDelete(row)">-->
            <!--              禁用-->
            <!--            </el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.page_size"
        @pagination="getList"
      />
      <!-- 新增编辑列表 -->
      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="temp"
          :inline="true"
          size="medium"
          label-width="200px"
        >
          <el-form-item label="客户名称" prop="customer_name">
            <el-input v-model="temp.customer_name" placeholder="输入客户名称" />
          </el-form-item>

          <el-form-item label="客户等级" prop="customer_level">
            <el-select
              v-model="temp.customer_level"
              placeholder="请选择等级"
              clearable
              style="width: 190px"
              class="filter-item"
            >
              <el-option
                v-for="item in grade"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="回款周期" prop="payment_period">
            <el-select
              v-model="temp.payment_period"
              placeholder="请选择回款周期"
              clearable
              style="width: 190px"
              class="filter-item"
            >
              <el-option
                v-for="item in cycle"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="信用代码" prop="social_code">
            <el-input v-model="temp.social_code" placeholder="请输入信用代码" />
          </el-form-item>

          <el-form-item label="注册电话" prop="register_tel">
            <el-input v-model="temp.register_tel"   oninput="this.value=this.value.replace(^(\(\d{3,4}-)|\d{3.4}-)?\d{7,8}$)" placeholder="输入注册电话" />
          </el-form-item>

          <el-form-item label="注册地址" prop="register_add">
            <el-input v-model="temp.register_add" placeholder="注册地址" />
          </el-form-item>

          <el-form-item label="开户行名" prop="bank_name">
            <el-input v-model="temp.bank_name" placeholder="输入开户行名" />
          </el-form-item>

          <el-form-item label="银行账户" prop="bank_account">
            <el-input v-model="temp.bank_account" placeholder="输入银行账户" />
          </el-form-item>

          <el-form-item label="联系人" prop="contact">
            <el-input v-model="temp.contact" placeholder="输入联系人" />
          </el-form-item>

          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="temp.phone" type="number" placeholder="输入联系电话" />
          </el-form-item>

          <el-form-item label="公司邮箱" prop="email">
            <el-input v-model="temp.email" placeholder="输入公司邮箱" />
          </el-form-item>

          <el-form-item label="经营地址" prop="address">
            <el-input v-model="temp.address" placeholder="输入经营地址" />
          </el-form-item>

          <el-form-item label="主营类目" prop="categories">
            <el-input v-model="temp.categories" type="textarea" style="width: 31vw" placeholder="输入主营类目" />
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            提交
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { CustomerList, CustomerAdd, CustomerEdit, importCustomer, CustomerDel, customerLevel } from '@/api/customer'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'
export default {
  name: 'Customer',
  components: {
    Pagination
  },
  data() {
    return {
      downloadLoading:false,
      tableData: [],
      grade: [],
      cycle: [
        {
          value: 1,
          label: '短'
        },
        {
          value: 2,
          label: '中'
        },
        {
          value: 3,
          label: '长'
        }
      ],
      total: 0,
      loading: false,
      listQuery: {
        page: 1,
        page_size: 10,
        type: 1,
        customer_name: ''
      },
      dialogVisible: false,
      textMap: {
        create: '新增客户',
        update: '编辑客户'
      },
      dialogStatus: '',
      rules: {
        customer_name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        // social_code: [{ required: true, message: '请输入社会信用代码', trigger: 'blur' }],
        // register_tel: [{ required: true, message: '请输入注册电话', trigger: 'blur' }],
        // register_add: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
        // bank_name: [{ required: true, message: '请填入开户行名称', trigger: 'blur' }],
        // bank_account: [{ required: true, message: '请输入银行账户', trigger: 'blur' }]
      },
      temp: {
        customer_name: '',
        customer_num: '',
        contact: '',
        phone: '',
        email: '',
        address: '',
        social_code: '',
        type: 1,
        register_tel: '',
        bank_name: '',
        bank_account: '',
        register_add: '',
        categories: '',
        customer_level: '',
        payment_period: ''
      },
      show_pop: false,
      popshow:false
    }
  },
  created() {
    this.getList()
    this.customerLevel()
  },
  methods: {
    upload() {
       this.flag('customer')
    },
    uploadSectionFile(params) {
      const file = params.file
      const form = new FormData()
      // 文件对象
      form.append('excel_file', file)
      // form.type = 1
      form.append('type', 1)
      importCustomer(form)
        .then(res => {
          if (res.meta.status === 200) {
            this.popshow = false
            this.getList()
          } else {
            // xxx
          }
        })
        .catch(() => {
          // xxx
        })
    },
    open() {
      this.popshow = true
    },
    closePop() {
      this.popshow = false
    },
    indexAdd(index) {
      const page = this.listQuery.page // 当前页码
      const pagesize = this.listQuery.page_size // 每页条数
      return index + 1 + (page - 1) * pagesize
    },
    getList() {
      this.loading = true
      CustomerList(this.listQuery).then(res => {
        this.tableData = res.data.data
        this.total = res.data.total
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    // 重置
    resetTemp() {
      this.temp = {
        customer_name: '',
        customer_num: '',
        contact: '',
        phone: '',
        email: '',
        address: '',
        social_code: '',
        register_tel: '',
        bank_name: '',
        bank_account: '',
        register_add: '',
        categories: '',
        customer_level: '',
        payment_period: ''
      }
    },
    // 删除
    handleDelete(row) {
      this.$confirm('是否禁用改客户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        CustomerDel({ id: row.id, status: 2 }).then(res => {
          this.getList()
        })
        this.$message({
          type: 'success',
          message: '禁用成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消禁用'
        })
      })
    },
    // 获取客户等级
    customerLevel() {
      customerLevel().then(res => {
        this.grade = res.data
      })
    },
    // 新增
    handleCreate() {
      this.dialogVisible = true
      this.dialogStatus = 'create'
      this.resetTemp()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, handleMerge() {
      this.show_pop = true
    },
    createData() {
      this.listLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          CustomerAdd(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            setTimeout(() => {
              this.listLoading = false
            }, 500)
          })
        }
      })
      this.getList()
    },
    // 编辑
    handleUpdate(row) {
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.temp.payment_period = row.payment_period_id
      this.temp.customer_level = row.customer_level_id
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          CustomerEdit(this.temp).then(response => {
            this.dialogVisible = false
            this.getList()
            this.$notify({
              title: 'Success',
              message: '提交成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    // 导出
    handleDownload() {
      this.loading = true
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = [
          '序号',
          '客户名称',
          '客户等级',
          '回款周期',
          '联系人',
          '手机',
          '邮箱',
          '主营类目',
          '经营地址',
          '社会信用代码',
          '注册电话',
          '注册地址',
          '开户行名称',
          '银行账户'
        ]
        const filterVal = [
          'customer_name',
          'customer_level',
          'payment_period',
          'contact',
          'phone',
          'email',
          'categories',
          'address',
          'social_code',
          'register_tel',
          'register_add',
          'bank_name',
          'bank_account'
        ]
        let explode_data = []
        this.listQuery.page_size = 10000
        await CustomerList(this.listQuery).then((response) => {
          if (response.meta.status === 200) {
            explode_data = response.data.data
            this.listQuery.page_size = 10
          }
        })

        const data = this.formatJson(explode_data, filterVal)
        data.forEach((v, k) => {
          this.numId = k + 1
          v.forEach((kv, kk) => {
            if (kk === 0) {
              v.unshift(this.numId)
            }
          })
        })
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '客户管理台账'
        })
        setTimeout(() => {
          this.loading = false
        }, 100)
      })
    },
    formatJson(explode_data, filterVal) {
      return explode_data.map((v) =>
        filterVal.map((j) => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        })
      )
    }
  }
}
</script>
