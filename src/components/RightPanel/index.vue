<template>
  <div class="">
    <div ref="rightPanel" :class="{ show: show }" class="rightPanel-container">
      <div class="rightPanel">
        <div class="handle-button" :style="{ 'top': buttonTop + 'px', 'background-color': theme }"
             @click="show = !show">
          <i :class="show ? 'el-icon-close' : 'el-icon-bell'" @click="handleDisappear" />

          <div v-if="spot == 1" class="spots"></div>

        </div>
        <div class="rightPanel-items">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { addClass, removeClass } from '@/utils'

export default {
  name: 'RightPanel',
  props: {
    clickNotClose: {
      default: false,
      type: Boolean
    },
    buttonTop: {
      default: 940,
      type: Number
    }
  },
  data() {
    return {
      show: false,
      onShow: true,
      num: 0,
      spot: 0
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  watch: {
    show(value) {
      if (value && !this.clickNotClose) {
        this.addEventClick()
      }
      if (value) {
        addClass(document.body, 'showRightPanel')
      } else {
        removeClass(document.body, 'showRightPanel')
      }
    },
    spot(){
    }
  },
  created() {
    this.handleNum()
  },
  mounted() {
    this.insertToBody()
  },
  beforeDestroy() {
    const elx = this.$refs.rightPanel
    elx.remove()
  },
  methods: {
    handleNum() {
      this.$socketPublic.onmessage = function (evt) {
        var type = JSON.parse(evt.data)
        var aa = 'msg'
        if (type.cmd == aa) {
          this.spot = 1
        }
      }
    },
    addEventClick() {
      window.addEventListener('click', this.closeSidebar)
    },
    closeSidebar(evt) {
      const parent = evt.target.closest('.rightPanel')
      if (!parent) {
        this.show = false
        window.removeEventListener('click', this.closeSidebar)
      }
    },
    insertToBody() {
      const elx = this.$refs.rightPanel
      const body = document.querySelector('body')
      body.insertBefore(elx, body.firstChild)
    },
    handleDisappear() {
      this.onShow = false
      this.spot = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.pieces {
  width: 100%;
  height: 100%;
  padding: 10px;

  .header {
    border-bottom: 1px solid #f1f1f1;
    padding: 10px 5px;

    .title {
      font-weight: bold;
    }

    .title-content {
      margin-left: 20px;
      font-weight: bold;
    }
  }

  .wrap {
    margin-top: 10px;

    .other {
      padding: 5px;

      .title-content {
        margin-left: 20px;
      }
    }
  }
}

.showRightPanel {
  overflow: hidden;
  position: relative;
  width: calc(100% - 15px);
}
</style>

<style lang="scss" scoped>
.rightPanel-background {
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
  background: rgba(0, 0, 0, .2);
  z-index: -1;
}

.rightPanel {
  width: 100%;
  max-width: 350px;
  position: fixed;
  top: -49px;
  right: 0;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);
  transition: all .25s cubic-bezier(.7, .3, .1, 1);
  transform: translate(100%);
  background: #fff;
  z-index: 40000;
}

.show {
  transition: all .3s cubic-bezier(.7, .3, .1, 1);

  .rightPanel-background {
    z-index: 20000;
    opacity: 1;
    width: 100%;
    height: 100%;
  }

  .rightPanel {
    transform: translate(0);
  }
}

.handle-button {
  width: 48px;
  height: 48px;
  left: -49px;
  text-align: center;
  font-size: 24px;
  border-radius: 6px 0 0 6px !important;
  z-index: 0;
  pointer-events: auto;
  cursor: pointer;
  color: #fff;
  line-height: 48px;
  position: relative;

  i {
    font-size: 24px;
    line-height: 48px;
  }
}

.spots {
  position: absolute;
  top: 2px;
  right: 5px;
  width: 10px;
  height: 10px;
  text-align: center;
  background: red;
  border-radius: 50%;
}
</style>
