import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'
import 'element-ui/lib/theme-chalk/index.css'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import i18n from './lang' // internationalization
import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import '@/assets/theme.less'
import '@/assets/global.css'
import '@/assets/iconfont/iconfont.css'

import Ellipsis from '@/components/common/Ellipsis'
import WDialog from '@/components/common/WDialog'
import Tip from '@/components/common/Tip'

Vue.use(Ellipsis)
Vue.use(WDialog)
Vue.use(Tip)

import * as filters from './filters' // global filters
// 引入echarts
import echarts from 'echarts'

Vue.prototype.$echarts = echarts

// import UploadImg from '@/components/uploadImg'
// Vue.component('UploadImg', UploadImg)

/**
 * 全局依赖注入
 */
import { flag } from '@/utils/download'

Vue.prototype.flag = flag
import { parseTime, resetForm, addDateRange, selectDictLabel, toMap, remove, removeByKey } from '@/utils/costum'

Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.toMap = toMap
Vue.prototype.remove = remove
Vue.prototype.removeByKey = removeByKey
// Vue.prototype.selectDictLabel = selectDictLabel


Vue.prototype.msgSuccess = function(msg) {
  this.$message({ showClose: true, message: msg, type: 'success' })
}

Vue.prototype.msgError = function(msg) {
  this.$message({ showClose: true, message: msg, type: 'error' })
}

import { contactSocket } from '@/utils/websocket.js' // 引入 websocket
// Vue.prototype.$socketPublic = contactSocket()

Vue.prototype.$isNotEmpty = function(obj) {
  return (obj !== undefined && obj !== null && obj !== '' && obj !== 'null')
}

Vue.prototype.$getDefalut = function(obj, key, df) {
  return (obj === undefined || key === undefined || !this.$isNotEmpty(obj[key])) ? df : obj[key]
}

Vue.prototype.$deepCopy = function(obj) {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */

// Vue.use(VueEditor)
Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})
// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  el: '#app', router, store, i18n, render: h => h(App)
})
