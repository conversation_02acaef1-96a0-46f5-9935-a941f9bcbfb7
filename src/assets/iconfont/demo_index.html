<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">

    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>

      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3538338" target="_blank" class="nav-more">查看项目</a>

    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">

            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">iconfont-kefu</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">BBD密码</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">人力社保</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe758;</span>
                <div class="name">部门</div>
                <div class="code-name">&amp;#xe758;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xec7f;</span>
                <div class="name">插入图片</div>
                <div class="code-name">&amp;#xec7f;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">考勤管理</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">身份证</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">位置</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe966;</span>
                <div class="name">24gf-phoneBubble</div>
                <div class="code-name">&amp;#xe966;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">考勤</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">会议</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">加班</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">表格</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xeb66;</span>
                <div class="name">使用文档</div>
                <div class="code-name">&amp;#xeb66;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">多选框</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe751;</span>
                <div class="name">单选</div>
                <div class="code-name">&amp;#xe751;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">出租</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">招聘</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe67d;</span>
                <div class="name">财务</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe887;</span>
                <div class="name">05采购</div>
                <div class="code-name">&amp;#xe887;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe68e;</span>
                <div class="name">住房补贴</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe679;</span>
                <div class="name">我的产品</div>
                <div class="code-name">&amp;#xe679;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">发票管理</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7e9;</span>
                <div class="name">工资</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">住房补贴账户</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">维修</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">员工离职</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">招聘管理</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">财务</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">请假申请</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe722;</span>
                <div class="name">出差</div>
                <div class="code-name">&amp;#xe722;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe67e;</span>
                <div class="name">用餐就餐</div>
                <div class="code-name">&amp;#xe67e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xea00;</span>
                <div class="name">地图组织站点,层级,下级,组织架构布局</div>
                <div class="code-name">&amp;#xea00;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">合同</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6ca;</span>
                <div class="name">补卡</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6c7;</span>
                <div class="name">出差</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe726;</span>
                <div class="name">报销申请-费用报销申请-02</div>
                <div class="code-name">&amp;#xe726;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">11C分组,组织树</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>

          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1658393010708') format('woff2'),
       url('iconfont.woff?t=1658393010708') format('woff'),
       url('iconfont.ttf?t=1658393010708') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">

          <li class="dib">
            <span class="icon iconfont icon-iconfontkefu"></span>
            <div class="name">
              iconfont-kefu
            </div>
            <div class="code-name">.icon-iconfontkefu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-mima"></span>
            <div class="name">
              BBD密码
            </div>
            <div class="code-name">.icon-mima
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-renlishebao"></span>
            <div class="name">
              人力社保
            </div>
            <div class="code-name">.icon-renlishebao
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-bumen"></span>
            <div class="name">
              部门
            </div>
            <div class="code-name">.icon-bumen
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-charutupian"></span>
            <div class="name">
              插入图片
            </div>
            <div class="code-name">.icon-charutupian
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-kaoqinguanli"></span>
            <div class="name">
              考勤管理
            </div>
            <div class="code-name">.icon-kaoqinguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shenfenzheng"></span>
            <div class="name">
              身份证
            </div>
            <div class="code-name">.icon-shenfenzheng
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-weizhi"></span>
            <div class="name">
              位置
            </div>
            <div class="code-name">.icon-weizhi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-24gf-phoneBubble"></span>
            <div class="name">
              24gf-phoneBubble
            </div>
            <div class="code-name">.icon-24gf-phoneBubble
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-kaoqin"></span>
            <div class="name">
              考勤
            </div>
            <div class="code-name">.icon-kaoqin
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-huiyi"></span>
            <div class="name">
              会议
            </div>
            <div class="code-name">.icon-huiyi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-jiaban"></span>
            <div class="name">
              加班
            </div>
            <div class="code-name">.icon-jiaban
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-biaoge"></span>
            <div class="name">
              表格
            </div>
            <div class="code-name">.icon-biaoge
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shiyongwendang"></span>
            <div class="name">
              使用文档
            </div>
            <div class="code-name">.icon-shiyongwendang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-duoxuankuang"></span>
            <div class="name">
              多选框
            </div>
            <div class="code-name">.icon-duoxuankuang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-danxuan"></span>
            <div class="name">
              单选
            </div>
            <div class="code-name">.icon-danxuan
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-chuzu"></span>
            <div class="name">
              出租
            </div>
            <div class="code-name">.icon-chuzu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zhaopin"></span>
            <div class="name">
              招聘
            </div>
            <div class="code-name">.icon-zhaopin
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-caiwu"></span>
            <div class="name">
              财务
            </div>
            <div class="code-name">.icon-caiwu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-caigou"></span>
            <div class="name">
              05采购
            </div>
            <div class="code-name">.icon-caigou
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zhufangbutie"></span>
            <div class="name">
              住房补贴
            </div>
            <div class="code-name">.icon-zhufangbutie
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-wodechanpin"></span>
            <div class="name">
              我的产品
            </div>
            <div class="code-name">.icon-wodechanpin
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-fapiaoguanli"></span>
            <div class="name">
              发票管理
            </div>
            <div class="code-name">.icon-fapiaoguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gongzi"></span>
            <div class="name">
              工资
            </div>
            <div class="code-name">.icon-gongzi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zhufangbutiezhanghu"></span>
            <div class="name">
              住房补贴账户
            </div>
            <div class="code-name">.icon-zhufangbutiezhanghu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-weixiu"></span>
            <div class="name">
              维修
            </div>
            <div class="code-name">.icon-weixiu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-yuangonglizhi"></span>
            <div class="name">
              员工离职
            </div>
            <div class="code-name">.icon-yuangonglizhi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-zhaopinguanli"></span>
            <div class="name">
              招聘管理
            </div>
            <div class="code-name">.icon-zhaopinguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-caiwu1"></span>
            <div class="name">
              财务
            </div>
            <div class="code-name">.icon-caiwu1
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-qingjiashenqing"></span>
            <div class="name">
              请假申请
            </div>
            <div class="code-name">.icon-qingjiashenqing
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-ziyuan207"></span>
            <div class="name">
              出差
            </div>
            <div class="code-name">.icon-ziyuan207
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-yongcanjiucan"></span>
            <div class="name">
              用餐就餐
            </div>
            <div class="code-name">.icon-yongcanjiucan
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-map-site"></span>
            <div class="name">
              地图组织站点,层级,下级,组织架构布局
            </div>
            <div class="code-name">.icon-map-site
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-hetong"></span>
            <div class="name">
              合同
            </div>
            <div class="code-name">.icon-hetong
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-buka"></span>
            <div class="name">
              补卡
            </div>
            <div class="code-name">.icon-buka
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-chucha"></span>
            <div class="name">
              出差
            </div>
            <div class="code-name">.icon-chucha
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-baoxiaoshenqing-feiyongbaoxiaoshenqing-02"></span>
            <div class="name">
              报销申请-费用报销申请-02
            </div>
            <div class="code-name">.icon-baoxiaoshenqing-feiyongbaoxiaoshenqing-02
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-a-11Cfenzuzuzhishu"></span>
            <div class="name">
              11C分组,组织树
            </div>
            <div class="code-name">.icon-a-11Cfenzuzuzhishu
            </div>
          </li>

        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-iconfontkefu"></use>
                </svg>
                <div class="name">iconfont-kefu</div>
                <div class="code-name">#icon-iconfontkefu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mima"></use>
                </svg>
                <div class="name">BBD密码</div>
                <div class="code-name">#icon-mima</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renlishebao"></use>
                </svg>
                <div class="name">人力社保</div>
                <div class="code-name">#icon-renlishebao</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bumen"></use>
                </svg>
                <div class="name">部门</div>
                <div class="code-name">#icon-bumen</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-charutupian"></use>
                </svg>
                <div class="name">插入图片</div>
                <div class="code-name">#icon-charutupian</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaoqinguanli"></use>
                </svg>
                <div class="name">考勤管理</div>
                <div class="code-name">#icon-kaoqinguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenfenzheng"></use>
                </svg>
                <div class="name">身份证</div>
                <div class="code-name">#icon-shenfenzheng</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weizhi"></use>
                </svg>
                <div class="name">位置</div>
                <div class="code-name">#icon-weizhi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-24gf-phoneBubble"></use>
                </svg>
                <div class="name">24gf-phoneBubble</div>
                <div class="code-name">#icon-24gf-phoneBubble</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaoqin"></use>
                </svg>
                <div class="name">考勤</div>
                <div class="code-name">#icon-kaoqin</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huiyi"></use>
                </svg>
                <div class="name">会议</div>
                <div class="code-name">#icon-huiyi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaban"></use>
                </svg>
                <div class="name">加班</div>
                <div class="code-name">#icon-jiaban</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoge"></use>
                </svg>
                <div class="name">表格</div>
                <div class="code-name">#icon-biaoge</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shiyongwendang"></use>
                </svg>
                <div class="name">使用文档</div>
                <div class="code-name">#icon-shiyongwendang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duoxuankuang"></use>
                </svg>
                <div class="name">多选框</div>
                <div class="code-name">#icon-duoxuankuang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-danxuan"></use>
                </svg>
                <div class="name">单选</div>
                <div class="code-name">#icon-danxuan</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuzu"></use>
                </svg>
                <div class="name">出租</div>
                <div class="code-name">#icon-chuzu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhaopin"></use>
                </svg>
                <div class="name">招聘</div>
                <div class="code-name">#icon-zhaopin</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caiwu"></use>
                </svg>
                <div class="name">财务</div>
                <div class="code-name">#icon-caiwu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caigou"></use>
                </svg>
                <div class="name">05采购</div>
                <div class="code-name">#icon-caigou</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhufangbutie"></use>
                </svg>
                <div class="name">住房补贴</div>
                <div class="code-name">#icon-zhufangbutie</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wodechanpin"></use>
                </svg>
                <div class="name">我的产品</div>
                <div class="code-name">#icon-wodechanpin</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fapiaoguanli"></use>
                </svg>
                <div class="name">发票管理</div>
                <div class="code-name">#icon-fapiaoguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzi"></use>
                </svg>
                <div class="name">工资</div>
                <div class="code-name">#icon-gongzi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhufangbutiezhanghu"></use>
                </svg>
                <div class="name">住房补贴账户</div>
                <div class="code-name">#icon-zhufangbutiezhanghu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixiu"></use>
                </svg>
                <div class="name">维修</div>
                <div class="code-name">#icon-weixiu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuangonglizhi"></use>
                </svg>
                <div class="name">员工离职</div>
                <div class="code-name">#icon-yuangonglizhi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhaopinguanli"></use>
                </svg>
                <div class="name">招聘管理</div>
                <div class="code-name">#icon-zhaopinguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caiwu1"></use>
                </svg>
                <div class="name">财务</div>
                <div class="code-name">#icon-caiwu1</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingjiashenqing"></use>
                </svg>
                <div class="name">请假申请</div>
                <div class="code-name">#icon-qingjiashenqing</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuan207"></use>
                </svg>
                <div class="name">出差</div>
                <div class="code-name">#icon-ziyuan207</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yongcanjiucan"></use>
                </svg>
                <div class="name">用餐就餐</div>
                <div class="code-name">#icon-yongcanjiucan</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-map-site"></use>
                </svg>
                <div class="name">地图组织站点,层级,下级,组织架构布局</div>
                <div class="code-name">#icon-map-site</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hetong"></use>
                </svg>
                <div class="name">合同</div>
                <div class="code-name">#icon-hetong</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-buka"></use>
                </svg>
                <div class="name">补卡</div>
                <div class="code-name">#icon-buka</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chucha"></use>
                </svg>
                <div class="name">出差</div>
                <div class="code-name">#icon-chucha</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baoxiaoshenqing-feiyongbaoxiaoshenqing-02"></use>
                </svg>
                <div class="name">报销申请-费用报销申请-02</div>
                <div class="code-name">#icon-baoxiaoshenqing-feiyongbaoxiaoshenqing-02</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-11Cfenzuzuzhishu"></use>
                </svg>
                <div class="name">11C分组,组织树</div>
                <div class="code-name">#icon-a-11Cfenzuzuzhishu</div>
            </li>

          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
