import request from '@/utils/request'

export function companyAdminList(query) {
  return request({
    url: '/admin/companyAdminList',
    method: 'get',
    params: query
  })
}
// 新增管理员
export function adminAdd(data) {
  return request({
    url: '/admin/companyAdminAdd',
    method: 'post',
    data
  })
}
// 新增用户
export function companyUserAdd(data) {
  return request({
    url: '/admin/companyUserAdd',
    method: 'post',
    data
  })
}

// 获取公司人员列表
export function getCompanyUsers(query) {
  return request({
    url: '/admin/getCompanyUsers',
    method: 'get',
    params: query
  })
}

export function adminEdit(data) {
  return request({
    url: '/admin/companyAdminUp',
    method: 'post',
    data
  })
}

export function adminBan(query) {
  return request({
    url: '/admin/companyAdminList',
    method: 'get',
    params: query
  })
}

// 获取所有成员列表
export function companyUserList(query) {
  return request({
    url: '/admin/companyUserList',
    method: 'get',
    params: query
  })
}
// 禁用公司管理员
export function adminDisable(data) {
  return request({
    url: '/admin/adminDisable',
    method: 'post',
    data
  })
}
// 禁用公司用户
export function userDisabled(data) {
  return request({
    url: '/admin/userDisabled',
    method: 'post',
    data
  })
}
// 编辑公司用户
export function companyUserUp(data) {
  return request({
    url: '/admin/companyUserUp',
    method: 'post',
    data
  })
}
export function companyUserDetail(params) {
  return request({
    url: '/admin/companyUserDetail',
    method: 'get',
    params
  })
}
// 获取面试人员列表
export function intervieweesList(data) {
  return request({
    url: '/admin/intervieweesList',
    method: 'post',
    data
  })
}
// 公司岗位

export function companyPositionList(query) {
  return request({
    url: '/admin/companyPositionList',
    method: 'get',
    params: query
  })
}

// 获取公司部门
export function getCompanyDept(query) {
  return request({
    url: '/admin/getCompanyDept',
    method: 'get',
    params: query
  })
}
// 获取角色列表
export function getCompanyRole(query) {
  return request({
    url: '/admin/getCompanyRole',
    method: 'get',
    params: query
  })
}
// 重置用户密码listRole
export function resetUserPassword(data) {
  return request({
    url: '/admin/resetUserPassword',
    method: 'post',
    data
  })
}

// 编辑面试人员
export function intervieweesUp(data) {
  return request({
    url: '/admin/intervieweesUp',
    method: 'post',
    data
  })
}

// 提交excelimportUser
export function importUser(data) {
  return request({
    url: '/admin/importUser',
    method: 'post',
    data
  })
}
// 获取用户日志 
export function getUserLog(query) {
  return request({
    url: '/admin/getUserLog',
    method: 'get',
    params: query
  })
}

