import request from '@/utils/request'
// 获取报销列表
export function getReimbursementList(query) {
  return request({
    url: '/admin/ExpensesList',
    method: 'get',
    params: query
  })
}
// 获取报销类型
export function getReimbursementType(query) {
  return request({
    url: '/admin/getInformalCategory',
    method: 'get',
    params: query
  })
}

// 审核报销
export function checkInformal(data) {
  return request({
    url: '/admin/checkInformal',
    method: 'post',
    data
  })
}

// 新增报销类型
export function addInformalCategory(data) {
  return request({
    url: '/admin/addInformalCategory',
    method: 'post',
    data
  })
}

// 编辑报销类型
export function editInformalCategory(data) {
  return request({
    url: '/admin/editInformalCategory',
    method: 'post',
    data
  })
}

// 删除报销类型
export function delInformalCategory(data) {
  return request({
    url: '/admin/delInformalCategory',
    method: 'post',
    data
  })
}

// 费用申请审核
export function costApplyAudit(data) {
  return request({
    url: '/admin/costApplyAudit',
    method: 'post',
    data
  })
}

// 获取费用申请列表
export function costApplyList(query) {
  return request({
    url: '/admin/costApplyList',
    method: 'get',
    params: query
  })
}

// 新增发票
export function invoiceAdd(data) {
  return request({
    url: '/admin/invoiceAdd',
    method: 'post',
    data
  })
}

// 编辑发票
export function invoiceEdit(data) {
  return request({
    url: '/admin/invoiceEdit',
    method: 'post',
    data
  })
}

// 发票列表
export function invoiceList(query) {
  return request({
    url: '/admin/invoiceList',
    method: 'get',
    params: query
  })
}

// 付款类型
export function paymentList(query) {
  return request({
    url: '/admin/paymentList',
    method: 'get',
    params: query
  })
}

// 新增付款类型
export function addPayment(data) {
  return request({
    url: '/admin/addPayment',
    method: 'post',
    data
  })
}

// 修改付款类型
export function editPayment(data) {
  return request({
    url: '/admin/editPayment',
    method: 'post',
    data
  })
}

// 删除付款类型
export function delPayment(data) {
  return request({
    url: '/admin/delPayment',
    method: 'post',
    data
  })
}

// 新增费用申请
export function costApplyAdd(data) {
  return request({
    url: '/admin/costApplyAdd',
    method: 'post',
    data
  })
}
// 下载图片
export function downloadedImg(query) {
  return request({
    url: '/admin/downloadedImg',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 上传付款凭证
export function paymentVoucher(data) {
  return request({
    url: '/admin/paymentVoucher',
    method: 'post',
    data
  })
}
