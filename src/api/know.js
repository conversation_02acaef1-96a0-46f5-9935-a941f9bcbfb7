import request from '@/utils/request'
// 知识分享数据
export function knowledgeSend(query) {
    return request({
        url: '/admin/knowledgeSend',
        method: 'get',
        params: query
    })
}
// 新增知识分享数据列表
export function addKnowledgeList(data) {
    return request({
        url: '/admin/addKnowledgeList',
        method: 'post',
        data
    })
}
// 编辑知识分享数据列表  
export function editKnowledgeList(data) {
    return request({
        url: '/admin/editKnowledgeList',
        method: 'post',
        data
    })
}

// 删除知识分享数据列表 
export function delKnowledgeList(query) {
    return request({
        url: '/admin/delKnowledgeList',
        method: 'get',
        params: query
    })
}
// 知识分享数据列表 
export function knowledgeList(query) {
    return request({
        url: '/admin/knowledgeList',
        method: 'get',
        params: query
    })
}

// 新增知识分类
export function addKnowledgeCate(data) {
    return request({
        url: '/admin/addKnowledgeCate',
        method: 'post',
        data
    })
}
// 编辑知识分类 
export function editKnowledgeCate(data) {
    return request({
        url: '/admin/editKnowledgeCate',
        method: 'post',
        data
    })
}
// 删除知识分类
export function delKnowledgeCate(query) {
    return request({
        url: '/admin/delKnowledgeCate',
        method: 'get',
        params: query
    })
}
// 知识分享类表
export function listKnowledgeCate(query) {
    return request({
        url: '/admin/listKnowledgeCate',
        method: 'get',
        params: query
    })
}