import request from '@/utils/request'


//  获取考勤组
export function getAttGroup(query) {
  return request({
    url: '/admin/attendance/getAttGroup',
    method: 'get',
    params: query
  })
}

// 添加考勤组
export function createAttGroup(data) {
  return request({
    url: '/admin/attendance/createAttGroup',
    method: 'post',
    data
  })
}

// 获取详情
export function getAtteGroupInfo(query) {
  return request({
    url: '/admin/attendance/getAtteGroupInfo',
    method: 'get',
    params: query
  })
}

// 编辑考勤组
export function editAtteGroup(data) {
  return request({
    url: '/admin/attendance/editAtteGroup',
    method: 'post',
    data
  })
}

// 删除考勤组
export function delAtteGroup(query) {
  return request({
    url: '/admin/attendance/delAtteGroup',
    method: 'get',
    params: query
  })
}





























// 考勤列表
export function attendanceList(data) {
  return request({
    url: '/admin/attendance/attendanceList', method: 'post', data
  })
}

// 补卡审核
export function reissueCardActive(data) {
  return request({
    url: '/admin/attendance/reissueCardActive', method: 'post', data
  })
}

// 请假审核
export function leaveActive(data) {
  return request({
    url: '/admin/attendance/leaveActive', method: 'post', data
  })
}

// 补卡列表
export function reissueCardList(data) {
  return request({
    url: '/admin/attendance/reissueCardList', method: 'post', data
  })
}

// 请假列表
export function leaveList(data) {
  return request({
    url: '/admin/leaveList', method: 'post', data
  })
}

// 添加请假类型
export function leaveTypeAdd(data) {
  return request({
    url: '/admin/leaveTypeAdd', method: 'post', data
  })
}

// 获取请假类型
export function leaveTypeList(query) {
  return request({
    url: '/admin/leaveTypeList', method: 'get', query
  })
}

// 修改请假类型
export function leaveTypeUp(data) {
  return request({
    url: '/admin/leaveTypeUp', method: 'post', data
  })
}

// 删除请假类型
export function leaveTypeDel(data) {
  return request({
    url: '/admin/leaveTypeDel', method: 'post', data
  })
}

// 修改考勤设置
export function attendanceSet(data) {
  return request({
    url: '/admin/attendance/attendanceSet', method: 'post', data
  })
}

// 查询考勤设置
export function attendanceSysInfo(query) {
  return request({
    url: '/admin/attendance/attendanceSysInfo', method: 'get', params: query
  })
}

// 删除考勤设置
export function attendanceSysDel(data) {
  return request({
    url: '/admin/attendance/attendanceSysDel', method: 'post', data
  })
}

// 添加考勤设置
export function attendanceSysAdd(data) {
  return request({
    url: '/admin/attendance/attendanceSysAdd', method: 'post', data
  })
}

// 公司机构数树
export function resultingtree(query) {
  return request({
    url: '/admin/organization', method: 'get', params: query
  })
}

// 考勤统计
export function attendanceStatistical(data) {
  return request({
    url: 'admin/attendance/attendanceStatistical', method: 'post', data
  })
}
// 获取考勤设置
export function getAttRole(query) {
  return request({
    url: '/admin/attendance/getAttRole', method: 'get', params: query
  })
}
// 设置考勤规则
export function setAttRole(data) {
  return request({
    url: 'admin/attendance/setAttRole', method: 'post', data
  })
}
// 删除多条规则
export function delAttRole(query) {
  return request({
    url: '/admin/attendance/delAttRole', method: 'get', params: query
  })
}




// 获取外勤打卡
export function attGroupList(query) {
  return request({
    url: '/admin/attendance/attGroupList',
    method: 'get',
    params: query
  })
}

// 添加外勤打卡
export function attGroupAdd(data) {
  return request({
    url: '/admin/attendance/attGroupAdd',
    method: 'post',
    data
  })
}

// 编辑外勤打卡
export function attGroupEdit(data) {
  return request({
    url: '/admin/attendance/attGroupEdit',
    method: 'post',
    data
  })
}

// 删除外勤打卡
export function attGroupDel(data) {
  return request({
    url: '/admin/attendance/attGroupDel',
    method: 'post',
    data
  })
}

// 关闭外勤打卡
export function attGroupOff(data) {
  return request({
    url: '/admin/attendance/attGroupOff',
    method: 'post',
    data
  })
}






