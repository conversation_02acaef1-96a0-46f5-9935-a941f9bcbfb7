import request from '@/utils/request'
// 获取性别数量
export function searchCompany(query) {
    return request({
      url: '/admin/sexRatio',
      method: 'get',
      params: query
    })
  }

// 年龄占比
export function ageRatio(query) {
  return request({
    url: '/admin/ageRatio',
    method: 'get',
    params: query
  })
}


// 岗位占比
export function positionRatio(query) {
  return request({
    url: 'admin/positionRatio',
    method: 'get',
    params: query
  })
}


// 岗位占比
export function educationRatio(query) {
  return request({
    url: 'admin/educationRatio',
    method: 'get',
    params: query
  })
}
