import request from '@/utils/request'

export function labelList(query) {
  return request({
    url: '/admin/labelList',
    method: 'get',
    params: query
  })
}
// 添加标签
export function addLabel(data) {
  return request({
    url: '/admin/addLabel',
    method: 'post',
    data
  })
}

// 编辑标签
export function upLabel(data) {
  return request({
    url: '/admin/upLabel',
    method: 'post',
    data
  })
}
// 获取标签分类列表
export function getCategoryList(query) {
  return request({
    url: '/admin/getCategoryList',
    method: 'get',
    params: query
  })
}

// 通过类型获取对应标签
export function categoryLabelList(data) {
  return request({
    url: 'admin/categoryLabelList', method: 'post', data
  })
}
// 每个标签对应的数据
export function getLabelData(data) {
  return request({
    url: 'admin/getLabelData', method: 'post', data
  })
}
// 获取标签对应规则、表头、
export function getTaskLabel(data) {
  return request({
    url: '/admin/getTaskLabel',
    method: 'post',
    data
  })
}

