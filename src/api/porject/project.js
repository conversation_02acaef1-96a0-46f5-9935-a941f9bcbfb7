import request from '@/utils/request'

// 获取项目列表
export function getproList(query) {
  return request({
    url: '/admin/projectList',
    method: 'get',
    params: query
  })
}
// 新增项目
export function projectAdd(data) {
  return request({
    url: '/admin/projectAdd',
    method: 'post',
    data
  })
}

// 编辑项目
export function projectEdit(data) {
  return request({
    url: '/admin/projectEdit',
    method: 'post',
    data
  })
}

// 删除项目
export function projectDel(data) {
  return request({
    url: '/admin/projectDel',
    method: 'post',
    data
  })
}

// 结束项目
export function projectEnd(query) {
  return request({
    url: '/admin/projectEnd',
    method: 'get',
    params: query
  })
}










// 获取公司列表
export function searchCompany(query) {
  return request({
    url: '/admin/companyList',
    method: 'get',
    params: query
  })
}

// 获取公司人员列表
export function getCompanyUsers(query) {
  return request({
    url: '/admin/getCompanyUsers',
    method: 'get',
    params: query
  })
}

// 上传文件
export function imageUpload(data) {
  return request({
    url: '/admin/importPro',
    method: 'post',
    data
  })
}

// 获取所有项目
export function getALLPro(query) {
  return request({
    url: '/admin/getALLPro',
    method: 'get',
    params: query
  })
}


// 获取项目类型
export function ProCategory(query) {
  return request({
    url: '/admin/ProCategory',
    method: 'get',
    params: query
  })
}
// 获取项目类型列表
export function getproTypeList(query) {
  return request({
    url: '/admin/projectTypeList',
    method: 'get',
    params: query
  })
}

// 新增项目类型
export function projectTypeAdd(data) {
  return request({
    url: '/admin/projectTypeAdd',
    method: 'post',
    data
  })
}
// 编辑项目类型
export function projectTypUpdate(data) {
  return request({
    url: '/admin/projectTypUpdate',
    method: 'post',
    data
  })
}
// 删除项目类型
export function projectTypeDel(data) {
  return request({
    url: '/admin/projectTypeDel',
    method: 'post',
    data
  })
}

// 新增任务
export function addTask(data) {
  return request({
    url: '/admin/addTask',
    method: 'post',
    data
  })
}
// 拆分任务
export function dismantleTask(data) {
  return request({
    url: '/admin/dismantleTask',
    method: 'post',
    data
  })
}
// 编辑任务
export function editTask(data) {
  return request({
    url: '/admin/editTask',
    method: 'post',
    data
  })
}
// 删除任务
export function delTask(data) {
  return request({
    url: '/admin/delTask',
    method: 'post',
    data
  })
}
// 获取任务列表
export function fetchList(query) {
  return request({
    url: '/admin/taskList',
    method: 'get',
    params: query
  })
}
// 获取任务类型
export function getTaskCategory(query) {
  return request({
    url: '/admin/TaskCategory',
    method: 'get',
    params: query
  })
}
// 获取任务类型列表
export function taskCategoryList(query) {
  return request({
    url: '/admin/taskCategoryList',
    method: 'get',
    params: query
  })
}
// 新增任务类型
export function taskCategoryAdd(data) {
  return request({
    url: '/admin/taskCategoryAdd',
    method: 'post',
    data
  })
}
// 编辑任务类型
export function taskCategoryUpdate(data) {
  return request({
    url: '/admin/taskCategoryUpdate',
    method: 'post',
    data
  })
}
// 删除任务类型
export function taskCategoryDel(data) {
  return request({
    url: '/admin/taskCategoryDel',
    method: 'post',
    data
  })
}
// 面试人员列表
export function intervieweesList(data) {
  return request({
    url: '/admin/intervieweesList',
    method: 'post',
    data
  })
}
// 审核任务
export function evaluation(data) {
  return request({
    url: '/admin/evaluation',
    method: 'post',
    data
  })
}
// 获取所有客户(没有分页)
export function CustomerAllList(query) {
  return request({
    url: '/admin/CustomerAllList',
    method: 'get',
    params: query
  })
}
// 获取等级
export function getProLevel(query) {
  return request({
    url: '/admin/getProLevel',
    method: 'get',
    params: query
  })
}

// 获取任务等级
export function getTaskLevel(query) {
  return request({
    url: '/admin/getTaskLevel',
    method: 'get',
    params: query
  })
}
// 导入任务
export function importTask(data) {
  return request({
    url: '/admin/importTask',
    method: 'post',
    data
  })
}
// 官网
export function potentialList(query) {
  return request({
    url: '/admin/potentialList',
    method: 'get',
    params: query
  })
}
// 当前项目的所有报销
export function claimexpenseList(query) {
  return request({
    url: '/admin/claimexpenseList',
    method: 'get',
    params: query
  })
}
// 当前项目的每个人参与工时
export function projectWorkDay(query) {
  return request({
    url: '/admin/projectWorkDay',
    method: 'get',
    params: query
  })
}
// 获取当前项目下所有评论
export function proMarkInfo(query) {
  return request({
    url: '/admin/proMarkInfo',
    method: 'get',
    params: query
  })
}
// 获取项目归档利列表
export function projectMergeList(query) {
  return request({
    url: '/admin/projectMergeList',
    method: 'get',
    params: query
  })
}

// 项目合同
export function projectContract(query) {
  return request({
    url: '/admin/projectContract',
    method: 'get',
    params: query
  })
}
// 添加项目归档
export function projectMerge(data) {
  return request({
    url: '/admin/projectMerge',
    method: 'post',
    data
  })
}
// 归档详情
export function mergeInfo(query) {
  return request({
    url: '/admin/mergeInfo',
    method: 'get',
    params: query
  })
}

// 项目评分
export function questionMark(query) {
  return request({
    url: '/admin/questionMark',
    method: 'get',
    params: query
  })
}

// 项目归档题库列表
export function questionList(query) {
  return request({
    url: '/admin/questionList',
    method: 'get',
    params: query
  })
}

// 删除归档题库列表
export function questionDel(query) {
  return request({
    url: '/admin/questionDel',
    method: 'get',
    params: query
  })
}
// 编辑归档题库列表
export function questionEdit(data) {
  return request({
    url: '/admin/questionEdit',
    method: 'post',
    data
  })
}
// 添加归档题库列表
export function questionAdd(data) {
  return request({
    url: '/admin/questionAdd',
    method: 'post',
    data
  })
}
// 最近一个月任务
export function dateTask(data) {
  return request({
    url: '/admin/dateTask',
    method: 'post',
    data
  })
}
// 获取当前项目下所有参与人
export function getProUser(query) {
  return request({
    url: '/admin/getProUser',
    method: 'get',
    params: query
  })
}
// 完成任务
export function taskEnd(data) {
  return request({
    url: '/admin/taskEnd',
    method: 'post',
    data
  })
}
// 获取参考分
export function taskscore(query) {
  return request({
    url: '/admin/taskScore',
    method: 'get',
    params: query
  })
}
