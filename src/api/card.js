import request from '@/utils/request'
// 获取补卡时间
export function lackCard(query) {
  return request({
    url: '/admin/lackCard',
    method: 'get',
    params: query
  })
}
// 费用
export function retroactive(data) {
  return request({
    url: '/admin/retroactive',
    method: 'post',
    data
  })
}
//  请假审核
export function leaveRequest(data) {
  return request({
    url: '/admin/leaveRequest',
    method: 'post',
    data
  })
}

// 报销
export function addInformalCategory(data) {
  return request({
    url: '/admin/addInformalCategory',
    method: 'post',
    data
  })
}
// 上传健康报截图
export function addHealthTreasure(data) {
  return request({
    url: '/admin/addHealthTreasure',
    method: 'post',
    data
  })
}

