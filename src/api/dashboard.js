import request from '@/utils/request'

export function getUserLabel(query) {
  return request({
    url: '/admin/getUserLabel',
    method: 'get',
    params: query
  })
}

// 获取消息列表
export function getMsgRemindList(data) {
  return request({
    url: '/admin/getMsgRemindList',
    method: 'post',
    data
  })
}

export function delMsgOfSelf(data) {
  return request({
    url: '/admin/delMsgOfSelf',
    method: 'post',
    data
  })
}
// 获取首页头像
export function getAdminInfo(query) {
  return request({
    url: '/admin/getAdminInfo',
    method: 'get',
    params: query
  })
}
// 获取首页在职人员数据等
export function getUserNum(data) {
  return request({
    url: '/admin/getUserNum',
    method: 'post',
    data
  })
}

// 获取本周考勤统计可视化图标
// export function getAttendanceList(data) {
//   return request({
//     url: '/api/getAttendanceList',
//     method: 'post',
//     data
//   })
// }
export function getAttendanceList(query) {
  return request({
    url: '/admin/getAttendanceList',
    method: 'get',
    params: query
  })
}
// 获取公司项目数量
export function getCompanyProNum(query) {
  return request({
    url: '/admin/getCompanyProNum',
    method: 'get',
    params: query
  })
}
// 获取用户任务评分
export function getUserTaskGrade(query) {
  return request({
    url: '/admin/getUserTaskGrade',
    method: 'get',
    params: query
  })
}
// 获取任务完成率
export function getUserTaskRate(query) {
  return request({
    url: '/admin/getUserTaskRate',
    method: 'get',
    params: query
  })
}
