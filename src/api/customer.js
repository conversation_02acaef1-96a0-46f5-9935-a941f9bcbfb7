import request from '@/utils/request'

// 新增客户
export function CustomerAdd(data) {
  return request({
    url: '/admin/CustomerAdd',
    method: 'post',
    data
  })
}

// 删除客户
export function CustomerDel(data) {
  return request({
    url: '/admin/CustomerDel',
    method: 'post',
    data
  })
}

// 编辑客户
export function CustomerEdit(data) {
  return request({
    url: '/admin/CustomerEdit',
    method: 'post',
    data
  })
}

// 获取客户列表
export function CustomerList(query) {
  return request({
    url: '/admin/CustomerList',
    method: 'get',
    params: query
  })
}

// 客户等级
export function customerLevel(query) {
  return request({
    url: '/admin/customerLevel',
    method: 'get',
    params: query
  })
}

// 导入客户EXCEl
export function importCustomer(data) {
  return request({
    url: '/admin/importCustomer',
    method: 'post',
    data
  })
}
