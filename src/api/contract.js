import request from '@/utils/request'

// 新增合同
export function contractAdd(data) {
  return request({
    url: '/admin/contractAdd',
    method: 'post',
    data
  })
}

// 编辑合同
export function contractEdit(data) {
  return request({
    url: '/admin/contractEdit',
    method: 'post',
    data
  })
}

// 获取合同列表
export function contractList(query) {
  return request({
    url: '/admin/contractList',
    method: 'get',
    params: query
  })
}

// 获取合同管理付款方式

export function paymentList(query) {
  return request({
    url: '/admin/paymentList',
    method: 'get',
    params: query
  })
}

// 修改合同状态

export function contractCheck(query) {
  return request({
    url: '/admin/contractCheck',
    method: 'get',
    params: query
  })
}
