import request from '@/utils/request'

// 获取公司列表
export function searchCompany(query) {
  return request({
    url: '/admin/companyList',
    method: 'get',
    params: query
  })
}
// 获取公司人员列表
export function getCompanyUsers(query) {
  return request({
    url: '/admin/getCompanyUsers',
    method: 'get',
    params: query
  })
}
// 上传文件
export function imageUpload(data) {
  return request({
    url: '/admin/importPro',
    method: 'post',
    data
  })
}
// 获取所有项目
export function getALLPro(query) {
  return request({
    url: '/admin/getALLPro',
    method: 'get',
    params: query
  })
}
// 获取项目列表
export function getproList(query) {
  return request({
    url: '/admin/projectList',
    method: 'get',
    params: query
  })
}
// 获取项目类型列表
export function getproTypeList(query) {
  return request({
    url: '/admin/projectTypeList',
    method: 'get',
    params: query
  })
}

// 新增项目类型
export function projectTypeAdd(data) {
  return request({
    url: '/admin/projectTypeAdd',
    method: 'post',
    data
  })
}
// 编辑项目类型
export function projectTypUpdate(data) {
  return request({
    url: '/admin/projectTypUpdate',
    method: 'post',
    data
  })
}
// 删除项目类型
export function projectTypeDel(data) {
  return request({
    url: '/admin/projectTypeDel',
    method: 'post',
    data
  })
}




// 删除归档题库列表
export function questionDel(query) {
  return request({
    url: '/admin/questionDel',
    method: 'get',
    params: query
  })
}
// 编辑归档题库列表 
export function questionEdit(data) {
  return request({
    url: '/admin/questionEdit',
    method: 'post',
    data
  })
}
// 添加归档题库列表 
export function questionAdd(data) {
  return request({
    url: '/admin/questionAdd',
    method: 'post',
    data
  })
}

// 最近一个月任务 
export function dateTask(data) {
  return request({
    url: '/admin/dateTask',
    method: 'post',
    data
  })
}