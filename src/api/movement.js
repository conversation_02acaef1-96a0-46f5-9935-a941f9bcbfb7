import request from '@/utils/request'

//  标签语言包列表
export function labelMsgList(query) {
    return request({
        url: '/admin/labelMsgList',
        method: 'get',
        params: query
    })
}
//  删除标签语言包
export function labelMsgDel(query) {
    return request({
        url: '/admin/labelMsgDel',
        method: 'get',
        params: query
    })
}
// 新增标签语言包
export function labelMsgAdd(data) {
    return request({
        url: '/admin/labelMsgAdd',
        method: 'post',
        data
    })
}
// 编辑标签语言包列表 
export function labelMsgEdit(data) {
    return request({
        url: '/admin/labelMsgEdit',
        method: 'post',
        data
    })
}
//  标签动作列表
export function labelActionList(query) {
    return request({
        url: '/admin/labelActionList',
        method: 'get',
        params: query
    })
}
//  删除标签动作
export function labelActionDEl(query) {
    return request({
        url: '/admin/labelActionDEl',
        method: 'get',
        params: query
    })
}
// 新增标签动作
export function labelActionAdd(data) {
    return request({
        url: '/admin/labelActionAdd',
        method: 'post',
        data
    })
}
// 编辑标签动作 
export function labelActionEdit(data) {
    return request({
        url: '/admin/labelActionEdit',
        method: 'post',
        data
    })
}