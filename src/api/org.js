import request from '@/utils/request'


// 查询组织架构树
export function getOrgTree(param) {
  return request({
    url: 'admin/workflow/organizationTree',
    method: 'get',
    params: param
  })
}


// 查询系统角色
export function getRole() {
  return request({
    url: 'oa/org/role',
    method: 'get'
  })
}

// 搜索人员
export function getUserByName(param) {
  return request({
    url: 'admin/companyAdminList?page=1&page_size=10&status=1',
    method: 'get',
    params: param
  })
}

export default {
  getOrgTree, getUserByName, getRole
}
