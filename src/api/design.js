import request from '@/utils/request'

// 查询表单组
export function getFormGroups(param) {
  return request({
    url: 'admin/workflow/getFormGroups', method: 'get', params: param
  })
}

// 新增表单组
export function creatFormGroups(param) {
  return request({
    url: 'admin/workflow/creatFormGroups', method: 'get', params: param
  })
}

// 编辑表单组
export function editFormGroups(param) {
  return request({
    url: 'admin/workflow/editFormGroups', method: 'get', params: param
  })
}

// 删除表单
export function delForm(param) {
  return request({
    url: 'admin/workflow/delForm', method: 'get', params: param
  })
}

// 查询表单
export function getFormItems(param) {
  return request({
    url: 'admin/workflow/getFormItems', method: 'get', params: param
  })
}

// 查询表单详情
export function getFormDetail(param) {
  return request({
    url: 'admin/workflow/getFormDetail', method: 'get', params: param
  })
}

// 创建表单
export function createForm(param) {
  return request({
    url: 'admin/workflow/createForm', method: 'post', data: param
  })
}

// 删除表单组
export function delFormGroups(param) {
  return request({
    url: 'admin/workflow/delFormGroups', method: 'get', params: param
  })
}

// 更新表单
export function updateForm(param) {
  return request({
    url: 'admin/workflow/updateForm', method: 'post', data: param
  })
}

// 创建流程
export function workflowRunStart(param) {
  return request({
    url: 'admin/workflow/workflowRunStart', method: 'post', data: param
  })
}

// 获取下一个步骤审批人
export function getNextUser(param) {
  return request({
    url: 'admin/workflow/getNextUser', method: 'post', data: param
  })
}

// 转交下一步
export function nextUser(param) {
  return request({
    url: 'admin/workflow/nextUser', method: 'post', data: param
  })
}

// 获取工作流列表
export function workflowRunList(query) {
  return request({
    url: 'admin/workflow/workflowRunList', method: 'get', params: query
  })
}

// 收藏流程
export function workflowCollect(query) {
  return request({
    url: 'admin/workflow/workflowCollect', method: 'get', params: query
  })
}

// 收藏流程
export function workflowEnd(param) {
  return request({
    url: 'admin/workflow/workflowEnd', method: 'post', data: param
  })
}

//  流程取消
export function workflowCancel(query) {
  return request({
    url: 'admin/workflow/workflowCancel', method: 'get', params: query
  })
}

//  流程取消
export function getFlowDiscuss(query) {
  return request({
    url: 'admin/workflow/getFlowDiscuss', method: 'get', params: query
  })
}

//  获取退回节点
export function getRollbackNode(query) {
  return request({
    url: 'admin/workflow/getRollbackNode', method: 'get', params: query
  })
}

// 退回到指定节点
export function rollbackNode(query) {
  return request({
    url: 'admin/workflow/rollbackNode', method: 'get', params: query
  })
}





