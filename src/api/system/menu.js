import request from '@/utils/request'

// 查询菜单列表
export function getMenuList(query) {
  return request({
    url: '/admin/getMenuList',
    method: 'get',
    params: query
  })
}

// 查询菜单下拉树结构
export function listMenu() {
  return request({
    url: '/admin/getMenuList',
    method: 'get'
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/admin/addMenuList',
    method: 'post',
    data
  })
}

// 根据角色ID查询菜单下拉树结构
export function getMenu(id) {
  const query = { 'id': id }
  return request({
    url: '/admin/getMenuList',
    method: 'get',
    params: query
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/admin/upMenuList',
    method: 'post',
    data
  })
}

// 删除菜单
export function delMenu(data) {
  return request({
    url: '/admin/delMenuList',
    method: 'post',
    data
  })
}

