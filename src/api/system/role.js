import request from '@/utils/request'

// 查询角色列表
export function getCompanyRole(query) {
  return request({
    url: '/admin/getCompanyRole',
    method: 'get',
    params: query
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/admin/addCompanyRole',
    method: 'post',
    data: data
  })
}

// 删除角色
export function delRole(data) {
  return request({
    url: '/admin/delCompanyRole',
    method: 'post',
    data
  })
}

// 查询角色详细
export function getRole(query) {
  return request({
    url: '/admin/getCompanyRole',
    method: 'get',
    params: query
  })
}

// 修改角色
export function upCompanyRole(data) {
  return request({
    url: '/admin/upCompanyRole',
    method: 'post',
    data: data
  })
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: '/api/v1/roledatascope',
    method: 'put',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/api/v1/role',
    method: 'put',
    data: data
  })
}

export function getListrole(id) {
  return request({
    url: '/api/v1/menu/role/' + id,
    method: 'get'
  })
}

export function getRoutes() {
  return request({
    url: '/api/v1/menurole',
    method: 'get'
  })
}

export function getMenuNames() {
  return request({
    url: '/api/v1/menuids',
    method: 'get'
  })
}
