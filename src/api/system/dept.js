import request from '@/utils/request'

export function getCompanyDept(query) {
  return request({
    url: '/admin/getCompanyDept',
    method: 'get',
    params: query
  })
}

export function getDeptList(query) {
  return request({
    url: '/admin/getCompanyDept',
    method: 'get',
    params: query
  })
}


// 新增部门
export function addCompanyDept(data) {
  return request({
    url: '/admin/addCompanyDept',
    method: 'post',
    data: data
  })
}

// 修改部门
export function upCompanyDept(data) {
  return request({
    url: '/admin/upCompanyDept',
    method: 'post',
    data
  })
}

// 删除部门
export function delCompanyDept(data) {
  return request({
    url: '/admin/delCompanyDept',
    method: 'post',
    data
  })
}
