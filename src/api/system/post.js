import request from '@/utils/request'

// 查询岗位列表
export function companyPositionList(query) {
  return request({
    url: '/admin/companyPositionList',
    method: 'get',
    params: query
  })
}

// 新增岗位
export function companyPositionAdd(data) {
  return request({
    url: '/admin/companyPositionAdd',
    method: 'post',
    data
  })
}

// 修改岗位
export function companyPositionUp(data) {
  return request({
    url: '/admin/companyPositionUp',
    method: 'post',
    data
  })
}

// 删除岗位
export function companyPositionDel(data) {
  return request({
    url: '/admin/companyPositionDel',
    method: 'post',
    data
  })
}

