import request from '@/utils/request'

// 文件上传
export function fileUpload(data) {
  return request({
    url: '/admin/saveFile',
    method: 'post',
    data
  })
}


// 登陆
export function login(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getInfo(token) {
  return request({
    url: '/admin/getAdminInfo',
    method: 'get'
  })
}


// 登出 暂时没用
export function logout() {
  return request({
    url: '/vue-element-admin/user/logout',
    method: 'post'
  })
}


// 修改用户密码
export function changePassword(data) {
  return request({
    url: '/admin/changePassword',
    method: 'post',
    data
  })
}
