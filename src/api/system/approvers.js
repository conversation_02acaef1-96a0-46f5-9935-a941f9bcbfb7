    getApproversModel
import request from '@/utils/request'

 // list
export function getApprovers(query) {
  return request({
    url: '/admin/approvers/getApprovers',
    method: 'get',
    params: query
  })
}

 // add
export function creatApprovers(data) {
  return request({
    url: '/admin/approvers/creatApprovers',
    method: 'post',
    data
  })
}

// edit
export function editApprovers(data) {
  return request({
    url: '/admin/approvers/editApprovers',
    method: 'post',
    data
  })
}

// del
export function delApprovers(query) {
  return request({
    url: '/admin/approvers/delApprovers',
    method: 'get',
    params: query
  })
}

// get
export function getApproversModel(query) {
  return request({
    url: '/admin/approvers/getApproversModel',
    method: 'get',
    params: query
  })
}
